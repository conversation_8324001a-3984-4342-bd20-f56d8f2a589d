# 银行转账待确认通知 - 实际数据样式

## 📧 发送给普通销售 (<EMAIL>)

**您有 `3` 个客户的银行转账待确认，请及时确认**
>
>
>[12345](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=12345)
>
>[67890](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=67890)
>
>[11111](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=11111)

---

## 📧 发送给销售运营 (<EMAIL>)

**您有 `1` 个客户的银行转账待确认，请及时确认**
>
>
>[44444](https://portal.qiniu.io/bo/financial/bank-transfer/admin?status=2&uid=44444)

---

## 🔍 实际代码逻辑

基于 `app/job/bank_transfer_pending_confirm_notify.go` 第 135-141 行的代码：

```go
content := fmt.Sprintf(workwxTemplate, len(users))
for _, uid := range users {
    content += fmt.Sprintf(`
>
>[%d](%s&uid=%d)
`, uid, link, uid)
}
```

## 📋 消息结构分析

1. **模板部分**:
   ```
   **您有 `N` 个客户的银行转账待确认，请及时确认**
   >
   ```

2. **用户链接部分** (每个用户):
   ```
   >
   >[用户ID](链接&uid=用户ID)
   ```

3. **完整结构**:
   - 粗体标题 + 反引号包围的数字
   - 引用块开始符 `>`
   - 空行
   - 每个用户前有一个空的引用行 `>`
   - 用户链接行 `>[用户ID](链接)`

## 🔗 链接差异

- **普通销售**: `https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=用户ID`
- **销售运营**: `https://portal.qiniu.io/bo/financial/bank-transfer/admin?status=2&uid=用户ID`

## 📱 企业微信渲染效果

在企业微信中，这个 Markdown 会被渲染为：
- **粗体标题**，数字用代码样式显示
- 引用块样式的内容区域
- 每个用户ID都是可点击的超链接
- 点击链接会跳转到对应的银行转账确认页面
