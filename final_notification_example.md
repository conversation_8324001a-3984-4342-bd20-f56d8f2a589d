# 银行转账待确认通知 - 实际数据样式

## 📧 发送给普通销售 (<EMAIL>)

**您有 `3` 个客户的银行转账待确认，请及时确认**
>
>[12345](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=12345)
>[67890](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=67890)
>[11111](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=11111)

---

## 📧 发送给销售运营 (<EMAIL>)

**您有 `1` 个客户的银行转账待确认，请及时确认**
>
>[44444](https://portal.qiniu.io/bo/financial/bank-transfer/admin?status=2&uid=44444)

---

## 🔍 格式说明

1. **标题部分**: 使用 Markdown 粗体格式，显示待确认的客户数量
2. **用户链接**: 每个用户ID都是一个可点击的链接
3. **链接差异**: 
   - 普通销售: `/bo/financial/bank-transfer/confirmation?status=2&uid=用户ID`
   - 销售运营: `/bo/financial/bank-transfer/admin?status=2&uid=用户ID`
4. **Markdown格式**: 支持企业微信的 Markdown 渲染

## ⚠️ 原始代码问题

原始代码中的这段逻辑有问题：

```go
content := fmt.Sprintf(workwxTemplate, len(users))
for _, uid := range users {
    content = fmt.Sprintf(content, `
>[%d](%s&uid=%d)
`, uid, link, uid)
}
```

**问题**: `content` 已经被格式化过，不应该再次作为格式化模板使用。

## ✅ 修复建议

```go
content := fmt.Sprintf(workwxTemplate, len(users))
for _, uid := range users {
    userLink := fmt.Sprintf(`
>[%d](%s&uid=%d)`, uid, link, uid)
    content += userLink
}
```

**修复**: 使用字符串拼接 (`+=`) 而不是重复格式化。
