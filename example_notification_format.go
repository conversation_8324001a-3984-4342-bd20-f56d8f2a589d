package main

import (
	"fmt"
)

// 模拟 pageLink 函数
func pageLink(email string) string {
	salesPageLink := "https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2"
	salesOperationPageLink := "https://portal.qiniu.io/bo/financial/bank-transfer/admin?status=2"
	
	if email == "<EMAIL>" {
		return salesOperationPageLink
	}
	return salesPageLink
}

func main() {
	// 模拟数据：notifiers 是一个 map，key 是邮箱，value 是用户ID列表
	notifiers := map[string][]uint64{
		"<EMAIL>": {12345, 67890, 11111},
		"<EMAIL>":   {22222, 33333},
		"<EMAIL>": {44444},
	}

	// 原始的拼接逻辑（有问题的版本）
	const workwxTemplate = `**您有 ` + "`%d`" + ` 个客户的银行转账待确认，请及时确认**
>
`

	fmt.Println("=== 原始代码的问题演示 ===\n")

	for email, users := range notifiers {
		fmt.Printf("📧 发送给: %s\n", email)
		fmt.Println("📝 原始逻辑的错误输出:")
		fmt.Println("---")

		link := pageLink(email)

		content := fmt.Sprintf(workwxTemplate, len(users))
		for _, uid := range users {
			// 这里有问题：content 已经被格式化过了，再次格式化会出错
			content = fmt.Sprintf(content, `
>[%d](%s&uid=%d)
`, uid, link, uid)
		}

		fmt.Print(content)
		fmt.Println("---\n")
	}

	fmt.Println("=== 修复后的正确实现 ===\n")

	for email, users := range notifiers {
		fmt.Printf("📧 发送给: %s\n", email)
		fmt.Println("📝 修复后的正确输出:")
		fmt.Println("---")

		link := pageLink(email)

		// 修复：使用字符串拼接而不是重复格式化
		content := fmt.Sprintf(workwxTemplate, len(users))
		for _, uid := range users {
			userLink := fmt.Sprintf(`
>[%d](%s&uid=%d)`, uid, link, uid)
			content += userLink
		}

		fmt.Print(content)
		fmt.Println("---\n")
	}
	
	fmt.Println("=== 问题分析 ===")
	fmt.Println("❌ 原始代码的问题:")
	fmt.Println("   - 在循环中重复使用 fmt.Sprintf(content, ...) 会导致格式化错误")
	fmt.Println("   - content 已经被格式化过，不应该再次作为格式化模板使用")
	fmt.Println()
	fmt.Println("✅ 修复方案:")
	fmt.Println("   - 使用字符串拼接 (+=) 而不是重复格式化")
	fmt.Println("   - 每个用户链接单独格式化后再拼接到 content")
	fmt.Println()
	fmt.Println("📋 最终格式说明:")
	fmt.Println("   1. 模板中的 %d 会被替换为用户数量")
	fmt.Println("   2. 每个用户ID会生成一个链接，格式为 [用户ID](链接&uid=用户ID)")
	fmt.Println("   3. 不同邮箱会根据是否为 sales-operation 使用不同的链接")
	fmt.Println("   4. 使用 Markdown 格式，支持粗体和链接")
}
