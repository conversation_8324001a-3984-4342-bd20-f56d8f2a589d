package main

import (
	"fmt"
)

// 模拟 pageLink 函数 - 基于实际代码
func pageLink(email string) string {
	portalHost := "https://portal.qiniu.io"
	salesPageLink := fmt.Sprintf(
		"%s/bo/financial/bank-transfer/confirmation?status=2",
		portalHost,
	)
	salesOperationPageLink := fmt.Sprintf(
		"%s/bo/financial/bank-transfer/admin?status=2",
		portalHost,
	)

	salesOperationEmail := "<EMAIL>" // 模拟 global.Env.Email.SalesOperationEmail
	if email == salesOperationEmail {
		return salesOperationPageLink
	}
	return salesPageLink
}

func main() {
	// 模拟数据：notifiers 是一个 map，key 是邮箱，value 是用户ID列表
	notifiers := map[string][]uint64{
		"<EMAIL>": {12345, 67890, 11111},
		"<EMAIL>":   {22222, 33333},
		"<EMAIL>": {44444},
	}

	// 最新修改后的代码中的拼接逻辑
	const workwxTemplate = `**您有 ` + "`%d`" + ` 个客户的银行转账待确认，请及时确认** `

	fmt.Println("=== 最新修改后代码的拼接逻辑和输出样式 ===\n")

	for email, users := range notifiers {
		fmt.Printf("📧 发送给: %s\n", email)
		fmt.Println("📝 最新修改后组装的消息内容:")
		fmt.Println("---")

		link := pageLink(email)

		// 最新修改后的代码逻辑：移除了用户链接末尾的换行符
		content := fmt.Sprintf(workwxTemplate, len(users))
		for _, uid := range users {
			content += fmt.Sprintf(`
>
>[%d](%s&uid=%d)`, uid, link, uid)
		}

		fmt.Print(content)
		fmt.Println("---\n")
	}

	fmt.Println("=== 最新修改后的格式分析 ===")
	fmt.Println("📋 消息结构:")
	fmt.Println("   1. 标题: **您有 `N` 个客户的银行转账待确认，请及时确认** (末尾有空格)")
	fmt.Println("   2. 每个用户前有空的引用行: >")
	fmt.Println("   3. 每个用户一行: >[用户ID](链接&uid=用户ID)")
	fmt.Println("   4. 用户链接之间无额外换行符")
	fmt.Println()
	fmt.Println("🔄 最新变化:")
	fmt.Println("   - 移除了用户链接格式化字符串末尾的换行符")
	fmt.Println("   - 用户链接更加紧凑，无多余空行")
	fmt.Println()
	fmt.Println("🔗 链接差异 (保持不变):")
	fmt.Println("   - 普通销售: /bo/financial/bank-transfer/confirmation?status=2&uid=用户ID")
	fmt.Println("   - 销售运营: /bo/financial/bank-transfer/admin?status=2&uid=用户ID")
	fmt.Println()
	fmt.Println("📝 Markdown 特性:")
	fmt.Println("   - 使用 ** 包围文字实现粗体")
	fmt.Println("   - 使用 ` 包围数字实现代码样式")
	fmt.Println("   - 用户链接部分使用 > 创建引用块")
	fmt.Println("   - 使用 [文字](链接) 创建超链接")
}
