module qiniu.io/gaea

go 1.23

require (
	github.com/Masterminds/squirrel v1.1.0
	github.com/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/go-wkhtmltopdf v1.5.0
	github.com/disintegration/imaging v1.6.2
	github.com/elastic/go-elasticsearch/v7 v7.17.7
	github.com/globalsign/mgo v0.0.0-20181015135952-eeefdecb41b8
	github.com/go-openapi/runtime v0.26.0
	github.com/go-openapi/strfmt v0.21.7
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-sql-driver/mysql v1.7.1
	github.com/go-xorm/xorm v0.7.6
	github.com/golang-jwt/jwt/v5 v5.0.0
	github.com/golang/mock v1.6.0
	github.com/google/go-querystring v1.0.0
	github.com/gorilla/schema v1.1.0
	github.com/julienschmidt/httprouter v1.3.0
	github.com/leebenson/conform v0.0.0-20170910104514-bdfc728cafe5
	github.com/mattbaird/elastigo v0.0.0-20170123220020-2fe47fd29e4b
	github.com/nicksnyder/go-i18n/v2 v2.2.1
	github.com/pborman/uuid v1.2.0
	github.com/prometheus/client_golang v1.16.0
	github.com/qbox/bo-base/base/phone v0.0.0-20220523084922-c4dab3fe30f5
	github.com/qbox/bo-base/v4 v4.33.2
	github.com/qbox/bo-sdk v0.0.0-20240410061948-2fc49ad2523d
	github.com/qbox/pay-sdk v0.0.0-20250619080523-7feaf9d1db0b
	github.com/qbox/sonic/sdk/trace v0.0.0-20220528090557-159e31cb04cb
	github.com/qiniu/errors v0.0.0-**************-************ // indirect
	github.com/qiniu/log.v1 v0.0.0-**************-************
	github.com/qiniu/rpc.v1 v0.0.0-**************-************
	github.com/qiniu/rpc.v2 v0.0.0-**************-************
	github.com/qiniu/rpc.v3 v0.0.0-**************-************
	github.com/qiniu/version/v2 v2.0.0
	github.com/qiniu/xlog.v1 v0.0.0-**************-************
	github.com/robfig/cron v1.2.0
	github.com/rs/xid v1.3.0
	github.com/samber/lo v1.39.0
	github.com/stretchr/testify v1.8.4
	github.com/teapots/config v0.0.0-**************-************
	github.com/teapots/gzip v0.0.0-**************-************
	github.com/teapots/inject v0.0.0-**************-************
	github.com/teapots/params v0.0.0-**************-************
	github.com/teapots/prometheusMetrics v0.0.0-**************-************
	github.com/teapots/render v0.0.0-**************-************
	github.com/teapots/request-logger v0.0.0-**************-************
	github.com/teapots/static-serve v0.0.0-**************-************
	github.com/teapots/teapot v0.0.0-**************-************
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.508
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ocr v1.0.508
	github.com/tidwall/gjson v1.14.4
	github.com/unidoc/unipdf/v3 v3.31.0
	github.com/urfave/cli v1.22.1
	github.com/wechatpay-apiv3/wechatpay-go v0.2.16
	github.com/xen0n/go-workwx v1.6.0
	go.mongodb.org/mongo-driver v1.12.0
	golang.org/x/exp v0.0.0-**************-fe59bbe5cc7f
	golang.org/x/sync v0.8.0
	golang.org/x/text v0.19.0
	google.golang.org/grpc v1.63.2
	google.golang.org/protobuf v1.35.1
	gopkg.in/go-playground/validator.v9 v9.31.0
	gopkg.in/yaml.v3 v3.0.1
	labix.org/v2/mgo v0.0.0-**************-************
	qbox.us/admin_api/account.v2 v0.0.0-**************-************
	qbox.us/admin_api/rs.v3 v0.0.0-**************-************
	qbox.us/api v0.0.0-**************-************
	qbox.us/api/account.v2 v0.0.0-**************-************
	qbox.us/api/fusion v0.0.0-**************-************
	qbox.us/api/message v0.0.0-**************-************
	qbox.us/api/one v0.0.0-**************-************
	qbox.us/api/pay v0.0.0-**************-************
	qbox.us/api/uc v0.0.0-**************-************
	qbox.us/api/uc.v2 v0.0.0-**************-************
	qbox.us/api/up v0.0.0-**************-************
	qbox.us/biz/api/gaea v0.0.0-**************-************
	qbox.us/biz/api/gaeaadmin v0.0.0-**************-************
	qbox.us/biz/api/portal.io v0.0.0-**************-************
	qbox.us/biz/component/api v0.0.0-**************-************
	qbox.us/biz/component/client v0.0.0-**************-************
	qbox.us/biz/component/filters v0.0.0-**************-************
	qbox.us/biz/component/helpers v0.0.0-**************-************
	qbox.us/biz/component/providers v0.0.0-**************-************
	qbox.us/biz/component/sessions v0.0.0-**************-************
	qbox.us/biz/services.v2 v0.0.0-**************-************
	qbox.us/biz/utils.v2 v0.0.0-**************-************
	qbox.us/cc v0.0.0-**************-************
	qbox.us/encoding v0.0.0-**************-************
	qbox.us/errors v0.0.0-**************-************
	qbox.us/net v0.0.0-**************-************
	qbox.us/oauth v0.0.0-**************-************
	qbox.us/servend v0.0.0-**************-************
	qbox.us/verifycode v0.0.0-**************-************
	qiniu.com/auth v0.0.0-**************-************
	qiniupkg.com/api.v7 v0.0.0-**************-************
	qiniupkg.com/x v7.0.8+incompatible
)

require (
	github.com/gocarina/gocsv v0.0.0-20240520201108-78e41c74b4b1
	github.com/hibiken/asynq v0.25.0
	github.com/hibiken/asynqmon v0.7.2
	github.com/redis/go-redis/v9 v9.7.0
	github.com/xuri/excelize/v2 v2.9.0
	qbox.us/iam v0.0.0-**************-************
)

require (
	github.com/go-playground/validator/v10 v10.13.0 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus v1.0.1 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware/v2 v2.1.0 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/montanaflynn/stats v0.7.1 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.4 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/spf13/cast v1.7.0 // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.1.2 // indirect
	github.com/xdg-go/stringprep v1.0.4 // indirect
	github.com/xuri/efp v0.0.0-20240408161823-9ad904a10d6d // indirect
	github.com/xuri/nfp v0.0.0-20240318013403-ab9948c2c4a7 // indirect
	github.com/youmark/pkcs8 v0.0.0-20201027041543-1326539a0a0a // indirect
)

require (
	code.google.com/p/go.net v0.0.0-**************-************ // indirect
	github.com/BurntSushi/toml v1.2.1 // indirect
	github.com/alicebob/gopher-json v0.0.0-20200520072559-a9ecdc9d1d3a // indirect
	github.com/alicebob/miniredis/v2 v2.30.3 // indirect
	github.com/araddon/gou v0.0.0-20190110011759-c797efecbb61 // indirect
	github.com/asaskevich/govalidator v0.0.0-20230301143203-a9d515a09cc2 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bitly/go-hostpool v0.0.0-20171023180738-a3a6125de932 // indirect
	github.com/bmizerany/assert v0.0.0-20160611221934-b7ed37b82869 // indirect
	github.com/bradfitz/gomemcache v0.0.0-20230905024940-24af94b03874 // indirect
	github.com/bradfitz/gomemcache.20160421 v0.0.0-**************-************ // indirect
	github.com/cenkalti/backoff/v4 v4.1.3 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/corpix/uarand v0.1.1 // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.2 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/etgryphon/stringUp v0.0.0-20121020160746-31534ccd8cac // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/facebookgo/httpdown v0.0.0-20180706035922-5979d39b15c2 // indirect
	github.com/facebookgo/stats v0.0.0-20151006221625-1b76add642e4 // indirect
	github.com/fatih/structs v1.1.0 // indirect
	github.com/go-logr/logr v1.2.4 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-openapi/analysis v0.21.4 // indirect
	github.com/go-openapi/errors v0.20.4 // indirect
	github.com/go-openapi/jsonpointer v0.19.6 // indirect
	github.com/go-openapi/jsonreference v0.20.2 // indirect
	github.com/go-openapi/loads v0.21.2 // indirect
	github.com/go-openapi/spec v0.20.9 // indirect
	github.com/go-openapi/swag v0.22.4 // indirect
	github.com/go-openapi/validate v0.22.1 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-redis/cache/v8 v8.4.4 // indirect
	github.com/gogo/googleapis v1.4.1 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.4.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/icrowley/fake v0.0.0-20180203215853-4178557ae428 // indirect
	github.com/imdario/mergo v0.3.15 // indirect
	github.com/jinzhu/gorm v1.9.16 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/kavu/go_reuseport v1.5.0 // indirect
	github.com/klauspost/compress v1.17.8 // indirect
	github.com/lann/builder v0.0.0-20180802200727-47ae307949d0 // indirect
	github.com/lann/ps v0.0.0-20150810152359-62de8c46ede0 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/nyaruka/phonenumbers v1.0.75 // indirect
	github.com/oklog/ulid v1.3.1 // indirect
	github.com/oklog/ulid/v2 v2.1.0 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/pkg/errors v0.9.1
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_model v0.3.0 // indirect
	github.com/prometheus/common v0.42.0 // indirect
	github.com/prometheus/procfs v0.10.1 // indirect
	github.com/qbox/bo-base/base/phone/v2 v2.0.0-20230202125626-2df96b146622 // indirect
	github.com/qcos/configs v0.0.0-**************-************ // indirect
	github.com/qiniu/api v0.0.0-**************-************ // indirect
	github.com/qiniu/bytes v0.0.0-**************-************ // indirect
	github.com/qiniu/ctype v0.0.0-**************-************ // indirect
	github.com/qiniu/http v0.0.0-**************-************ // indirect
	github.com/qiniu/io v0.0.0-**************-************ // indirect
	github.com/qiniu/largefile v0.0.0-**************-************ // indirect
	github.com/qiniu/mockhttp.v2 v0.0.0-**************-************ // indirect
	github.com/qiniu/qmgo v1.1.8
	github.com/qiniu/ts v0.0.0-**************-************ // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	github.com/sirupsen/logrus v1.9.2 // indirect
	github.com/streadway/amqp v1.0.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/uber/jaeger-client-go v2.30.0+incompatible // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/unidoc/pkcs7 v0.1.0 // indirect
	github.com/unidoc/timestamp v0.0.0-20200412005513-91597fd3793a // indirect
	github.com/unidoc/unitype v0.2.1 // indirect
	github.com/vmihailenco/go-tinylfu v0.2.2 // indirect
	github.com/vmihailenco/msgpack/v5 v5.3.5 // indirect
	github.com/vmihailenco/tagparser/v2 v2.0.0 // indirect
	github.com/yuin/gopher-lua v1.1.0 // indirect
	go.opentelemetry.io/otel v1.16.0 // indirect
	go.opentelemetry.io/otel/metric v1.16.0 // indirect
	go.opentelemetry.io/otel/trace v1.16.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	golang.org/x/crypto v0.28.0 // indirect
	golang.org/x/image v0.18.0 // indirect
	golang.org/x/net v0.30.0 // indirect
	golang.org/x/sys v0.26.0 // indirect
	golang.org/x/time v0.7.0 // indirect
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/genproto v0.0.0-**************-790db918fca8 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-**************-8c6c420018be // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-**************-8c6c420018be // indirect
	gopkg.in/bsm/ratelimit.v1 v1.0.0-**************-************ // indirect
	gopkg.in/mgo.v2 v2.0.0-**************-a6b53ec6cb22 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	launchpad.net/mgo v0.0.0-**************-************ // indirect
	qbox.us/account-api v0.0.0-**************-************ // indirect
	qbox.us/admin_api/v2/account v0.0.0-**************-************ // indirect
	qbox.us/api/account v0.0.0-**************-************ // indirect
	qbox.us/api/cdn.v1 v0.0.0-**************-************ // indirect
	qbox.us/api/conf v0.0.0-**************-************ // indirect
	qbox.us/api/qconf v0.0.0-**************-************ // indirect
	qbox.us/api/tblmgr v0.0.0-**************-************ // indirect
	qbox.us/digest_auth v0.0.0-**************-************ // indirect
	qbox.us/http v0.0.0-**************-************ // indirect
	qbox.us/limit v0.0.0-**************-************ // indirect
	qbox.us/mockacc v0.0.0-**************-************ // indirect
	qbox.us/multipart v0.0.0-**************-************ // indirect
	qbox.us/qconf v0.0.0-**************-************ // indirect
	qbox.us/rateio v0.0.0-**************-************ // indirect
	qbox.us/ratelimit v0.0.0-**************-************ // indirect
	qbox.us/rpc v0.0.0-**************-************ // indirect
	qbox.us/servestk v0.0.0-**************-************ // indirect
	qiniupkg.com/dyn v0.0.0-**************-************ // indirect
	qiniupkg.com/httptest.v1 v0.0.0-**************-************ // indirect
	qiniupkg.com/qiniutest v0.0.0-**************-************ // indirect
	qiniupkg.com/trace.v1 v0.0.0-**************-************ // indirect
	xorm.io/builder v0.3.5 // indirect
	xorm.io/core v0.7.0 // indirect
)

replace code.google.com/p/go.net => ./_qbox_base/com/src/code.google.com/p/go.net

replace github.com/bradfitz/gomemcache.20160421 => ./_qbox_base/com/src/github.com/bradfitz/gomemcache.20160421

replace github.com/qcos/configs => ./_qbox_base/portal/src/github.com/qcos/configs

replace github.com/qiniu/api => ./_qbox_base/qiniu/src/github.com/qiniu/api

replace github.com/qiniu/bits => ./_qbox_base/qiniu/src/github.com/qiniu/bits

replace github.com/qiniu/bufio => ./_qbox_base/qiniu/src/github.com/qiniu/bufio

replace github.com/qiniu/bytes => ./_qbox_base/qiniu/src/github.com/qiniu/bytes

replace github.com/qiniu/ctype => ./_qbox_base/qiniu/src/github.com/qiniu/ctype

replace github.com/qiniu/dyn => ./_qbox_base/qiniu/src/github.com/qiniu/dyn

replace github.com/qiniu/encoding => ./_qbox_base/qiniu/src/github.com/qiniu/encoding

replace github.com/qiniu/encoding.v2 => ./_qbox_base/qiniu/src/github.com/qiniu/encoding.v2

replace github.com/qiniu/errors => ./_qbox_base/qiniu/src/github.com/qiniu/errors

replace github.com/qiniu/filelog => ./_qbox_base/qiniu/src/github.com/qiniu/filelog

replace github.com/qiniu/http => ./_qbox_base/qiniu/src/github.com/qiniu/http

replace github.com/qiniu/io => ./_qbox_base/qiniu/src/github.com/qiniu/io

replace github.com/qiniu/largefile => ./_qbox_base/qiniu/src/github.com/qiniu/largefile

replace github.com/qiniu/log.v1 => ./_qbox_base/qiniu/src/github.com/qiniu/log.v1

replace github.com/qiniu/mockhttp.v2 => ./_qbox_base/qiniu/src/github.com/qiniu/mockhttp.v2

replace github.com/qiniu/osl => ./_qbox_base/qiniu/src/github.com/qiniu/osl

replace github.com/qiniu/reliable => ./_qbox_base/qiniu/src/github.com/qiniu/reliable

replace github.com/qiniu/rpc.v1 => ./_qbox_base/qiniu/src/github.com/qiniu/rpc.v1

replace github.com/qiniu/rpc.v2 => ./_qbox_base/qiniu/src/github.com/qiniu/rpc.v2

replace github.com/qiniu/rpc.v3 => ./_qbox_base/qiniu/src/github.com/qiniu/rpc.v3

replace github.com/qiniu/ts => ./_qbox_base/qiniu/src/github.com/qiniu/ts

replace github.com/qiniu/xlog.v1 => ./_qbox_base/qiniu/src/github.com/qiniu/xlog.v1

// XXX this unfortunate package should remain here before someone fixes
// all the problematic tests failing on the latest version
//replace github.com/stretchr/testify => ./_qbox_base/com/src/github.com/stretchr/testify

//replace github.com/stretchr/testify.v1 => ./_qbox_base/com/src/github.com/stretchr/testify.v1

//replace github.com/stretchr/testify.v2 => ./_qbox_base/com/src/github.com/stretchr/testify.v2

replace github.com/teapots/config => ./_qbox_base/portal/src/github.com/teapots/config

replace github.com/teapots/gzip => ./_qbox_base/portal/src/github.com/teapots/gzip

replace github.com/teapots/inject => ./_qbox_base/portal/src/github.com/teapots/inject

replace github.com/teapots/params => ./_qbox_base/portal/src/github.com/teapots/params

replace github.com/teapots/prometheusMetrics => ./_qbox_base/portal/src/github.com/teapots/prometheusMetrics

replace github.com/teapots/render => ./_qbox_base/portal/src/github.com/teapots/render

replace github.com/teapots/request-logger => ./_qbox_base/portal/src/github.com/teapots/request-logger

replace github.com/teapots/static-serve => ./_qbox_base/portal/src/github.com/teapots/static-serve

replace github.com/teapots/teapot => ./_qbox_base/portal/src/github.com/teapots/teapot

replace gopkg.in/bsm/ratelimit.v1 => ./_qbox_base/com/src/gopkg.in/bsm/ratelimit.v1

replace gopkg.in/check.v1 => ./_qbox_base/com/src/gopkg.in/check.v1

replace gopkg.in/mgo.v2 => ./_qbox_base/com/src/gopkg.in/mgo.v2

replace gopkg.in/tomb.v2 => ./_qbox_base/com/src/gopkg.in/tomb.v2

replace labix.org/v2/mgo => ./_qbox_base/com/src/labix.org/v2/mgo

replace launchpad.net/gocheck => ./_qbox_base/com/src/launchpad.net/gocheck

replace launchpad.net/mgo => ./_qbox_base/com/src/launchpad.net/mgo

replace qbox.us/account => ./_qbox_base/mockacc/src/qbox.us/account

replace qbox.us/account-api => ./_qbox_base/account-api/src/qbox.us/account-api

replace qbox.us/admin_api/account.v2 => ./_qbox_base/biz/src/qbox.us/admin_api/account.v2

replace qbox.us/admin_api/one/ufop => ./_qbox_base/biz/src/qbox.us/admin_api/one/ufop

replace qbox.us/admin_api/rs.v3 => ./_qbox_base/biz/src/qbox.us/admin_api/rs.v3

replace qbox.us/admin_api/uc => ./_qbox_base/biz/src/qbox.us/admin_api/uc

replace qbox.us/admin_api/v2/account => ./_qbox_base/biz/src/qbox.us/admin_api/v2/account

replace qbox.us/api => ./_qbox_base/com/src/qbox.us/api

replace qbox.us/api/account => ./_qbox_base/biz/src/qbox.us/api/account

replace qbox.us/api/account.v2 => ./_qbox_base/biz/src/qbox.us/api/account.v2

replace qbox.us/api/cdn.v1 => ./_qbox_base/biz/src/qbox.us/api/cdn.v1

replace qbox.us/api/conf => ./_qbox_base/biz/src/qbox.us/api/conf

replace qbox.us/api/fusion => ./_qbox_base/biz/src/qbox.us/api/fusion

replace qbox.us/api/message => ./_qbox_base/biz/src/qbox.us/api/message

replace qbox.us/api/notification => ./_qbox_base/biz/src/qbox.us/api/notification

replace qbox.us/api/one => ./_qbox_base/biz/src/qbox.us/api/one

replace qbox.us/api/pay => ./_qbox_base/biz/src/qbox.us/api/pay

replace qbox.us/api/pfopmq => ./_qbox_base/biz/src/qbox.us/api/pfopmq

replace qbox.us/api/qconf => ./_qbox_base/biz/src/qbox.us/api/qconf

replace qbox.us/api/stat => ./_qbox_base/biz/src/qbox.us/api/stat

replace qbox.us/api/tblmgr => ./_qbox_base/biz/src/qbox.us/api/tblmgr

replace qbox.us/api/uc => ./_qbox_base/biz/src/qbox.us/api/uc

replace qbox.us/api/uc.v2 => ./_qbox_base/biz/src/qbox.us/api/uc.v2

replace qbox.us/api/up => ./_qbox_base/biz/src/qbox.us/api/up

replace qbox.us/audit => ./_qbox_base/com/src/qbox.us/audit

replace qbox.us/autoua => ./_qbox_base/com/src/qbox.us/autoua

replace qbox.us/biz/api/gaea => ./_qbox_base/portal/src/qbox.us/biz/api/gaea

replace qbox.us/biz/api/gaeaadmin => ./_qbox_base/portal/src/qbox.us/biz/api/gaeaadmin

replace qbox.us/biz/api/portal.io => ./_qbox_base/portal/src/qbox.us/biz/api/portal.io

replace qbox.us/biz/component/api => ./_qbox_base/portal/src/qbox.us/biz/component/api

replace qbox.us/biz/component/client => ./_qbox_base/portal/src/qbox.us/biz/component/client

replace qbox.us/biz/component/filters => ./_qbox_base/portal/src/qbox.us/biz/component/filters

replace qbox.us/biz/component/helpers => ./_qbox_base/portal/src/qbox.us/biz/component/helpers

replace qbox.us/biz/component/providers => ./_qbox_base/portal/src/qbox.us/biz/component/providers

replace qbox.us/biz/component/sessions => ./_qbox_base/portal/src/qbox.us/biz/component/sessions

replace qbox.us/biz/services.v2 => ./_qbox_base/portal/src/qbox.us/biz/services.v2

replace qbox.us/biz/utils.v2 => ./_qbox_base/portal/src/qbox.us/biz/utils.v2

replace qbox.us/cc => ./_qbox_base/com/src/qbox.us/cc

replace qbox.us/digest_auth => ./_qbox_base/com/src/qbox.us/digest_auth

replace qbox.us/dyn => ./_qbox_base/com/src/qbox.us/dyn

replace qbox.us/encoding => ./_qbox_base/com/src/qbox.us/encoding

replace qbox.us/errors => ./_qbox_base/com/src/qbox.us/errors

replace qbox.us/http => ./_qbox_base/biz/src/qbox.us/http

replace qbox.us/iam => ./_qbox_base/biz/src/qbox.us/iam

replace qbox.us/iputil => ./_qbox_base/com/src/qbox.us/iputil

replace qbox.us/largefile => ./_qbox_base/com/src/qbox.us/largefile

replace qbox.us/lbsocketproxy => ./_qbox_base/com/src/qbox.us/lbsocketproxy

replace qbox.us/limit => ./_qbox_base/com/src/qbox.us/limit

replace qbox.us/mgo2 => ./_qbox_base/com/src/qbox.us/mgo2

replace qbox.us/mockacc => ./_qbox_base/biz/src/qbox.us/mockacc

replace qbox.us/multipart => ./_qbox_base/com/src/qbox.us/multipart

replace qbox.us/net => ./_qbox_base/com/src/qbox.us/net

replace qbox.us/oauth => ./_qbox_base/com/src/qbox.us/oauth

replace qbox.us/profile => ./_qbox_base/com/src/qbox.us/profile

replace qbox.us/qconf => ./_qbox_base/biz/src/qbox.us/qconf

replace qbox.us/rateio => ./_qbox_base/com/src/qbox.us/rateio

replace qbox.us/ratelimit => ./_qbox_base/com/src/qbox.us/ratelimit

replace qbox.us/rpc => ./_qbox_base/com/src/qbox.us/rpc

replace qbox.us/servend => ./_qbox_base/biz/src/qbox.us/servend

replace qbox.us/servestk => ./_qbox_base/com/src/qbox.us/servestk

replace qbox.us/state => ./_qbox_base/com/src/qbox.us/state

replace qbox.us/ufop => ./_qbox_base/biz/src/qbox.us/ufop

replace qbox.us/verifycode => ./_qbox_base/com/src/qbox.us/verifycode

replace qiniu.com/auth => ./_qbox_base/qiniu/src/qiniu.com/auth

replace qiniu.com/probe => ./_qbox_base/qiniu/src/qiniu.com/probe

replace qiniupkg.com/api.v7 => ./_qbox_base/qiniu/src/qiniupkg.com/api.v7

replace qiniupkg.com/dyn => ./_qbox_base/qiniu/src/qiniupkg.com/dyn

replace qiniupkg.com/httptest.v1 => ./_qbox_base/qiniu/src/qiniupkg.com/httptest.v1

replace qiniupkg.com/qiniutest => ./_qbox_base/qiniu/src/qiniupkg.com/qiniutest

replace qiniupkg.com/trace.v1 => ./_qbox_base/qiniu/src/qiniupkg.com/trace.v1

replace qiniupkg.com/x => ./_qbox_base/qiniu/src/qiniupkg.com/x

replace qiniupkg.com/http => ./_qbox_base/qiniu/src/qiniupkg.com/http

replace github.com/teapots/teapot-cmd/teapot => ./_qbox_base/portal/src/github.com/teapots/teapot-cmd/teapot
