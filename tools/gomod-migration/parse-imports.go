package main

import (
	"fmt"
	"go/parser"
	"go/token"
	"io/ioutil"
	"os"
)

func main() {
	filename := os.Args[1]
	contents, err := ioutil.ReadFile(filename)
	if err != nil {
		panic(err)
	}

	fset := token.NewFileSet()
	ast, err := parser.ParseFile(fset, filename, contents, parser.ImportsOnly)
	if err != nil {
		panic(err)
	}

	// fmt.Println(ast.Name)

	for _, sp := range ast.Imports {
		importPath := sp.Path.Value
		fmt.Println(importPath[1 : len(importPath)-1])
	}
}
