#!/usr/bin/env python3

import functools
import glob
import os
import pathlib
import re
import subprocess
import sys

from typing import *
from typing import TextIO

T = TypeVar('T')


@functools.total_ordering
class GoPkg:
    def __init__(self, root: str, import_path: str, path: Optional[pathlib.Path]) -> None:
        self.root: str = root
        self.import_path: str = import_path
        self.path: Optional[pathlib.Path] = path

        self.deps: Set[str] = set()
        self.resolved_deps: Optional[List[GoPkg]] = None

    def __repr__(self) -> str:
        return '<GoPkg {}:{}>'.format(self.root, self.import_path)

    def __eq__(self, other) -> bool:
        if not isinstance(other, GoPkg):
            return NotImplemented
        return self.import_path == other.import_path

    def __lt__(self, other) -> bool:
        if not isinstance(other, GoPkg):
            return NotImplemented
        return self.import_path < other.import_path

    def add_dep(self, imp: str) -> None:
        self.deps.add(imp)

    def is_deps_resolved(self) -> bool:
        return self.resolved_deps is not None

    def resolve_deps(self, registry: 'LocalPkgRegistry') -> None:
        tmp = []
        for dep in self.deps:
            pkg = registry.resolve_pkg(dep)
            if pkg is None:
                print('>>> [{}] {} => \x1b[1;31m!!!!!!!!!!!\x1b[m'.format(self.import_path, dep))
                #raise ValueError('import {} cannot be resolved'.format(dep))
                tmp.append(GoPkg('XXX', dep, None))
                continue
            print('>>> [{}] {} => \x1b[0;32m{}\x1b[m:{}'.format(self.import_path, dep, pkg.root, pkg.import_path))
            tmp.append(pkg)
        self.resolved_deps = tmp

    def probe_for_child_pkg(self, import_path: str) -> bool:
        # self.import_path = foo/bar
        # import_path = foo/bar/baz/quux

        if self.path is None:
            # self is unresolved, don't know if there're children
            return False

        if self.import_path == import_path:
            # it's asking for self!
            return True

        # get relative path to self
        # error out if import_path does not start with self
        if not import_path.startswith(self.import_path + '/'):
            raise ValueError('given path not possible to be subdir of self')

        rel_path_str = import_path[len(self.import_path) + 1:]
        #print('>>>>>', self.import_path, import_path, rel_path_str)

        rel_path = self.path / rel_path_str
        return rel_path.is_dir()

    def make_go_mod_file(self, reg: 'LocalPkgRegistry') -> 'GoModFile':
        if self.resolved_deps is None:
            raise ValueError('dep is not resolved yet for this pkg')
        return GoModFile(self, self.resolved_deps, reg)


def carcdr(l: List[T]) -> Tuple[Optional[T], List[T]]:
    if not l:
        return None, []
    return l[0], l[1:]


class PrefixTree:
    def __init__(self, name='', parent=None) -> None:
        self.name: str = name
        self._parent: 'PrefixTree' = parent
        self._children: Dict[str, 'PrefixTree'] = {}

    def add(self, import_path: str) -> None:
        frags = import_path.split('/')
        return self._add(frags)

    def _add(self, frags: List[str]) -> None:
        car, cdr = carcdr(frags)
        if car is None:
            raise ValueError('unreachable')

        # find child
        child = self.find_one(car)

        # if not already present at this level, create one
        if child is None:
            child = PrefixTree(car, self)
            self._children[car] = child

        # mkdir -p
        if len(cdr) > 0:
            child._add(cdr)

    def find_one(self, name: str):
        return self._children.get(name)

    def query_prefix(self, import_path: str) -> Optional[str]:
        frags = import_path.split('/')
        return self._query_prefix(frags)

    def _query_prefix(self, frags: List[str]) -> Optional[str]:
        car, cdr = carcdr(frags)
        if car is None:
            # it's a hit only if self is leaf
            return self._str() if self.is_leaf() else None
        #print('----- self=<{}> frags={}'.format(self._str(), frags))

        # car matched
        # if self is leaf (non-root), this means self is prefix of outermost frags
        #
        # e.g.:
        #
        # 1. qbox.us/abc vs qbox.us/abc
        # eventually we'll be standing at abc with cdr=[]
        #
        # 2. qbox.us/abc vs qbox.us/abc/def/ghi
        # eventually we'll be standing at abc with cdr=['def', 'ghi']
        if self.is_leaf():
            return self._str()

        child = self.find_one(car)
        if child is None:
            return None

        return child._query_prefix(cdr)

    def is_leaf(self) -> bool:
        return len(self._children) == 0 and self._parent is not None

    def _str(self) -> str:
        # root node has no import path
        if self._parent is None:
            return ''

        # str(parent)/self.name
        return '{}/{}'.format(self._parent._str(), self.name).lstrip('/')


class LocalPkgRegistry:
    def __init__(self) -> None:
        self._pkgs: Dict[str, GoPkg] = {}
        self._prefixes_by_roots: Dict[str, PrefixTree] = {}

    def add_pkg(self, pkg: GoPkg) -> None:
        # init prefix tree for the gopath root if not already done
        gopath_root = pkg.root
        if gopath_root not in self._prefixes_by_roots:
            prefixtree = PrefixTree()
            self._prefixes_by_roots[gopath_root] = prefixtree
        else:
            prefixtree = self._prefixes_by_roots[gopath_root]

        # check prefix matches to ensure every root is independent
        imp = pkg.import_path
        matched_prefix = prefixtree.query_prefix(imp)
        if matched_prefix is not None:
            raise ValueError('prefix already present in registry: {} (prefix {})'.format(imp, matched_prefix))

        self._pkgs[imp] = pkg
        prefixtree.add(imp)

    def num_pkgs(self) -> int:
        return len(self._pkgs)

    def get_by_import_path(self, import_path: str) -> Optional[GoPkg]:
        return self._pkgs.get(import_path)

    def iter_pkgs(self) -> ValuesView[GoPkg]:
        return self._pkgs.values()

    def resolve_pkg(self, import_path: str) -> Optional[GoPkg]:
        resolutions = []
        for prefixtree in self._prefixes_by_roots.values():
            root_pkg = prefixtree.query_prefix(import_path)
            if root_pkg is not None:
                pkg = self.get_by_import_path(root_pkg)
                if pkg is None:
                    raise ValueError('unreachable')

                # probe if "false friend"
                if not pkg.probe_for_child_pkg(import_path):
                    continue

                resolutions.append(pkg)

        if len(resolutions) == 0:
            return None
        elif len(resolutions) > 1:
            raise ValueError('ambiguous resolution: {} => {}'.format(import_path, resolutions))

        return resolutions[0]


class GoModFile:
    def __init__(self, who: GoPkg, replaces: List[GoPkg], reg: LocalPkgRegistry) -> None:
        self.who: GoPkg = who
        self.replaces: List[GoPkg] = replaces
        self.reg: LocalPkgRegistry = reg

    def my_path(self) -> pathlib.Path:
        if self.who.path is None:
            raise ValueError('cannot get go.mod path for unresolved pkg')
        return self.who.path / 'go.mod'

    def write_out(self) -> None:
        with open(self.my_path(), 'w') as fp:
            return self._write_to(fp)

    def _write_to(self, fp: TextIO) -> None:
        p = lambda x: print(x, file=fp)
        p('module {}'.format(self.who.import_path))
        p('')
        p('go 1.18')
        written: Set[str] = set()
        for pkg in sorted(self.replaces):
            if pkg.root == 'XXX':
                # hhh
                continue

            if pkg.import_path in written:
                continue
            written.add(pkg.import_path)

            p('')
            relativized_replace_path = relpath_between_pkgs(self.who, pkg)
            p('replace {} => {}'.format(pkg.import_path, relativized_replace_path))


def relpath_between_pkgs(ref: GoPkg, other: GoPkg) -> pathlib.Path:
    if ref.path is None or other.path is None:
        raise ValueError('cannot derive relative path between unresolved pkg(s)')
    return pathlib.Path(os.path.relpath(other.path, ref.path))


def parse_pkg_entry(line: str) -> GoPkg:
    import_path, gopath_root, path = line.strip().split(',')
    return GoPkg(gopath_root, import_path, pathlib.Path(path))


def collect_imports_from_one_file(filename: str) -> List[str]:
    args = ['./tools/gomod-migration/parse-imports', filename, ]
    subp = subprocess.Popen(args, stdout=subprocess.PIPE)
    stdout, _stderr = subp.communicate()
    x = stdout.decode('utf-8').strip()
    return [] if not x else x.split('\n')


def collect_imports_from_package(path: pathlib.Path) -> List[str]:
    unique_imports = set()
    for filepath in path.glob('**/*.go'):
        imports = collect_imports_from_one_file(str(filepath))
        for imp in imports:
            if imp in {'github.com/onsi/A', 'github.com/onsi/B', 'github.com/onsi/C'}:
                # this is broken; ginkgo copies the files over to correct location at test-time!
                continue

            unique_imports.add(imp)

    return list(unique_imports)


def collect_package_external_imports(pkg: GoPkg, goroot_pkgs: Set[str]) -> Iterator[str]:
    path = pkg.path
    if path is None:
        raise ValueError('cannot collect imports for unresolved pkg')
    for imp in collect_imports_from_package(path):
        if imp not in goroot_pkgs and not imp.startswith(pkg.import_path):
            yield imp


def read_goroot_pkgs() -> Iterator[str]:
    with open('./tools/gomod-migration/gorootpkgs.txt', 'r') as fp:
        for line in fp:
            yield line.strip()


USAGE = '''\
usage: {} <pkgs-to-migrate.txt>

generate go.mod files full of replace directives, for all packages inside
the transitive dep closure of input packages

go.mod roots are specified in gomod-roots.csv along with this script

format of the input is one import path per line
'''

def main(argv: List[str]) -> None:
    if len(argv) != 2:
        print(USAGE.format(argv[0]))
        sys.exit(1)
        return

    pkg_registry = LocalPkgRegistry()
    with open('./tools/gomod-migration/gomod-roots.csv', 'r') as fp:
        for line in fp:
            pkg = parse_pkg_entry(line)
            pkg_registry.add_pkg(pkg)

    goroot_pkgs = set(read_goroot_pkgs())

    print('{} packages total.'.format(pkg_registry.num_pkgs()))

    cached_deps = 'REGEN_DEP_CACHE' not in os.environ
    if cached_deps:
        dep_cache: Dict[str, List[str]] = {}
        with open('./tools/gomod-migration/deps.cache.txt', 'r') as fp:
            dep_entries = (l.strip().split(' => ') for l in fp)
            for pkg_, dep_ in dep_entries:
                if pkg_ in dep_cache:
                    dep_cache[pkg_].append(dep_)
                else:
                    dep_cache[pkg_] = [dep_]

        for pkg in pkg_registry.iter_pkgs():
            for imp in dep_cache.get(pkg.import_path, ()):
                pkg.add_dep(imp)
    else:
        # gather imports
        buf = []
        for pkg in pkg_registry.iter_pkgs():
            for imp in collect_package_external_imports(pkg, goroot_pkgs):
                line = '{} => {}'.format(pkg.import_path, imp)
                print(line)
                buf.append(line)
                pkg.add_dep(imp)

        buf.sort()
        with open('./tools/gomod-migration/deps.cache.txt', 'w') as fp:
            fp.write('\n'.join(buf))
            fp.write('\n')

    # packages to migrate => modules to make
    with open(argv[1], 'r') as fp:
        pkgs_to_migrate = [i.strip() for i in fp]

    # resolve into roots
    roots_to_migrate = set()
    for imp in pkgs_to_migrate:
        pkg_opt = pkg_registry.resolve_pkg(imp)
        if pkg_opt is None:
            raise ValueError('import path {} unresolved'.format(imp))
        # print('# {} => {}'.format(imp, pkg.import_path))
        roots_to_migrate.add(pkg_opt.import_path)

    print('{} packages to migrate ({} root modules).'.format(len(pkgs_to_migrate), len(roots_to_migrate)))

    # resolve deps and add transitive deps
    transitive_dep_completed = False
    while not transitive_dep_completed:
        transitive_dep_completed = True
        tmp = roots_to_migrate.copy()
        for root in roots_to_migrate:
            pkg_opt = pkg_registry.get_by_import_path(root)

            if pkg_opt is None:
                # unresolved import
                continue

            if pkg_opt.is_deps_resolved():
                continue

            pkg_opt.resolve_deps(pkg_registry)
            transitive_dep_completed = False

            # add all deps into root set
            if pkg_opt.resolved_deps is None:
                raise ValueError('unreachable')
            for p in pkg_opt.resolved_deps:
                tmp.add(p.import_path)

        roots_to_migrate = tmp

    print('transitive dep closure have {} packages.'.format(len(roots_to_migrate)))

    # make go mod files!
    for root in roots_to_migrate:
        pkg_opt = pkg_registry.get_by_import_path(root)
        if pkg_opt is None:
            # unresolved (transitive) dep
            continue
        gomod = pkg_opt.make_go_mod_file(pkg_registry)
        gomod.write_out()


if __name__ == '__main__':
    # get to project root
    base_root = pathlib.Path(__file__).parent / '../..'
    base_root = base_root.resolve()
    os.chdir(base_root)
    main(sys.argv)
