package global

import (
	"time"

	"google.golang.org/grpc/keepalive"

	"github.com/qbox/bo-base/v4/intl"
	"github.com/teapots/teapot"
	"qbox.us/biz/component/sessions"
)

// Env 是全局配置信息
var Env Setting
var SessionManager *sessions.SessionManager

// Setting 从配置文件解析, 与配置文件一起
type Setting struct {
	Teapot *teapot.Teapot `conf:"-"`
	Config *teapot.Config `conf:"-"`

	// EnablePprof 调试用，是否启用 /debug/pprof API
	EnablePprof bool `conf:"enable_pprof"`

	TemplatesPath string `conf:"templates_path"`
	ViewsPath     string `conf:"views_path"`

	EnableTotpRateLimit bool `conf:"enable_totp_rate_limit"`

	ClearBucketNotifyDays string `conf:"clear_bucket_notify_days"` // 清理空间通知多少天前被冻结的用户

	TestMobile string `conf:"test_mobile"` // 测试手机号，跳过绑定手机号次数限制

	Intl intl.IniConfig `conf:"intl"`

	HOST struct {
		PortalFrontend    string `conf:"portal_frontend"`
		PortalGaeaBackend string `conf:"portal_gaea_backend"`

		SupportLink                    string `conf:"support_link"`
		SecurityLink                   string `conf:"security_link"`
		Unsubscribe                    string `conf:"unsubscribe"`
		IdentityLink                   string `conf:"identity_link"`
		RechargeLink                   string `conf:"recharge_link"`
		FreezePolicyLink               string `conf:"freeze_policy_link"`
		FreezeStrategyLink             string `conf:"freeze_strategy_link"`
		OweTheProcessDescLink          string `conf:"owe_the_process_desc_link"`          // 新版欠费流程链接
		FreezeAndThawLink              string `conf:"freeze_and_thaw_link"`               // 账户冻结与解冻链接
		FreezeDescLink                 string `conf:"freeze_desc_link"`                   // 冻结服务解释链接
		ViolationInfoStandardsLink     string `conf:"violation_info_standards_link"`      // 违规信息处罚标准链接
		ViolationSelfCheckTemplateLink string `conf:"violation_self_check_template_link"` // 违规自检模板链接

		// su api urls
		SuPortalSignInLink string `conf:"su_portal_signin_link"`
		SuEVMSignInLink    string `conf:"su_evm_signin_link"`

		FinancialOverviewLink string `conf:"financial_overview_link"`
		IPIP                  string `conf:"ipip"`
	} `conf:"host"`

	Service struct {
		AdminUserName       string `conf:"admin_username"`
		AdminPassword       string `conf:"admin_password"`
		AdminClientId       string `conf:"admin_client_id"`
		AdminClientSecret   string `conf:"admin_client_secret"`
		EmailActivateSecret string `conf:"email_activate_secret"`
		BoAdminUserName     string `conf:"bo_admin_username"`
		BoAdminPassword     string `conf:"bo_admin_password"`
		ACCOUNT             string `conf:"account_host"`
		ONE                 string `conf:"one_host"`
		UC                  string `conf:"uc_host"`
		WALLET              string `conf:"wallet_host"`
		WALLETBIZ           string `conf:"walletbiz_host"`
		PRICE               string `conf:"price_host"`
		PRODUCT             string `conf:"product_host"`
		NOTIFICATION        string `conf:"message_host"` // deprecated, bo-notification host
		TRADE               string `conf:"trade_host"`
		GAEA                string `conf:"gaea_host"`
		IAM                 string `conf:"iam_host"`
		FUSION              string `conf:"fusion_host"`
		FUSION_ADMIN        string `conf:"fusion_admin_host"`
		FUSION_ACCOUNT      string `conf:"fusion_account"`
		FUSIONEC            string `conf:"fusionec_host"`
		FUSION_STAT_HOST    string `conf:"fusion_stat_host"`
		FUSION_TRAFFIC_HOST string `conf:"fusion_traffic_host"`
		KODO_STAT_HOST      string `conf:"kodo_stat_host"`
		PILI_STAT_HOST      string `conf:"pili_stat_host"`
		UFOP_STAT_HOST      string `conf:"ufop_stat_host"`
		DORA_STAT_HOST      string `conf:"dora_stat_host"`
		SF_CRM_HOST         string `conf:"sf_crm_host"`
		PORTAL_IO_HOST      string `conf:"portal_io_host"`
		TS_HOST             string `conf:"ts_host"`
		QVM_HOST            string `conf:"qvm_host"`
		MEASURE_HOST        string `conf:"measure_host"`
		QBPM_HOST           string `conf:"qbpm_host"`
		CREDIT_HOST         string `conf:"credit_host"`
		SMS_HOST            string `conf:"sms_host"`
		DW_IBAPP_HOST       string `conf:"dw_ibapp_host"`
		DW_IBSYNC_HOST      string `conf:"dw_ibsync_host"`

		// rs_host, pub_host
		Qiniu_Rs_Host  string `conf:"rs_host"`
		Qiniu_Pub_Host string `conf:"pub_host"`

		PayCouponServiceHost string `conf:"pay_coupon_service_host"`

		RespackServiceHost string `conf:"respack_service_host"`
		WALLET_V4_HOST     string `conf:"wallet_v4_host"`
		DICT_V4_HOST       string `conf:"dict_v4_host"`
		Price_V4_HOST      string `conf:"price_v4_host"`
		UserCenterHost     string `conf:"user_center_host"`
		OrganizationHost   string `conf:"organization_host"`
		PronoeHost         string `conf:"pronoe_host"`
	} `conf:"service"`

	GrpcClientKeepalive keepalive.ClientParameters `conf:"grpc_client_keepalive"`

	DB struct {
		BopGaeaHost       string        `conf:"bop_gaea.host"`
		GaeaDatabase      string        `conf:"gaea.database"`
		GaeaAdminDatabase string        `conf:"gaea_admin.database"`
		MarketingDatabase string        `conf:"marketing.database"`
		ConnTimeout       time.Duration `conf:"conn_timeout"`
	} `conf:"mongo"`

	Rank struct {
		RankSource string `conf:"rank_source"`
	} `conf:"rank"`

	Insight struct {
		// 配置文件
		FilePath string `conf:"file_path"`

		// Metabase
		MetabaseHost string `conf:"metabase_host"`
		MetaBaseKey  string `conf:"metabase_key"`

		// GuanBI
		GuanBIHost       string `conf:"guanbi_host"`
		GuanBIPrivateKey string `conf:"guanbi_private_key"`
	} `conf:"insight"`

	ES struct {
		HOST  string `conf:"es.host"`
		Index string `conf:"es.index"`
	} `conf:"elasticsearch"`

	Redis RedisConfig `conf:"redis"`

	Cache CacheConfig `conf:"cache"`

	Eventbus struct {
		URL           string `conf:"url"`
		HeartBeat     int    `conf:"heart_beat"`
		RetryTimes    int    `conf:"retry_times"`
		RetryInterval int    `conf:"retry_interval"`
		Tls           bool   `conf:"tls"`
	} `conf:"eventbus"`

	DeveloperIdentity struct {
		DefaultReviewer                     string  `conf:"default_reviewer"`
		Reviewers                           string  `conf:"reviewers"`
		Bucket                              string  `conf:"bucket"`
		BucketAccessKey                     string  `conf:"bucket_access_key"`
		BucketSecretKey                     string  `conf:"bucket_secret_key"`
		BucketDomain                        string  `conf:"bucket_domain"`
		BucketUpHost                        string  `conf:"bucket_up_host"`
		ExpiredIn                           int64   `conf:"expired_in"`
		SizeLimit                           int64   `conf:"size_limit"`
		AlipayUidWhiteList                  string  `conf:"alipay_uid_white_list"`
		BankTransferQiniuAccount            string  `conf:"bank_transfer_qiniu_account"`
		OCRAutoFlip                         bool    `conf:"ocr_auto_flip"`
		MinFaceSimilarity                   float32 `conf:"min_face_similarity"`        // 人脸核验相似度阈值，如 90.5
		EnterpriseCodeWhitelist             string  `conf:"enterprise_code_whitelist"`  // 企业白名单，用于开放企业认证账号数量
		MaxRecordsPerEnterprise             int     `conf:"max_records_per_enterprise"` // 企业最多认证数量，不配置，默认为 3
		EnterpriseStatus                    string  `conf:"enterprise_status"`
		MaxRecordsPerEnterpriseForWhitelist int     `conf:"max_records_per_enterprise_for_whitelist"` // 企业白名单内企业最多认证数量，不配置，默认为10
	} `conf:"developer_identity"`

	// Billing 出账相关设置
	Billing struct {
		// EnforceMonthClosed 启用关账状态 API。为假则 IsMonthClosed 响应恒为假
		EnforceMonthClosed bool `conf:"enforce_month_closed"`
	} `conf:"billing"`

	Invoice struct {
		Bucket          string `conf:"bucket"`
		BucketAccessKey string `conf:"bucket_access_key"`
		BucketSecretKey string `conf:"bucket_secret_key"`
		BucketDomain    string `conf:"bucket_domain"`
	} `conf:"invoice"`

	PDFBill struct {
		Bucket                    string `conf:"bucket"`
		BucketAccessKey           string `conf:"bucket_access_key"`
		BucketSecretKey           string `conf:"bucket_secret_key"`
		BucketDomain              string `conf:"bucket_domain"`
		CreatePDFConcurrencyLimit int    `conf:"create_pdf_concurrency_limit"`
	} `conf:"pdfbill"`

	QbpmAttachment struct {
		Bucket          string `conf:"bucket"`
		BucketAccessKey string `conf:"bucket_access_key"`
		BucketSecretKey string `conf:"bucket_secret_key"`
		BucketDomain    string `conf:"bucket_domain"`
	} `conf:"qbpm_attachment"`

	UrlSafe struct {
		TencentPassword string `conf:"tencent_password"`
		ArchiveBucket   string `conf:"archive_bucket"`
		ArchiveAK       string `conf:"archive_ak"`
		ArchiveSK       string `conf:"archive_sk"`
		ReplaceSrc      string `conf:"replace_src"`
	} `conf:"urlsafe"`

	Pili struct {
		PiliView string `conf:"pili_view"`
	} `conf:"pili"`

	Pandora struct {
		PandoraView string `conf:"pandora_view"`
	} `conf:"pandora"`

	Workwx struct {
		CorpID     string `conf:"corpid"`
		CorpSecret string `conf:"corpsecret"`
		AgentID    int64  `conf:"agentid"`

		ChatIDGenBills string `conf:"chatid_gen_bills"`
		ChatIDWithdraw string `conf:"chatid_withdraw"`
	} `conf:"workwx"`

	// Fadada 银企直连电子印章的法大大接口配置
	Fadada struct {
		BaseURL    string `conf:"base_url"`
		TemplateID string `conf:"template_id"`
		CustomerID string `conf:"customer_id"`
		// 不透明的签章
		SignatureID string `conf:"signature_id"`
		// 半透明的签章(50%透明度)
		TranslucentSignatureID string `conf:"translucent_signature_id"`
	} `conf:"fadada"`

	AuditLog struct {
		LogDir     string `conf:"log_dir"`
		ChunkBits  uint   `conf:"chunk_bits"`
		BodyLimit  int    `conf:"body_limit"`
		ModuleName string `conf:"module_name"`
	} `conf:"audit_log"`

	Data struct {
		Host   string `conf:"host"`
		Id     string `conf:"id"`
		Secret string `conf:"secret"`
	} `conf:"data"`

	CronSpecs struct {
		SyncUserProduct                  string `conf:"sync_user_product"`
		SyncEsDeveloperInfoUpdate        string `conf:"sync_es_developer_info_update"`
		ClearBucketNotify                string `conf:"clear_bucket_notify"`
		MonthlySalesNotify               string `conf:"monthly_sales_notify"`
		SyncQuerySPDBTransferResult      string `conf:"sync_query_spdb_transfer_result"`
		SPDBCheckBalance                 string `conf:"spdb_check_balance"`
		NotCertifiedForThreeDaysNotify   string `conf:"not_certified_for_three_days_notify"`
		BankTransferPendingConfirmNotify string `conf:"bank_transfer_pending_confirm_notify"`
	} `conf:"cron_specs"`

	Dora struct {
		AccessKey                 string `conf:"access_key"`
		SecretKey                 string `conf:"secret_key"`
		DoraControllerHost        string `conf:"dora_controller_host"`
		ApplicationControllerHost string `conf:"application_controller_host"`
		IDCardOcrHost             string `conf:"id_card_ocr_host"`
		BusinessOcrHost           string `conf:"business_ocr_host"`
		FaceDetectHost            string `conf:"face_detect_host"`
		FaceCompareAuthHost       string `conf:"face_compare_auth_host"`
		FaceActionLiveHost        string `conf:"face_action_live_host"`
		FourMetaCheckUrl          string `conf:"four_meta_check_url"`
		FreezeSK                  string `conf:"freeze_sk"`
		Email                     string `conf:"email"`
	} `conf:"dora"`

	Freeze struct {
		FreezeOemParentsWhiteList string `conf:"freeze_oem_parents_white_list"`
	} `conf:"freeze"`

	SSO struct {
		Host     string `conf:"host"`
		ClientID string `conf:"client_id"`
	} `conf:"sso"`

	Feature map[string][]Feature

	Mysql struct {
		GaeaAdminMysqlDSN string `conf:"gaea_admin_mysql_dsn"`
	} `conf:"mysql"`

	Top2000 struct {
		Source string `conf:"source"`
	} `conf:"top2000_source"`

	Email struct {
		PortalEmail string `conf:"portal_email"`
	} `conf:"email"`

	Tencent struct {
		SecretID    string `conf:"secret_id"`
		SecretKey   string `conf:"secret_key"`
		OCREndpoint string `conf:"ocr_endpoint"`
	} `conf:"tencent"`

	Baidu struct {
		GrantType         string `conf:"grant_type"`
		SecretID          string `conf:"secret_id"`
		SecretKey         string `conf:"secret_key"`
		ClientPrefix      string `conf:"client_prefix"`
		TokenPrefix       string `conf:"token_prefix"`
		FourFactorsEnable bool   `conf:"four_factors_enable"`
	} `conf:"baidu"`

	BufferedTime struct {
		EnterpriseBufferedTime    int64 `conf:"enterprise_buffered_time"`
		PersonalBufferedTime      int64 `conf:"personal_buffered_time"`
		NotIdentifiedBufferedTime int64 `conf:"not_identified_buffered_time"`
	} `conf:"buffered_time"`

	ElectricInvoice struct {
		AppID  string `conf:"appid"`
		Secret string `conf:"secret"`
		// Yhdm 用户代码
		Yhdm string `conf:"yhdm"`
		// Kpzddm 开票终端代码
		Kpzddm string `conf:"kpzddm"`

		// 申请开具电子发票的第三方接口
		CreateInvoiceUrl string `conf:"create_invoice_url"`
		// 获取已开票的电子发票pdf链接的第三方接口
		FetchInvoiceUrl string `conf:"fetch_invoice_url"`
		// 注册码（由服务商提供）
		AppKey string `conf:"app_key"`
		// 销售方纳税人识别号
		SellerTaxpayerIdentityNumber string `conf:"seller_taxpayer_identity_number"`
		// 销售方名称
		SellerName string `conf:"seller_name"`
		// 销售方地址、电话
		SellerAddressPhone string `conf:"seller_address_phone"`
		// 销售方银行名称及账号
		SellerBankAccount string `conf:"seller_bank_account"`
		// 商品编码（信息技术服务费的商品编码）
		CommodityCode string `conf:"commodity_code"`
		// 收款人
		Payee string `conf:"payee"`
		// 复核人
		Reviewer string `conf:"reviewer"`
		// 开票人
		Drawer string `conf:"drawer"`
		// 第三方接口能够承受的 qps 值
		QPSLimit float64 `conf:"qps_limit"`
	} `conf:"electric_invoice"`

	SPDB struct {
		// 浦发银行辅账号余额不足 & 打款失败，提醒用户列表
		BalanceInsufficientReminders string `conf:"balance_insufficient_reminders"`
		// 浦发银行辅账号余额不足阈值（1000元），提醒用户列表
		BalanceThresholdReminders string `conf:"balance_threshold_reminders"`
		// 银行转账认证多次打款（大于 3 次）, 提醒用户列表
		IdentityThresholdReminders string `conf:"identity_threshold_reminders"`
		// TransferAccount 七牛付款账号（财务称呼为辅账号，为了便于区分出账和入账，该账号仅用于对外付款）
		TransferAccount string `conf:"transfer_account"`
	} `conf:"spdb"`

	ProtectCaptcha struct {
		MaxRetry  int `conf:"max_retry"`  // 需要验证码的最大错误次数
		BlockTime int `conf:"block_time"` // 触发验证码后，需要的解封时间(分钟)
	} `conf:"protect_captcha"`

	Captcha struct {
		MaxRetry  int `conf:"max_retry"`  // 需要验证码的最大错误次数
		BlockTime int `conf:"block_time"` // 触发验证码后，需要的解封时间(小时)

		LocalImageWidth      int `conf:"local_image_width"`
		LocalImageHeight     int `conf:"local_image_height"`
		LocalImageCharNumber int `conf:"local_image_char_number"` // 字符个数
		LocalImageExpiredAt  int `conf:"local_image_expired_at"`  // 图片验证码失效时间（秒）
		LocalImageWeight     int `conf:"local_image_weight"`      // 本地图片验证码权重

		SmsCaptchaExpired int `conf:"sms_captcha_expired"` // 手机验证码过期时间
	} `conf:"captcha"`

	Paypal struct {
		ClientID string `conf:"client_id"`
		Secret   string `conf:"secret"`
		BaseUrl  string `conf:"base_url"`
	} `conf:"paypal"`

	Alipay struct {
		AppID      string `conf:"app_id"`
		BaseUrl    string `conf:"base_url"`
		PrivateKey string `conf:"qiniu_private_key"`
		PublicKey  string `conf:"alipay_public_key"`
	} `conf:"alipay"`

	EBank struct {
		AppID      string `conf:"app_id"`
		BaseUrl    string `conf:"base_url"`
		PrivateKey string `conf:"qiniu_private_key"`
		PublicKey  string `conf:"alipay_public_key"`
	} `conf:"ebank"`

	WxPay struct {
		AppId           string `conf:"app_id"`
		MchId           string `conf:"mch_id"`
		Key             string `conf:"key"`
		CASerialNumber  string `conf:"ca_serial_number"`
		PrivateKeyPath  string `conf:"private_key"`
		NotifyUrl       string `conf:"notify_url"`
		RefundNotifyUrl string `conf:"refund_notify_url"`
	} `conf:"wxpay"`

	Signup struct {
		NotifyEnabled         bool   `conf:"notify_enabled"`
		NotifyChannelID       string `conf:"notify_channel_id"`
		NotifyTemplateID      string `conf:"notify_template_id"`
		BaiDuSignUpEventToken string `conf:"baidu_signup_event_token"`
	} `conf:"signup"`

	StudentCertify struct {
		NotifyChannelID  string `conf:"notify_channel_id"`
		NotifyTemplateID string `conf:"notify_template_id"`
		CouponExcodes    string `conf:"coupon_excodes"`
	} `conf:"student_certify"`

	BillValidation struct {
		GeneralValidationTable string `conf:"general_validation_table"`
		GeneralValidationToken string `conf:"general_validation_token"`
		UserLargeDiffTable     string `conf:"user_large_diff_table"`
		UserLargeDiffToken     string `conf:"user_large_diff_token"`
		NewAndGoneUsersTable   string `conf:"new_and_gone_users_table"`
		NewAndGoneUsersToken   string `conf:"new_and_gone_users_token"`
		NewUserDetailsTable    string `conf:"new_user_details_table"`
		NewUserDetailsToken    string `conf:"new_user_details_token"`
		GoneUserDetailsTable   string `conf:"gone_user_details_table"`
		GoneUserDetailsToken   string `conf:"gone_user_details_token"`
	} `conf:"bill_validation"`

	Unregister struct {
		EnableRefreshDummyBill bool `conf:"enable_refresh_dummy_bill"`
	} `conf:"unregister"`

	Tracing struct {
		Enable                     bool          `conf:"enable"`                        // 是否开启 tracing
		ServiceName                string        `conf:"service_name"`                  // 服务名称，为空则默认取当前应用可执行文件名称
		SamplingType               string        `conf:"sampling_type"`                 // 采样类型
		SamplingParam              float64       `conf:"sampling_param"`                // 采样参数
		SamplingRefreshInterval    time.Duration `conf:"sampling_refresh_interval"`     // 采样刷新间隔
		SamplingServerURL          string        `conf:"sampling_server_url"`           // 采样策略服务地址
		ReporterLocalAgentEndpoint string        `conf:"reporter_local_agent_endpoint"` // 采样上报本地 agent 地址
		ReporterCollectorEndpoint  string        `conf:"reporter_collector_endpoint"`   // 采样数据收集器地址
	} `conf:"tracing"`

	Lock struct {
		SyncEsDeveloperLockKey string `conf:"sync_es_developer_lock_key"`
	} `conf:"lock"`

	Notification struct {
		MorseClientID      string `conf:"morse_client_id"`
		MorseHost          string `conf:"morse_host"`
		NotificationV2Host string `conf:"notification_v2_host"`
	} `conf:"notification"`

	NotCertifiedForThreeDaysNotify struct {
		NotifyChannelID  string `conf:"notify_channel_id"`
		NotifyTemplateID string `conf:"notify_template_id"`
	} `conf:"not_certified_for_three_days_notify"`
}

type RedisConfig struct {
	Addrs      string `conf:"addrs"`
	DB         int    `conf:"db"`
	Password   string `conf:"password"`
	MasterName string `conf:"master_name"`
}

type Feature struct {
	FeatureId string `json:"featureId"`
	Summary   string `json:"summary"`
	Disable   bool   `json:"disable"`
	Index     int    `json:"index"`
	Refer     string `json:"refer"`
}

type CacheConfig struct {
	EnableFreezeCache bool `conf:"enable_freeze_cache"`
}

const (
	ProxyHeaderUidKey   string = "X-QINIU-ADMIN-UID"
	ProxyHeaderEmailKey string = "X-QINIU-ADMIN-EMAIL"
	VendorHeaderKey     string = "X-Vendor"
)
