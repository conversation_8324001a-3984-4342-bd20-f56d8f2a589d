package account

import (
	"github.com/qbox/pay-sdk/base/oauth"
	"github.com/teapots/inject"
	"github.com/teapots/teapot"
	"qbox.us/biz/services.v2/account"
)

func AdminAccountService(host string) interface{} {
	return inject.Provide{
		inject.Dep{0: "admin"},
		func(adminOAuth *oauth.Transport, log teapot.ReqLogger) (service account.AdminAccountService) {
			service = account.NewAdminAccountService(host, adminOAuth, log)
			return service
		},
	}
}
