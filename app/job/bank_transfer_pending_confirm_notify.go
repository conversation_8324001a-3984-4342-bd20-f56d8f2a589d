package job

import (
	"context"
	"fmt"
	"time"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/sync/resultgroup"

	"qiniu.io/gaea/app/models"
	"qiniu.io/gaea/app/services/workwx"

	"github.com/qbox/pay-sdk/wallet"

	"github.com/go-redis/redis/v8"
	"github.com/qbox/bo-base/v4/lock"
	"qiniu.io/gaea/app/env/global"

	"github.com/teapots/teapot"
)

const lockKeyBankTransferPendingConfirmNotify = "lock:key:bank:transfer:pending:confirm:notify"

func bankTransferPendingConfirmNotify(ctx context.Context, tea *teapot.Teapot) {

	if global.Env.Intl.IntlConfig().IsSufy() {
		return
	}
	var (
		redisClient     redis.UniversalClient
		walletV4Service wallet.PayWalletServiceClient
		model           *models.ModelMgr
		workwxService   workwx.WorkwxService
	)
	if err := tea.Injector().Find(&redisClient, ""); err != nil {
		tea.Logger().Errorf(
			"bankTransferPendingConfirmNotify cannot find redisClient: %s", err,
		)
		return
	}

	locker := lock.NewRedisLocker(
		redisClient,
		lock.WithExpiry(time.Minute),
		lock.WithSpinTimes(1),
		lock.WithSpinInterval(time.Second),
		lock.WithAutoRenewal(true),
	)
	locked := locker.Lock(lockKeyBankTransferPendingConfirmNotify)
	if !locked {
		tea.Logger().Warnf("bankTransferPendingConfirmNotify get lock failed")
		return
	}
	defer locker.Unlock(lockKeyBankTransferPendingConfirmNotify)

	if err := tea.Injector().Find(&walletV4Service, ""); err != nil {
		tea.Logger().Errorf(
			"bankTransferPendingConfirmNotify cannot find payV4Wallet: %s", err,
		)
		return
	}
	if err := tea.Injector().Find(&model, ""); err != nil {
		tea.Logger().Errorf(
			"bankTransferPendingConfirmNotify cannot find modelMgr: %s", err,
		)
		return
	}
	page := 1
	pendingConfirmRecords := make([]*wallet.BankTransfer, 0)
	for {
		res, err := walletV4Service.ListBankTransfer(ctx, &wallet.BankTransferListParam{
			Status:   wallet.BankTransferStatusPendingConfirmation,
			Page:     1,
			PageSize: 200,
		})
		if err != nil {
			tea.Logger().Errorf(
				"bankTransferPendingConfirmNotify list bankTransfers failed: %s", err,
			)
			return
		}
		if len(res.GetData()) == 0 {
			break
		}
		pendingConfirmRecords = append(pendingConfirmRecords, res.GetData()...)
		page++
	}
	// 查销售归属
	uids := make([]uint64, len(pendingConfirmRecords))
	for _, record := range pendingConfirmRecords {
		if record.GetUid() > 0 {
			uids = append(uids, record.GetUid())
		}
	}
	uids = base.UniqueIntSlice(uids)
	if len(uids) == 0 {
		return
	}
	type UIDSale struct {
		Email string
		UID   uint64
	}
	userSales, _ := resultgroup.ParallelMap(
		uids,
		func(uid uint64) (UIDSale, error) {
			sales, err := model.GetSalesByUid(uint32(uid))
			if err != nil {
				// 报错认为无销售归属, 判给 salesOperation
				return UIDSale{
					UID:   uid,
					Email: global.Env.Email.SalesOperationEmail,
				}, nil
			}
			return UIDSale{
				Email: sales.Email,
				UID:   uid,
			}, nil
		},
	)
	notifiers := make(map[string][]uint64)
	for _, sales := range userSales {
		if _, ok := notifiers[sales.Email]; !ok {
			notifiers[sales.Email] = make([]uint64, 0)
		}
		notifiers[sales.Email] = append(notifiers[sales.Email], sales.UID)
	}
	salesPageLink := fmt.Sprintf(
		"%s/bo/financial/bank-transfer/confirmation?status=2",
		global.Env.Service.PORTAL_IO_HOST,
	)
	salesOperationPageLink := fmt.Sprintf(
		"%s/bo/financial/bank-transfer/admin?status=2",
		global.Env.Service.PORTAL_IO_HOST,
	)
	// 发企微和邮件通知
	const workwxTemplate = `**您有 ` + "`%d`" + ` 个客户的银行转账待确认，请及时确认**
>
>
>[点此查看](%s)
`
	for email, users := range notifiers {
		content := fmt.Sprintf(
			workwxTemplate,
			len(users),
			func() string {
				if email == global.Env.Email.SalesOperationEmail {
					return salesOperationPageLink
				}
				return salesPageLink
			}(),
		)
		workwxService.SendMarkdownToUsers([]string{}, content, false)
	}
}
