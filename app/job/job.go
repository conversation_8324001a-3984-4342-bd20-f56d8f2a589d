package job

import (
	"context"

	"github.com/robfig/cron"
	"github.com/teapots/teapot"
	"qiniu.io/gaea/app/env/global"
)

const (
	cronJobSkipSpec = "-"
)

type cronJobSetting struct {
	Name string
	Spec string
	Cmd  func(ctx context.Context, tea *teapot.Teapot)
}

func CronJob(tea *teapot.Teapot) {
	ctx := context.Background()
	// cron job
	c := cron.New()

	settings := []cronJobSetting{
		{
			// sync developer info to es
			Name: "Sync ES Developer Info Update",
			Spec: global.Env.CronSpecs.SyncEsDeveloperInfoUpdate,
			Cmd:  syncEsDeveloperInfoUpdate,
		},
		{
			// send monthly notify to sales
			Name: "Monthly Sales Notify",
			Spec: global.Env.CronSpecs.MonthlySalesNotify,
			Cmd:  monthlySalesNotify,
		},
		{
			// send clear bucket notification to users
			Name: "Clear Bucket Notify",
			Spec: global.Env.CronSpecs.ClearBucketNotify,
			Cmd:  clearBucketNotify,
		},
		{
			Name: "developer identity bank transfer crontab",
			Spec: global.Env.CronSpecs.SyncQuerySPDBTransferResult,
			Cmd:  spdbTransferResultAndUpdateStatus,
		},
		{
			Name: "check spdb bank balance",
			Spec: global.Env.CronSpecs.SPDBCheckBalance,
			Cmd:  spdbCheckBalance,
		},
		{
			Name: "Not Certified For Three Days Notify",
			Spec: global.Env.CronSpecs.NotCertifiedForThreeDaysNotify,
			Cmd:  notCertifiedForThreeDaysNotify,
		},
		{
			Name: "Bank Transfer",
			Spec: global.Env.CronSpecs.NotCertifiedForThreeDaysNotify,
			Cmd:  notCertifiedForThreeDaysNotify,
		},
	}

	logger := tea.Logger()
	for _, setting := range settings {
		if setting.Spec == cronJobSkipSpec {
			logger.Infof("<cron job> Skip cron job %q explicitly", setting.Name)
			continue
		}

		_, err := cron.Parse(setting.Spec)
		if err != nil {
			logger.Errorf("<cron job> Bad cron job spec %q for job %q, err: %s", setting.Spec, setting.Name, err)
			continue
		}

		cmdFunc := setting.Cmd
		err = c.AddFunc(setting.Spec, func() {
			cmdFunc(ctx, tea)
		})
		if err != nil {
			logger.Errorf("<cron job> Register cron job %q failed: %s", setting.Name, err)
			continue
		}

		logger.Infof("<cron job> Register cron job %q with spec %q", setting.Name, setting.Spec)
	}

	c.Start()
}

func DeamonJob(tea *teapot.Teapot) {
	//go freezeDaemonV2(tea)
	go eventBusDaemon(tea)

	// 这个会自己起 goroutine，不用帮它
	spawnWorkwxTokenRefresher(tea)
}
