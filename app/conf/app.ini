run_mode = dev
http_addr = 0.0.0.0
http_port = 9013
# run_mode = prod 时是否打开 /debug/pprof 系列 API
# 处于其他 run_mode 时，无条件开启
enable_pprof = false

templates_path = ../../../../../templates/views
views_path = ./views

enable_totp_rate_limit = false

# 清理空间通知多少天前被冻结的用户, 支持多个时间点, 以逗号分隔
clear_bucket_notify_days = 25

# 测试手机号，跳过绑定手机号次数限制，多个用逗号分隔
test_mobile =

[intl]
ref_timezone_name = CST
# 8*3600
ref_timezone_offset = 28800
default_lang_code = zh

[host]
portal_frontend = http://localhost:3000
support_link = https://support.qiniu.com
unsubscribe = https://portal.qiniu.com/user/subscribe
identity_link = https://portal.qiniu.com/identity
recharge_link = https://portal.qiniu.com/financial/recharge
freeze_policy_link = https://developer.qiniu.com/af/kb/1489
su_portal_signin_link = http://portalv4.jfcs.qiniu.io/api/gaea/su/signin
su_evm_signin_link = http://evm.staging.qiniu.io/api/admin/su
financial_overview_link = https://portal.qiniu.com/financial/overview
ipip = http://bo-staging-ipip.jfcs-k8s-qa1.qiniu.io
owe_the_process_desc_link = https://developer.qiniu.com/af/kb/5996/new-owe-the-process-description
freeze_and_thaw_link = https://developer.qiniu.com/af/kb/1489/freeze-and-thaw
freeze_desc_link = https://developer.qiniu.com/af/manual/4162/freeze-service
violation_info_standards_link = https://developer.qiniu.com/af/7736/violation-of-information-standards
violation_self_check_template_link = https://dn-market-edm.qbox.me/documentation.docx

[service]
admin_username = root
admin_password = root
admin_client_id = 4_odedBxmrAHiu4Y0Qp0HPG0NANCf6VAsAjWL_k9
admin_client_secret = SrRuUVfDX6drVRvpyN8mv8Vcm9XnMZzlbDfvVfMe

email_activate_secret = portal.qiniu.com

bo_admin_username = root
bo_admin_password = root

account_host = http://localhost:9100
one_host = http://kodo-dev.one.jfcs-k8s-qa2.qiniu.io
uc_host = http://kodo-dev.bucket.jfcs-k8s-qa2.qiniu.io
wallet_host = http://127.0.0.1:9520
measure_host = 127.0.0.1:9702
walletbiz_host = http://127.0.0.1:9519
price_host = http://127.0.0.1:19521
product_host = http://127.0.0.1:9300
message_host = http://127.0.0.1:9012
fusion_host = http://fusiondomain.qcdn-sophon-sufy.jfcs-k8s-qa1.qiniu.io
fusionec_host = http://************:55550,http://************:55550
fusion_stat_host = http://127.0.0.1:9509/fusion
fusion_traffic_host = https://easy-mock.com/mock/5d36e6ec4751a47657a61508/api/v1
bi_stat_host = http://127.0.0.1:9509
kodo_stat_host = http://127.0.0.1:9509/kodo
pili_stat_host = http://127.0.0.1:9509/pili
ufop_stat_host = http://127.0.0.1:9509/ufop
dora_stat_host = http://127.0.0.1:9509/dora
sf_crm_host = http://127.0.0.1:3000/api
portal_io_host = http://bo-staging-admin.jfcs-k8s-qa1.qiniu.io
trade_host = http://127.0.0.1:9522
# gaea_host 使用本地即可，避免远程网络请求, 端口和 http_port 保持一致
gaea_host = http://127.0.0.1:9013
wallet_v4_host = 127.0.0.1:9703
dict_v4_host = 127.0.0.1:9701
credit_host = 127.0.0.1:9801
price_v4_host = 127.0.0.1:9704

rs_host = http://kodo-dev.rspub.jfcs-k8s-qa2.qiniu.io
pub_host = http://************:30035
ts_host = https://ts-dev.qiniu.io
qvm_host = http://bo3rd5nmniiw.kegate-xs.cloudappl.com
qbpm_host = http://127.0.0.1:6013
organization_host = 127.0.0.1:18003

pay_coupon_service_host = 127.0.0.1:9703
respack_service_host = 127.0.0.1:9902
user_center_host = 127.0.0.1:18001
pronoe_host = 127.0.0.1:10083

[elasticsearch]
es.host = http://************:9202,http://************:9202

es.index = gaea-admin-developer

[mongo]
bop_gaea.host = mongodb://127.0.0.1:27017/?connectionTimeoutMS=10000&maxPoolSize=2048&minPoolSize=1024&maxIdleTimeMS=300000
gaea.database = gaea
gaea_admin.database = gaea_admin
marketing.database = marketing
conn_timeout = 3s

[redis]
addrs = localhost:6379
#master_name = redis-bop

[eventbus]
url = amqp://bo:bo@localhost:5672/
heart_beat = 5
retry_times = 3
retry_interval = 500
tls = false

[email]
portal_email =
sales_operation_email =

[developer_identity]
default_reviewer = 系统
reviewers = 系统
#email:<EMAIL>
bucket = identity-uploadfile
bucket_access_key = GB9ikSIdYaiqfR2QQzq5saxd8VU-1qrQgu6kdU9V
bucket_secret_key = ozVGHIcKOFO1G-UJR8fLVOQpgiJWb39UWFEUAlS7
bucket_domain = oav3y6wmu.bkt.clouddn.com
bucket_up_host = up.qiniu.com
expired_in = 1440 # 1天，单位：分钟
size_limit = 5 # 5M, 单位: M
alipay_uid_white_list = ****************
bank_transfer_qiniu_account = *****************
enterprise_status = 存续,在营,开业,在册,正常经营,登记成立,在业,正常,经营,在营在册,有效,在业再册,登记,确立
max_records_per_enterprise = 1000

[urlsafe]
#账号 <EMAIL>/qatest
tencent_password = test
archive_bucket = tencent-archive
archive_ak = GB9ikSIdYaiqfR2QQzq5saxd8VU-1qrQgu6kdU9V
archive_sk = ozVGHIcKOFO1G-UJR8fLVOQpgiJWb39UWFEUAlS7

[pili]
pili_view =

[paypal]
client_id = ATW2iesCKBDgMxrWVGqpBYIKCcvLHymMN1gBUlNQEpxwCSW29CxQZ2tcfE-sUPwcM_CJTrc27mf63LPf
secret = EG0OZtx6luFSkDDY-a1yEGohFJpHbYKWmglsD5iULKY7Py2T9Y5hl9nSKKCBCDEBAwPglrZXhiXrSD0l
base_url = https://api.sandbox.paypal.com

[pandora]
pandora_view =

[billing]
enforce_month_closed = false

[workwx]
corpid = wweebfaa2b51a28243
corpsecret = 4raREcK16AZ26pRMdNP_51Ax0zV_dNvQzB8_MiKHb3k
agentid = 1000017
chatid_gen_bills = billingalerttest
chatid_withdraw = withdrawalerttest

[fadada]
base_url = http://bo-fadada.jfcs.qiniu.io/sdk_client_server
template_id = qiniu_fadada_test_template_id
customer_id = 169DD743DED8CE932C220615692536F7
signature_id = 5042719
translucent_signature_id = 5170259

[alipay]
app_id = ****************
base_url = https://openapi-sandbox.dl.alipaydev.com
qiniu_private_key=conf/qiniu.alipay.private
alipay_public_key=conf/alipay.pub

# TODO modify
[ebank]
app_id = ****************
base_url = https://openapi-sandbox.dl.alipaydev.com
qiniu_private_key=conf/qiniu.ebank.alipay.private
alipay_public_key=conf/ebank.alipay.pub

[audit_log]
log_dir = ./run/log/auditlog/gaea-admin
chunk_bits = 29
body_limit = 256
module_name = GAEA_ADMIN

[invoice]
bucket = invoice-certificate
bucket_access_key = GB9ikSIdYaiqfR2QQzq5saxd8VU-1qrQgu6kdU9V
bucket_secret_key = ozVGHIcKOFO1G-UJR8fLVOQpgiJWb39UWFEUAlS7
bucket_domain = oavapy56p.bkt.clouddn.com

[qbpm_attachment]
bucket = bo-biz-dev
bucket_access_key = KfY9Gu2yX4SicLTPvxxI7n3otnzNy627DvFM-EXf
bucket_secret_key = LEjviutEmhZYSM7KGsfy7oSQZrQpFXU4YV0tkel-
bucket_domain = s3c2mkbo7.bkt.clouddn.com

[pdfbill]
bucket = bills-zip
bucket_access_key = DMmrsKZ4OLPKYSunCrup5xI8DmYr4mkMB62i7kl5
bucket_secret_key = C_DNWMJ_3-usIVSvFJ-UzxFvbPkeayKGKw9Gz3JB
bucket_domain = http://pjiqnih3a.bkt.clouddn.com/
create_pdf_concurrency_limit = 8

[dora]
access_key = dora-apps
secret_key = dora-apps
freeze_service_host = http://************:3214
id_card_ocr_host = https://ocr-idcard.qiniuapi.com
business_ocr_host =  https://ocr-bs.qiniuapi.com
face_detect_host = https://face-detect.qiniuapi.com
face_compare_auth_host = https://face-hdphotoauth.qiniuapi.com
face_action_live_host = https://face-actlive.qiniuapi.com
four_meta_check_url = https://ap-gate-z0.qiniuapi.com/faceref/fourmeta/check
freeze_sk =
email = <EMAIL>

[freeze]
freeze_oem_parents_white_list = 

[sso]
host = http://sso.jfcs.qiniu.io
client_id =

[cron_specs]
# cron spec grammar: https://github.com/robfig/cron/blob/master/doc.go#L27
# you can disable a job by setting the spec to '-'
sync_user_product = -
sync_es_developer_info_update = -
clear_bucket_notify = -
monthly_sales_notify = -
sync_query_spdb_transfer_result = -
spdb_check_balance = -
bank_transfer_pending_confirm_notify = -

[data]
host =
id =
secret =

[mysql]
gaea_admin_mysql_dsn = root:@tcp(localhost:3306)/gaea_admin?charset=utf8

[top2000_source]
#根据source配置选择top2000客户的数据源，dw为数仓数据源
source = ts_service
#source = dw

[tencent]
secret_id =
secret_key =
ocr_endpoint =


[buffered_time]
# 单位为天，代码中使用时需要*24
enterprise_buffered_time = 5
personal_buffered_time = 3
not_identified_buffered_time = 1

[electric_invoice]
# 申请开具电子发票的第三方接口（百旺电子发票接口没有测试环境）
create_invoice_url = http://connot.reach.host/api/xxgl/v3/tbkp.do
# 获取已开票的电子发票pdf链接的第三方接口
fetch_invoice_url = http://sc.bwfapiao.com/fpserver/FpServlet
# 注册码（由服务商提供）
app_key = 29E68D8A0B9A9C30F240A3D245D7DE8532A32607D27E6AF6366996D01D4D16E71F493075D17812EE2940F9DA9DB6823DC8C72CEB4FC0357652EF93107C72B11FA7CE26F42E50D087B634902AEF33072C
# 销售方纳税人识别号
seller_taxpayer_identity_number = 91310000580583950X
# 销售方名称
seller_name = 上海七牛信息技术有限公司
# 销售方地址、电话
seller_address_phone = 中国（上海）自由贸易试验区博霞路66号1-5层 021-********
# 销售方银行名称及账号
seller_bank_account = 上海浦东发展银行杨浦支行 *****************
# 商品编码（信息技术服务费的商品编码）
commodity_code = 3040203000000000000
# 收款人
payee = 余伟秋
# 复核人
reviewer = 张袁昊
# 开票人
drawer = 张袁昊
# 第三方接口限速，允许 5s 发送一个请求过去
qps_limit = 0.2
appid = appid
secret = secret
kpzddm = kpzddm
yhdm = yhdm

[spdb]
# 浦发银行辅账号余额不足提醒用户列表
balance_insufficient_reminders = <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>
balance_threshold_reminders = <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>
identity_threshold_reminders = <EMAIL>;<EMAIL>;<EMAIL>
[bind_email]
notify_channel_id = bind_email
notify_template_id = bind_email_coupon_notify
coupon_excodes = 1040,1043,1046,1049

[tracing]
enable = false
service_name = gaea-admin
sampling_type = const
sampling_param = 1
sampling_refresh_interval =
sampling_server_url =
reporter_local_agent_endpoint = 127.0.0.1:6831
reporter_collector_endpoint =

[notification]
morse_host = http://bo-staging-morse.jfcs-k8s-qa1.qiniu.io
morse_client_id = 5963399a43c8ce5967000001
notification_v2_host = http://notification-backend.notification-env-default:4050
