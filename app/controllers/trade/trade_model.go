package trade

import (
	"strconv"
	"strings"
	"time"

	"github.com/qbox/bo-base/v4/zone"

	"qbox.us/api/pay/pay"
	v3 "qbox.us/api/pay/price/v3"
	tradeV1 "qbox.us/api/pay/trade.v1"
	"qbox.us/api/pay/trade.v1/enums"
	tradeModels "qbox.us/api/pay/trade.v1/models"
)

var (
	// FixedCSTZone cst zone
	FixedCSTZone = time.FixedZone("CST", 8*3600)
	// ParseFormatLayout layout
	ParseFormatLayout = "2006-01-02"
	// DatetimeLayout datatime layout
	DatetimeLayout = "2006-1-2 15:04:05"
	// RespackEffectTimeLayout respack effect_time layout 每月1号
	RespackEffectTimeLayout = "20060102"
)

// OrderNewInput order new request
type OrderNewInput struct {
	Orders  []tradeV1.ReqProductOrderNew `json:"orders"`
	BuyerId uint32                       `json:"uid"`
	Memo    string                       `json:"memo"`
}

func (i *OrderNewInput) valid() bool {
	if i.BuyerId == 0 || len(i.Orders) == 0 {
		return false
	}

	return true
}

// OrderListInput order list input
type OrderListInput struct {
	UnionOrderHash string    `param:"union_order_hash"`
	PackageOrder   string    `param:"package_order"`
	OrderHash      *string   `param:"order_hash"`
	WithDetail     bool      `param:"with_detail"`
	SellerId       *int64    `param:"seller_id"`
	BuyerId        *uint32   `param:"uid"`
	Status         int       `param:"status"`
	CreateFrom     time.Time `param:"create_from"`
	CreateTo       time.Time `param:"create_to"`
	PayFrom        time.Time `param:"pay_from"`
	PayTo          time.Time `param:"pay_to"`
	Page           int       `param:"page"`
	PageSize       int       `param:"page_size"`
	ProductId      int64     `param:"product_id"`
	PackageIDs     *string   `param:"package_ids"`
	CurrencyType   string    `param:"currency_type"`
	POStatus       int       `param:"po_status"`
	FuzzyName      string    `param:"fuzzy_name"`
	Hidden         *bool     `param:"hidden"`
}

type respackProductPreivew struct {
	// Product        productInput              `json:"product"`
	ExistsRespacks map[string]v3.ModelResPack `json:"exists_respacks"`
	NewRespacks    map[string]v3.ModelResPack `json:"new_respacks"`
}

type productInput struct {
	Email               string                  `json:"email"`
	CategoryID          int64                   `json:"category_id"`
	SettlementMode      int                     `json:"settlement_mode"`
	Name                string                  `json:"name"`
	OriginalPrice       float64                 `json:"original_price"`
	Price               *float64                `json:"price"`
	DollarOriginalPrice int64                   `json:"dollar_original_price"`
	DollarPrice         int64                   `json:"dollar_price"`
	SupportedCurrency   enums.SupportedCurrency `json:"supported_currency"`
	Unit                tradeModels.ProductUnit `json:"unit"`
	Duration            uint                    `json:"duration"`
	BanValet            bool                    `json:"ban_valet"`
	ValetCategory       string                  `json:"valet_category"`
	Status              int                     `json:"status"`
	Description         string                  `json:"description"`
	RespackDesc         string                  `json:"respack_desc"`
	StartTime           *tradeV1.CustomTime     `json:"start_time"`
	EndTime             *tradeV1.CustomTime     `json:"end_time"`
	Model               string                  `json:"model"`
	SPU                 string                  `json:"spu"`
	ExpiresIn           uint64                  `json:"expires_in"`
	IdleStart           string                  `json:"idle_start"`
	IdleEnd             string                  `json:"idle_end"`
}
type respackProductProperty struct {
	Respacks  []respackItem `json:"respacks"`
	Desc      string        `json:"desc"`
	IdleStart string        `json:"idle_start"`
	IdleEnd   string        `json:"idle_end"`
}

type respackItem struct {
	ID          string    `json:"id"`
	Zone        zone.Zone `json:"zone"`
	IsComboItem bool      `json:"is_combo_item"`
}

type respackInput struct {
	respackID       string
	Zone            zone.Zone          `json:"zone"`
	Item            pay.Item           `json:"item"`
	IsComboItem     bool               `json:"is_combo_item"`
	Name            string             `json:"name"`
	Desc            string             `json:"desc"`
	Quota           int64              `json:"quota"`
	CarryOverPolicy v3.CarryOverPolicy `json:"carry_over_policy"`
	EffectTime      time.Time          `json:"effect_time"`
	DeadTime        time.Time          `json:"dead_time"`
}

// RespackProductInput respack product request
type RespackProductInput struct {
	Product  *productInput   `json:"product"`
	Respacks []*respackInput `json:"respacks"`
}

func (input RespackProductInput) validate() bool {
	if input.Product == nil || len(input.Respacks) == 0 {
		return false
	}

	// check product
	if input.Product.Email == "" {
		return false
	}
	if input.Product.Name == "" {
		return false
	}
	if input.Product.Description == "" {
		return false
	}
	if input.Product.RespackDesc == "" {
		return false
	}
	if len(strings.Split(input.Product.RespackDesc, ":")) != 6 {
		return false
	}
	if input.Product.Model == "" {
		return false
	}
	if input.Product.SPU == "" {
		return false
	}
	if input.Product.Duration <= 0 {
		return false
	}
	// check respacks 资源包-商品业务变更成1:1的关系, 自此资源包型商品只允许添加一个资源包
	if len(input.Respacks) != 1 {
		return false
	}
	for _, res := range input.Respacks {
		if res.Item == pay.Item("") {
			return false
		}
		if res.Name == "" {
			return false
		}
		if res.Desc == "" {
			return false
		}
		if res.Quota <= 0 {
			return false
		}
	}
	if input.Product.OriginalPrice < 0 || (input.Product.Price != nil && *input.Product.Price < 0) {
		return false
	}
	if input.Product.DollarPrice < 0 || input.Product.DollarOriginalPrice < 0 {
		return false
	}
	if !input.Product.SupportedCurrency.Valid() {
		return false
	}
	return true
}

type packageListInput struct {
	SellerId          int64                     `param:"seller_id" json:"seller_id"`
	Page              int                       `param:"page" json:"page"`
	PageSize          int                       `param:"page_size" json:"page_size"`
	PackageIds        string                    `param:"package_ids" json:"package_ids"`
	Status            tradeModels.PackageStatus `param:"status" json:"status"`
	PackageName       string                    `param:"package_name" json:"package_name"`
	ProductName       string                    `param:"product_name" json:"product_name"`
	ProductId         int64                     `param:"product_id" json:"product_id"`
	StartFrom         *tradeV1.CustomTime       `param:"start_from" json:"start_from"`
	StartTo           *tradeV1.CustomTime       `param:"start_to" json:"start_to"`
	CreateFrom        *tradeV1.CustomTime       `param:"create_from" json:"create_from"`
	CreateTo          *tradeV1.CustomTime       `param:"create_to" json:"create_to"`
	SupportedCurrency enums.SupportedCurrency   `param:"supported_currency" json:"supported_currency"`
}

func (i packageListInput) parsePackageIds() []int64 {
	ids := strings.Split(i.PackageIds, ",")
	packageIDs := make([]int64, 0)
	for _, id := range ids {
		if id == "" {
			continue
		}

		ID, err := strconv.ParseInt(id, 10, 64)
		if err != nil {
			continue
		}
		packageIDs = append(packageIDs, ID)
	}
	return packageIDs
}

type poDetailInput struct {
	OrderHash string `param:"order_hash"`
}

func (i *poDetailInput) validate() bool {
	return i.OrderHash != ""
}

type respackDetailInput struct {
	ProductOrderID int64  `param:"po_id"`
	UID            uint32 `param:"uid"`
}

func (i *respackDetailInput) validate() bool {
	return i.ProductOrderID > 0 && i.UID > 0
}

type respackProperty struct {
	Respacks []struct {
		ID          string    `json:"id"`
		Zone        zone.Zone `json:"zone"`
		IsComboItem bool      `json:"is_combo_item"`
	} `json:"respacks"`
}

type effectProperty struct {
	EffectTime string `json:"effect_time"`
}

type availableCouponInput struct {
	UID            uint32 `param:"uid"`
	UnionOrderHash string `param:"union_order_hash"`
}

func (i *availableCouponInput) validate() bool {
	return i.UnionOrderHash != "" && i.UID > 0
}

type packageBuyInput struct {
	PackageID  int64            `json:"package_id"`
	Quantity   uint             `json:"quantity"`
	BuyerID    uint32           `json:"buyer_id"`
	Memo       string           `json:"memo"`
	EffectType enums.EffectType `json:"effect_type"`
}

func (i *packageBuyInput) validate() bool {
	if i.BuyerID <= 0 {
		return false
	}
	if i.PackageID <= 0 {
		return false
	}
	if i.Quantity <= 0 {
		return false
	}

	return true
}

type respackDeductDetails []DeductDetail

// Len length of details
func (r respackDeductDetails) Len() int {
	return len(r)
}

// Less compare deduct date, 抵扣日期倒序
func (r respackDeductDetails) Less(i, j int) bool {
	return r[i].DeductDate.After(r[j].DeductDate)
}

// Swap swap two details
func (r respackDeductDetails) Swap(i, j int) {
	r[i], r[j] = r[j], r[i]
}

// ProductWithRespack product with respack info
type ProductWithRespack struct {
	tradeModels.Product
	RespackID       string             `json:"respack_id"`
	ItemCode        pay.Item           `json:"item_code"`
	ComboItem       string             `json:"combo_item"`
	Zone            zone.Zone          `json:"zone"`
	Quota           int64              `json:"quota"`
	CarryOverPolicy v3.CarryOverPolicy `json:"carry_over_policy"`
	AvailableTime   string             `json:"available_time"`
}
