package finance

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/sync/resultgroup"
	"github.com/qbox/pay-sdk/message"
	"github.com/qbox/pay-sdk/wallet"

	"qbox.us/biz/component/api"

	"qiniu.io/gaea/app/controllers"
	"qiniu.io/gaea/app/env/global"
	"qiniu.io/gaea/app/models"
	"qiniu.io/gaea/app/services/workwx"
)

// BankTransfer 银行转账
type BankTransfer struct {
	controllers.Base

	RequestUser *models.User     `inject:"requestUser"`
	Model       *models.ModelMgr `inject:"="`

	WalletV4Service     wallet.PayWalletServiceClient `inject:"="`
	WorkwxService       workwx.WorkwxService          `inject:"="`
	NotificationService message.Notifier              `inject:"="`
}

// Get 获取记录详情
func (b *BankTransfer) Get(sn string) (res *api.JsonResult) {
	res = api.NewJsonResult()
	if sn == "" {
		res.Code = api.InvalidArgs
		return
	}
	resp, err := b.WalletV4Service.GetBankTransfer(
		b.Req.Context(),
		&wallet.SNParam{
			Sn: sn,
		},
	)
	if err != nil {
		b.Log.Errorf(
			"<BankTransfer.Get> WalletV4Service.GetBankTransfer failed. input:%s, err:%s",
			sn, err,
		)
		res.Code = api.ResultError
		res.Message = err.Error()
		return res
	}
	bts, err := b.fillUserAndSales([]*wallet.BankTransfer{resp})
	if err != nil {
		res.Code = api.ResultError
		res.Message = err.Error()
	}
	res.Data = bts[0]
	return res
}

// List 转账记录列表
func (b *BankTransfer) List() (res *api.JsonResult) {
	res = api.NewJsonResult()
	input := BankTransferListInput{}
	b.Params.BindValuesToStruct(&input)
	if !input.Valid() {
		res.Code = api.InvalidArgs
		return
	}
	resp, err := b.WalletV4Service.ListBankTransfer(
		b.Req.Context(),
		&wallet.BankTransferListParam{
			PaymentAccount:   input.PaymentAccount,
			PaymentAccountNo: input.PaymentAccountNo,
			ReceivedAccount:  input.ReceivedAccount,
			CreatedAtStart: func() *timestamppb.Timestamp {
				if input.CreatedAtStart == nil {
					return nil
				}
				return timestamppb.New(*input.CreatedAtStart)
			}(),
			CreatedAtEnd: func() *timestamppb.Timestamp {
				if input.CreatedAtEnd == nil {
					return nil
				}
				return timestamppb.New(*input.CreatedAtEnd)
			}(),
			Status:   wallet.BankTransferStatus(input.Status),
			Uid:      input.Uid,
			Page:     input.Page,
			PageSize: input.PageSize,
		},
	)
	if err != nil {
		b.Log.Errorf(
			"<BankTransfer.List> WalletV4Service.ListBankTransfer failed. input:%+v, err:%s",
			input, err,
		)
		res.Code = api.ResultError
		res.Message = err.Error()
		return res
	}
	list, err := b.fillUserAndSales(resp.GetData())
	if err != nil {
		res.Code = api.ResultError
		res.Message = err.Error()
		return res
	}
	res.Data = struct {
		List  []BankTransferModel `json:"list"`
		Total uint64              `json:"total"`
	}{
		List:  list,
		Total: resp.GetTotal(),
	}

	return res
}

func (b *BankTransfer) fillUserAndSales(
	list []*wallet.BankTransfer,
) ([]BankTransferModel, error) {
	uas, err := b.fetchUserAndSales(list)
	if err != nil {
		b.Log.Errorf(
			"<BankTransfer.List> fetchUserAndSales failed. list:%+v, err:%s",
			list, err,
		)
		return nil, err
	}
	res := make([]BankTransferModel, len(list))
	for i, transfer := range list {
		res[i] = BankTransferModel{
			BankTransfer: transfer,
			UserAndSales: UserAndSales{
				Sales:        uas[uint32(transfer.GetUid())].Sales,
				IdentityName: uas[uint32(transfer.GetUid())].IdentityName,
			},
			ReceivedDate: transfer.ReceivedDate.AsTime(),
			CreatedAt:    transfer.CreatedAt.AsTime(),
		}
	}
	return res, nil
}

// Create 新增转账记录
func (b *BankTransfer) Create() (res *api.JsonResult) {
	res = api.NewJsonResult()
	input := BankTransferCreateInput{}
	b.Params.BindJsonBody(&input)
	if !input.Valid() {
		res.Code = api.InvalidArgs
		return
	}

	_, err := b.WalletV4Service.CreateBankTransfer(
		b.Req.Context(),
		&wallet.BankTransfer{
			PaymentAccount:    input.PaymentAccount,
			PaymentAccountNo:  input.PaymentAccountNo,
			Amount:            input.Amount.String(),
			ReceivedAccount:   input.ReceivedAccount,
			ReceivedAccountNo: input.ReceivedAccountNo,
			ReceivedBank:      input.ReceivedBank,
			ReceivedDate:      timestamppb.New(input.ReceivedDate),
			Uid:               uint64(input.Uid),
			Creator:           b.RequestUser.Email,
			PaymentBank:       input.PaymentBank,
			BankTxnNo:         input.BankTxnNo,
			CurrencyType:      input.CurrencyType.String(),
			Remark:            input.Remark,
		},
	)
	if err != nil {
		b.Log.Errorf(
			"<BankTransfer.Create> WalletV4Service.CreateBankTransfer failed. input:%+v, err:%s",
			input, err,
		)
		res.Code = api.ResultError
		res.Message = err.Error()
		return res
	}
	return res
}

// Edit 编辑转账记录
func (b *BankTransfer) Edit() (res *api.JsonResult) {
	res = api.NewJsonResult()
	input := BankTransferEditInput{}
	b.Params.BindJsonBody(&input)
	if !input.Valid() {
		res.Code = api.InvalidArgs
		return
	}
	_, err := b.WalletV4Service.EditBankTransfer(
		b.Req.Context(),
		&wallet.BankTransfer{
			Sn:                input.Sn,
			PaymentAccount:    input.PaymentAccount,
			PaymentAccountNo:  input.PaymentAccountNo,
			Amount:            input.Amount.String(),
			ReceivedAccount:   input.ReceivedAccount,
			ReceivedAccountNo: input.ReceivedAccountNo,
			ReceivedBank:      input.ReceivedBank,
			ReceivedDate:      timestamppb.New(input.ReceivedDate),
			Uid:               uint64(input.Uid),
			Creator:           b.RequestUser.Email,
			PaymentBank:       input.PaymentBank,
			CurrencyType:      input.CurrencyType.String(),
			Remark:            input.Remark,
		},
	)
	if err != nil {
		b.Log.Errorf(
			"<BankTransfer.Edit> WalletV4Service.EditBankTransfer failed. input:%+v, err:%s",
			input, err,
		)
		res.Code = api.ResultError
		res.Message = err.Error()
		return res
	}

	return res
}

// Revoke 撤销或重新分配
func (b *BankTransfer) Revoke() (res *api.JsonResult) {
	res = api.NewJsonResult()
	input := BankTransferRevokeInput{}
	b.Params.BindJsonBody(&input)

	if !input.Valid() {
		res.Code = api.InvalidArgs
		return
	}

	reallocates := make([]*wallet.ReallocateParam, 0, len(input.Reallocates))
	for _, r := range input.Reallocates {
		reallocates = append(reallocates, &wallet.ReallocateParam{
			Uid:    uint64(r.Uid),
			Amount: r.Amount.String(),
			Remark: r.Remark,
		})
	}
	_, err := b.WalletV4Service.RevokeBankTransfer(
		b.Req.Context(),
		&wallet.SNParam{
			Sn:          input.Sn,
			Creator:     b.RequestUser.Email,
			Reallocates: reallocates,
		},
	)
	if err != nil {
		b.Log.Errorf(
			"<BankTransfer.Cancel> WalletV4Service.RevokeBankTransfer failed. input:%+v, err:%s",
			input, err,
		)
		res.Code = api.ResultError
		res.Message = err.Error()
		return res
	}
	return res
}

// ImportTemplate 下载导入模板
func (b *BankTransfer) ImportTemplate() (res *api.JsonResult) {
	res = api.NewJsonResult()

	return res
}

// Import 导入转账记录
func (b *BankTransfer) Import() (res *api.JsonResult) {
	res = api.NewJsonResult()

	return res
}

func (b *BankTransfer) fetchUserAndSales(
	data []*wallet.BankTransfer,
) (map[uint32]UserAndSales, error) {
	uids := make([]uint32, 0)
	for _, d := range data {
		if d.GetUid() == 0 {
			continue
		}
		uids = append(uids, uint32(d.GetUid()))
	}

	distinctUIDs := base.UniqueIntSlice(uids)
	uas, err := resultgroup.ParallelMap(
		distinctUIDs,
		func(uid uint32) (UserAndSales, error) {
			sales, err := b.Model.GetSalesByUid(uid)
			if err != nil && !errors.Is(err, models.NoSalesBindedErr) {
				b.Log.Errorf(
					"<BankTransfer.fetchUserInfo> GetSalesByUid failed. uid:%d, err:%s",
					uid, err,
				)
				return UserAndSales{
					Sales: "有销售但获取销售信息异常",
				}, nil
			}
			var dev models.DeveloperModel
			err = b.Model.Developer.FindByUid(uid, &dev)
			if err != nil {
				b.Log.Errorf(
					"<BankTransfer.fetchUserInfo> Developer.FindByUid failed. uid:%d, err:%s",
					uid, err,
				)
				return UserAndSales{}, err
			}
			return UserAndSales{
				IdentityName: dev.Fullname,
				Sales: func() string {
					if sales.CnName != "" {
						return sales.CnName
					}
					if sales.Name != "" {
						return sales.Name
					}
					return "无归属销售"
				}(),
			}, nil
		},
	)
	if err != nil {
		return nil, err
	}
	res := make(map[uint32]UserAndSales)
	for i, ua := range uas {
		res[distinctUIDs[i]] = ua
	}
	return res, nil
}

// ApprovedNotify 发送转账审核消息
func (b *BankTransfer) ApprovedNotify() (res *api.JsonResult) {
	res = api.NewJsonResult()
	input := BankTransferNotifyInput{}
	b.Params.BindJsonBody(&input)

	if !input.Valid() {
		res.Code = api.InvalidArgs
		return
	}
	sales, err := b.Model.GetSalesByUid(input.UID)
	if err != nil && !errors.Is(err, models.NoSalesBindedErr) {
		b.Log.Errorf(
			"<BankTransfer.ApprovedNotify> GetSalesByUid failed. uid:%d, err:%s",
			input.UID, err,
		)
		res.Code = api.ResultError
		res.Message = err.Error()
		return
	}
	if err != nil {
		// 无销售归属
		return
	}

	link := BankTransferNotifyPageLink(sales.Email)

	err = BankTransferSendWorkwxNotify(
		[]uint64{uint64(input.UID)},
		link,
		b.WorkwxService,
		sales.Email,
	)
	if err != nil {
		b.Log.Errorf(
			"BankTransferSendWorkwxNotify send workwxNotify failed: %s",
			err,
		)
	}
	err = BankTransferSendEmailNotify(
		context.Background(),
		[]uint64{uint64(input.UID)},
		link,
		b.NotificationService,
		sales.Email,
	)
	if err != nil {
		b.Log.Errorf(
			"BankTransferSendEmailNotify send email failed: %s",
			err,
		)
	}

	return res
}

const workwxNotifyTemplate = `**您有 ` + "`%d`" + ` 个客户的银行转账待确认，请及时确认** `

// BankTransferSendWorkwxNotify 发送待确认企微消息
func BankTransferSendWorkwxNotify(
	users []uint64,
	link string,
	workwxService workwx.WorkwxService,
	email string,
) error {
	content := fmt.Sprintf(workwxNotifyTemplate, len(users))
	for _, uid := range users {
		content += fmt.Sprintf(`
>
>[%d](%s&uid=%d)`, uid, link, uid)
	}
	content += "\n"
	return workwxService.SendMarkdownToUsers(
		[]string{strings.TrimSuffix(email, "@qiniu.com")},
		content,
		false,
	)
}

// BankTransferSendEmailNotify 发送待确认邮件
func BankTransferSendEmailNotify(
	ctx context.Context,
	users []uint64,
	link string,
	notificationService message.Notifier,
	email string,
) error {
	subject := fmt.Sprintf("您有[%d]个客户的银行转账待确认，请及时确认", len(users))
	content := ""
	for _, uid := range users {
		content += fmt.Sprintf("UID:%d, %s<br>", uid, fmt.Sprintf("%s&uid=%d", link, uid))
	}
	_, err := notificationService.SendEmail(ctx, &message.SendMailReq{
		To:      []string{email},
		Subject: &subject,
		Content: &content,
		Tag:     []string{"bank-transfer-pending-confirm-notify"},
		From:    global.Env.Email.PortalEmail,
	})
	type emailData struct {
		UID  uint64 `json:"uid"`
		Link string `json:"link"`
	}
	_, err = notificationService.SendTemplateEmail(
		ctx,
		&message.SendTemplateEmailReq{
			SendMailReq: message.SendMailReq{
				To:   []string{email},
				Tag:  []string{"bank-transfer-pending-confirm-notify"},
				From: global.Env.Email.PortalEmail,
			},
			SendTemplateReq: message.SendTemplateReq{
				ChannelID:  "internal_message",
				TemplateID: "bank-transfer-pending-confirm-notify",
				TemplateData: map[string]interface{}{
					"total": len(users),
					"users": func() []emailData {
						links := make([]emailData, len(users))

						for _, uid := range users {
							links = append(links, emailData{
								UID:  uid,
								Link: fmt.Sprintf("%s&uid=%d", link, uid),
							})
						}
						return links
					},
				},
			},
		},
	)
	return err
}

// BankTransferNotifyPageLink 根据不同发送对象获取页面连接地址
func BankTransferNotifyPageLink(email string) string {
	salesPageLink := fmt.Sprintf(
		"%s/bo/financial/bank-transfer/confirmation?status=2",
		global.Env.Service.PORTAL_IO_HOST,
	)
	salesOperationPageLink := fmt.Sprintf(
		"%s/bo/financial/bank-transfer/admin?status=2",
		global.Env.Service.PORTAL_IO_HOST,
	)

	if email == global.Env.Email.SalesOperationEmail {
		return salesOperationPageLink
	}
	return salesPageLink
}
