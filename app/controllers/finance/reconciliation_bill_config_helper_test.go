package finance

import (
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/test"
	"github.com/stretchr/testify/assert"

	"qbox.us/api/pay/pay"
	walletV3 "qbox.us/api/pay/wallet.v3"
)

func TestGroupDistinctBillItemsByMonth(t *testing.T) {
	test.RunWithUTCAndCST(t, testGroupDistinctBillItemsByMonth)
}

func testGroupDistinctBillItemsByMonth(t *testing.T, loc *time.Location) {
	tests := []struct {
		bills    []walletV3.ModelBaseBill
		expected map[int64][]string
	}{
		{
			bills:    nil,
			expected: map[int64][]string{},
		},
		{
			bills: []walletV3.ModelBaseBill{
				{
					Item:    pay.SPACE,
					Group:   pay.GROUP_SPACE,
					Product: pay.PRODUCT_KODO,
					FromSec: pay.NewSecond(time.Date(2018, 1, 1, 0, 0, 0, 0, loc)),
					ToSec:   pay.NewSecond(time.Date(2018, 2, 1, 0, 0, 0, 0, loc)),
				},
				{
					Item:    pay.SPACE,
					Group:   pay.GROUP_SPACE,
					Product: pay.PRODUCT_KODO,
					FromSec: pay.NewSecond(time.Date(2020, 1, 1, 0, 0, 0, 0, loc)),
					ToSec:   pay.NewSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, loc)),
				},
				{
					Item:    pay.PILI_TRANSFER_DOWN,
					Group:   pay.GROUP_PILI_TRANSFER,
					Product: pay.PRODUCT_PILI,
					FromSec: pay.NewSecond(time.Date(2020, 1, 1, 0, 0, 0, 0, loc)),
					ToSec:   pay.NewSecond(time.Date(2020, 1, 2, 0, 0, 0, 0, loc)),
				},
				{
					Item:    pay.PILI_TRANSFER_DOWN,
					Group:   pay.GROUP_PILI_TRANSFER,
					Product: pay.PRODUCT_PILI,
					FromSec: pay.NewSecond(time.Date(2020, 1, 11, 0, 0, 0, 0, loc)),
					ToSec:   pay.NewSecond(time.Date(2020, 1, 12, 0, 0, 0, 0, loc)),
				},
				{
					Item:    pay.PILI_TRANSFER_DOWN,
					Group:   pay.GROUP_PILI_TRANSFER,
					Product: pay.PRODUCT_PILI,
					FromSec: pay.NewSecond(time.Date(2020, 1, 31, 0, 0, 0, 0, loc)),
					ToSec:   pay.NewSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, loc)),
				},
				{
					Item:    pay.SPACE,
					Group:   pay.GROUP_SPACE,
					Product: pay.PRODUCT_KODO,
					FromSec: pay.NewSecond(time.Date(2020, 2, 1, 0, 0, 0, 0, loc)),
					ToSec:   pay.NewSecond(time.Date(2020, 3, 1, 0, 0, 0, 0, loc)),
				},
			},
			expected: map[int64][]string{
				time.Date(2018, 1, 1, 0, 0, 0, 0, loc).Unix(): {
					"space",
				},
				time.Date(2020, 1, 1, 0, 0, 0, 0, loc).Unix(): {
					"space",
					"pili:transfer:down",
				},
				time.Date(2020, 2, 1, 0, 0, 0, 0, loc).Unix(): {
					"space",
				},
			},
		},
	}
	for _, tt := range tests {
		actual := groupDistinctBillItemsByMonth(tt.bills, loc)

		assert.Len(t, actual, len(tt.expected))

		for expectedMonthTs, expectedItemCodes := range tt.expected {
			actualItemCodes, ok := actual[expectedMonthTs]
			assert.True(t, ok)
			assert.ElementsMatch(t, expectedItemCodes, actualItemCodes)
		}
	}
}
