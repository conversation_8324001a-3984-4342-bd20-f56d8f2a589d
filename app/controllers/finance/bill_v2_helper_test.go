package finance

import (
	"strings"
	"testing"
	"time"

	"github.com/qbox/bo-base/v4/test"
	"github.com/stretchr/testify/assert"
)

func TestBillsDownloadInput_IsValid(t *testing.T) {
	billsDownloadInput := BillsDownloadInput{}

	billsDownloadInput = append(
		billsDownloadInput,
		BillDownloadRequest{
			BillRequestId: "",
		},
	)
	assert.Equal(t, false, billsDownloadInput.IsValid())

	billsDownloadInput[0].BillRequestId = "not empety"
	assert.Equal(t, true, billsDownloadInput.IsValid())
}

func TestBillsDownloadInput_SplitTimeInMonth(t *testing.T) {
	billV2 := BillV2{}

	billsDownloadInput := make([]billDownloadInfo, 0)
	var (
		wantList [][]string
	)

	billDownloadInfo1 := mockBillDownInfo(1380536335, getTimeMonth("201809"), getTimeMonth("201811"))
	billsDownloadInput = append(billsDownloadInput, billDownloadInfo1)
	want1 := []string{"201809", "201810", "201811"}
	wantList = append(wantList, want1)

	billDownloadInfo2 := mockBillDownInfo(1380536335, getTimeMonth("201810"), getTimeMonth("201811"))
	billsDownloadInput = append(billsDownloadInput, billDownloadInfo2)
	want2 := []string{"201810", "201811"}
	wantList = append(wantList, want2)

	for index, billDownloadInfo := range billsDownloadInput {
		out := billV2.SplitTimeInMonth(billDownloadInfo.Start, billDownloadInfo.End)
		assert.Equal(t, wantList[index], out)
	}

}

func TestBillV2_GetBillHeadInfo(t *testing.T) {
	test.RunWithUTCAndCST(t, testBillV2_GetBillHeadInfo)
}

func testBillV2_GetBillHeadInfo(t *testing.T, loc *time.Location) {
	billV2 := BillV2{}

	var (
		billDetailInfoList []BillDetailInfoResult
	)

	billDetailInfo := mockBillDetailInfo("<EMAIL>", 10000)
	billDetailInfoList = append(billDetailInfoList, billDetailInfo)

	billDetailInfo = mockBillDetailInfo("<EMAIL>", 20000)
	billDetailInfoList = append(billDetailInfoList, billDetailInfo)

	startTime := getTime("20181001", loc)
	endTime := getTime("20181231", loc)

	want := BillHeadRenderParams{
		User:      "<EMAIL>",
		CostList:  []string{"1.00", "2.00"},
		TotalCost: 30000,
	}

	out := billV2.GetBillHeadInfo(billDetailInfoList, startTime, endTime)
	assert.Equal(t, want.User, out.User)
	assert.Equal(t, want.CostList, out.CostList)
	assert.Equal(t, want.TotalCost, out.TotalCost)
}

func TestBillV2_GetBillHeadUserStr(t *testing.T) {
	billV2 := BillV2{}

	userNameList := []string{"<EMAIL>", "<EMAIL>"}
	want := "<EMAIL>"

	out := billV2.GetBillHeadUserStr(userNameList)
	assert.Equal(t, want, out)

	userNameList = []string{"<EMAIL>", "<EMAIL>"}
	outUserNameStr := billV2.GetBillHeadUserStr(userNameList)

	for _, userName := range userNameList {
		out := strings.Contains(outUserNameStr, userName)
		want := true
		assert.Equal(t, want, out)
	}
}

func TestBillV2_GetBillHeadTotalCost(t *testing.T) {
	billV2 := BillV2{}

	totalCostList := []int64{10000, 20000}
	want := int64(30000)

	out := billV2.GetBillHeadTotalCost(totalCostList)
	assert.Equal(t, want, out)

	totalCostList = []int64{0, 10000, 20000, 30000}
	want = int64(60000)

	out = billV2.GetBillHeadTotalCost(totalCostList)
	assert.Equal(t, want, out)
}

func getTimeMonth(timeStr string) time.Time {
	time, _ := time.Parse("200601", timeStr)
	return time
}

func mockBillDownInfo(uid uint32, start time.Time, end time.Time) billDownloadInfo {
	return billDownloadInfo{
		Uid:   uid,
		Start: start,
		End:   end,
	}
}

func mockBillDetailInfo(user string, totalCost int64) BillDetailInfoResult {
	return BillDetailInfoResult{
		User:      user,
		TotalCost: totalCost,
	}
}
