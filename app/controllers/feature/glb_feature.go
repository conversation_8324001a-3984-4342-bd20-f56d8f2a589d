package feature

import (
	"context"
	"strings"

	"github.com/qiniu/qmgo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"qbox.us/biz/component/api"
	"qiniu.io/gaea/app/env/global"
	"qiniu.io/gaea/app/models"
)

func (c *Feature) GlbFeature() (res *api.JsonResult) {
	res = &api.JsonResult{}

	featureList, err := c.Model.GlbFeatureConfg.List()
	if err != nil {
		res.Code = api.DatabaseError
		c.Log.Errorf("glb featureconfg list failed %+v", err)
		return
	}

	retAll := global.Env.Feature

	ret := map[string][]global.Feature{}
	for k, v := range retAll {
		if strings.Contains(k, "_NAVS") {
			continue
		}
		tmp := make([]global.Feature, len(v))
		copy(tmp, v)
		ret[k] = tmp
	}

	for _, v := range featureList {
		var featureId []string
		if len(strings.Split(v.FeatureId, "_")) > 1 {
			featureId = strings.Split(v.FeatureId, "_")
		} else {
			c.Log.Errorf("invalid featureId %s", v.FeatureId)
			continue
		}

		for k, val := range ret[featureId[0]] {
			if v.FeatureId == val.FeatureId {
				if v.State == models.FeatureDisable {
					ret[featureId[0]][k].Disable = true
				}
				break
			}
		}

	}

	res.Data = ret

	return res
}

func (c *Feature) GlbFeatureEdit() (res *api.JsonResult) {
	res = &api.JsonResult{}

	var input glbFeatureEditInput
	c.Params.BindJsonBody(&input)
	if !input.Valid() {
		res.Code = api.InvalidArgs
		return
	}

	gfcm, err := c.Model.GlbFeatureConfg.FindByFid(input.FeatureId)
	if err != nil && err != qmgo.ErrNoSuchDocuments {
		res.Code = api.DatabaseError
		c.Log.Errorf("glbfeatureconfg FindByFid %s failed %s", input.FeatureId, err)
		return
	}

	state := models.FeatureNormal
	if input.Disable {
		state = models.FeatureDisable
	} else {
		state = models.FeatureEnable
	}

	if gfcm.FeatureId == "" {
		gfcm.Id = primitive.NewObjectID()
		gfcm.FeatureId = input.FeatureId
		gfcm.State = state
		gfcm.Summary = input.Summary

		_, err = c.Model.GlbFeatureConfg.InsertOne(context.Background(), &gfcm)
		if err != nil {
			c.Log.Errorf("new glb feature config %+v failed %s", gfcm, err)
			res.Code = api.DatabaseError
		}

		return
	}

	if state == models.FeatureNormal {
		err = c.Model.GlbFeatureConfg.RemoveId(context.Background(), input.FeatureId)
		if err != nil {
			c.Log.Errorf("delete glb feature config %s failed %s", input.FeatureId, err)
			res.Code = api.DatabaseError
		}

		return
	}

	query := bson.M{"_id": gfcm.Id}
	change := bson.M{"$set": bson.M{
		"featureId": input.FeatureId,
		"summary":   input.Summary,
		"state":     state,
	}}

	err = c.Model.GlbFeatureConfg.UpdateOne(context.Background(), query, change)
	if err != nil {
		res.Code = api.DatabaseError
		c.Log.Errorf("glb feature update %+v %+v failed %s", query, change, err)
	}

	return res
}

type glbFeatureEditInput struct {
	FeatureId string `json:"featureId"`
	Disable   bool   `json:"disable"`
	Summary   string `json:"summary"`
}

func (i *glbFeatureEditInput) Valid() bool {
	i.FeatureId = strings.TrimSpace(i.FeatureId)
	if i.FeatureId == "" {
		return false
	}

	return true
}
