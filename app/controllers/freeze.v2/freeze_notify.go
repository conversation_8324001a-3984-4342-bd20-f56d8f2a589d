package freeze

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/qiniu/qmgo"

	"github.com/qbox/bo-base/v4/intl/tz"
	"github.com/qbox/pay-sdk/message"

	"qbox.us/biz/component/api"
	"qbox.us/biz/services.v2/account"
	"qbox.us/biz/utils.v2/types"

	"qiniu.io/gaea/app/enums"
	"qiniu.io/gaea/app/env/global"
	"qiniu.io/gaea/app/models"
	"qiniu.io/gaea/app/services/cache"
	"qiniu.io/gaea/app/utils"
)

const (
	DefaultTimeLayout               = "01-02 15:04"
	DefaultFreezeBufferedTimeLayout = "2006-01-02 09:00"
)

func (c *Freeze) IsNotifyRateLimited(bizCtx *Context, strategy *Strategy) (limited bool) {
	// 如果不是冻结告知提醒，则控制频率
	if strategy.NotifyStrategy.IsFreezeNotify() {
		return
	}

	// 默认通知频率为1天一次
	notifyPeriod := time.Hour * 24

	// FB默认频率 3 天
	if strategy.Strategy == enums.FreezeStrategyB {
		notifyPeriod = time.Hour * 72
	}

	if c.isReachRateLimit(bizCtx.ctx, bizCtx.payload.Base.Uid, notifyPeriod) {
		limited = true
		c.Log.Infof("<Freeze.SendNotification> message not send due to limit, uid: %d ", bizCtx.payload.Base.Uid)
		return
	}

	return limited
}

func (c *Freeze) SendNotification(bizCtx *Context, strategy *Strategy) {
	msgIn := &message.SendMessageReq{
		UID:       int64(bizCtx.payload.Base.Uid),
		ChannelID: "13",
	}
	msgIn.Params = map[string]interface{}{
		"tags": []string{"gaea", "freeze_notify"},
	}

	// 冻结策略： https://cf.qiniu.io/pages/viewpage.action?pageId=58279906
	switch strategy.NotifyStrategy {
	case enums.FreezeNotifyStrategyNotNotify:
		return
	case enums.FreezeNotifyStrategyA:
		// Not Send
		return
	case enums.FreezeNotifyStrategyB:
		c.fillDataForStrategyB(bizCtx.developer, bizCtx.payload, msgIn)
		c.sendSellerWarning(bizCtx.developer, bizCtx.payload)
	case enums.FreezeNotifyStrategyC:
		c.fillDataForStrategyC(strategy, bizCtx.developer, bizCtx.payload, msgIn)
		// BO-6245 冻结提醒余额不要算上抵用券金额
		c.sendSellerNotify(bizCtx.payload.Base.Uid, bizCtx.input.Type, bizCtx.input.Reason, bizCtx.payload)
	case enums.FreezeNotifyStrategyD:
		c.fillDataForStrategyD(strategy, bizCtx.developer, bizCtx.payload, msgIn)
		c.sendSellerNotify(bizCtx.payload.Base.Uid, bizCtx.input.Type, bizCtx.input.Reason, bizCtx.payload)
	case enums.FreezeNotifyStrategyE:
		if utils.IsViolationFreeze(bizCtx.input.Type, bizCtx.input.Reason) {
			c.fillDataForViolationNotice(bizCtx.developer, msgIn)
		} else {
			c.fillDataForStrategyE(strategy, bizCtx.developer, bizCtx.payload, bizCtx.input.Reason, msgIn)
		}
		c.sendSellerNotify(bizCtx.payload.Base.Uid, bizCtx.input.Type, bizCtx.input.Reason, bizCtx.payload)
	case enums.FreezeNotifyStrategyF:
		c.fillDataForStrategyF(strategy, bizCtx.developer, bizCtx.payload, msgIn)
		c.sendSellerBufferedNotify(bizCtx.developer, bizCtx.payload)
	}

	if !bizCtx.input.Dummy {
		err := c.Notifier.SendMessage(c.Req.Context(), msgIn)
		if err != nil {
			c.Log.Errorf("<Freeze.SendNotification> c.Notifier.SendMessage(msgIn) with error: %s", err)
			return
		}
	}
}

func (c *Freeze) fillDataForStrategyB(developer *models.DeveloperModel, payload *models.FreezePayload, msgIn *message.SendMessageReq) {
	msgIn.TemplateID = "freeze_tpl_b"

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "余额不足提醒",
			Other: "余额不足提醒",
		},
	})

	msgIn.TemplateData = map[string]interface{}{
		"op":                    opTemp,
		"email":                 developer.Email,
		"time":                  time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultTimeLayout),
		"currency_type":         payload.Finance.CurrencyType.FallbackToCNY().String(),
		"availableBalance":      payload.Freeze.AvailableBalance.String(),
		"parentSumBalance":      payload.Freeze.ParentSumBalance.String(),
		"financialType":         payload.FreezeFinanceParent.Type,
		"financialParent":       payload.FreezeFinanceParent.ParentUID,
		"financialOverviewLink": global.Env.HOST.FinancialOverviewLink,
		"freezeAndThawLink":     global.Env.HOST.OweTheProcessDescLink,
		"rechargeLink":          global.Env.HOST.RechargeLink,
		"supportLink":           fmt.Sprintf("%s?feedback=true", global.Env.HOST.PortalFrontend),
		"freezeDescLink":        global.Env.HOST.FreezeDescLink,
	}
}

func (c *Freeze) fillDataForStrategyC(strategy *Strategy, developer *models.DeveloperModel, payload *models.FreezePayload, msgIn *message.SendMessageReq) {
	msgIn.TemplateID = "freeze_tpl_c"

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "冻结告知",
			Other: "冻结告知",
		},
	})

	msgIn.TemplateData = map[string]interface{}{
		"op":                    opTemp,
		"email":                 developer.Email,
		"time":                  time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultTimeLayout),
		"currency_type":         payload.Finance.CurrencyType.FallbackToCNY().String(),
		"availableBalance":      payload.Freeze.AvailableBalance.String(),
		"parentSumBalance":      payload.Freeze.ParentSumBalance.String(),
		"financialType":         payload.FreezeFinanceParent.Type,
		"financialParent":       payload.FreezeFinanceParent.ParentUID,
		"childrenAffected":      strategy.ChildrenAffected,
		"freezeAndThawLink":     global.Env.HOST.OweTheProcessDescLink,
		"rechargeLink":          global.Env.HOST.RechargeLink,
		"financialOverviewLink": global.Env.HOST.FinancialOverviewLink,
		"supportLink":           fmt.Sprintf("%s?feedback=true", global.Env.HOST.PortalFrontend),
		"freezeDescLink":        global.Env.HOST.FreezeDescLink,
	}
}

func (c *Freeze) fillDataForStrategyD(strategy *Strategy, developer *models.DeveloperModel, payload *models.FreezePayload, msgIn *message.SendMessageReq) {
	msgIn.TemplateID = "freeze_tpl_d"

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "冻结告知",
			Other: "冻结告知",
		},
	})

	msgIn.TemplateData = map[string]interface{}{
		"op":                opTemp,
		"email":             developer.Email,
		"time":              time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultTimeLayout),
		"freezeAndThawLink": global.Env.HOST.FreezeAndThawLink,
		"supportLink":       fmt.Sprintf("%s?feedback=true", global.Env.HOST.PortalFrontend),
		"freezeDescLink":    global.Env.HOST.FreezeDescLink,
		"childrenAffected":  strategy.ChildrenAffected,
	}
}

func (c *Freeze) fillDataForStrategyE(strategy *Strategy, developer *models.DeveloperModel, payload *models.FreezePayload, reason string, msgIn *message.SendMessageReq) {
	msgIn.TemplateID = "freeze_tpl_e"

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "冻结告知",
			Other: "冻结告知",
		},
	})

	msgIn.TemplateData = map[string]interface{}{
		"op":                    opTemp,
		"email":                 developer.Email,
		"time":                  time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultTimeLayout),
		"reason":                reason,
		"freezeAndThawLink":     global.Env.HOST.FreezeAndThawLink,
		"supportLink":           fmt.Sprintf("%s?feedback=true", global.Env.HOST.PortalFrontend),
		"freezeDescLink":        global.Env.HOST.FreezeDescLink,
		"childrenAffected":      strategy.ChildrenAffected,
		"triggerDeleteResource": strategy.TriggerDeleteResource,
	}
}

func (c *Freeze) fillDataForStrategyF(strategy *Strategy, developer *models.DeveloperModel, payload *models.FreezePayload, msgIn *message.SendMessageReq) {
	msgIn.TemplateID = "freeze_tpl_f"

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "欠费告知",
			Other: "欠费告知",
		},
	})

	msgIn.TemplateData = map[string]interface{}{
		"op":                    opTemp,
		"email":                 developer.Email,
		"time":                  time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultFreezeBufferedTimeLayout),
		"currency_type":         payload.Finance.CurrencyType.FallbackToCNY().String(),
		"availableBalance":      payload.Freeze.AvailableBalance.String(),
		"parentSumBalance":      payload.Freeze.ParentSumBalance.String(),
		"financialType":         payload.FreezeFinanceParent.Type,
		"financialParent":       payload.FreezeFinanceParent.ParentUID,
		"remainingHour":         strconv.FormatInt(payload.Freeze.RemainingHours/24, 10),
		"childrenAffected":      strategy.ChildrenAffected,
		"financialOverviewLink": global.Env.HOST.FinancialOverviewLink,
		"rechargeLink":          global.Env.HOST.RechargeLink,
		"freezeAndThawLink":     global.Env.HOST.OweTheProcessDescLink,
		"supportLink":           fmt.Sprintf("%s?feedback=true", global.Env.HOST.PortalFrontend),
		"freezeDescLink":        global.Env.HOST.FreezeDescLink,
	}
}

func (c *Freeze) fillDataForViolationNotice(developer *models.DeveloperModel, msgIn *message.SendMessageReq) {
	// 单独配置违法违规冻结
	msgIn.ChannelID = "14"
	msgIn.TemplateID = "violation_freeze_notice_b"
	var email string
	if !utils.IsFakeEmail(developer.Email) {
		email = developer.Email
	}

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "冻结告知",
			Other: "冻结告知",
		},
	})

	msgIn.TemplateData = map[string]interface{}{
		"op":                                opTemp,
		"email":                             email,
		"time":                              time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultTimeLayout),
		"violationInformationStandardsLink": global.Env.HOST.ViolationInfoStandardsLink,
		"documentationLink":                 global.Env.HOST.ViolationSelfCheckTemplateLink,
	}
}

func (c *Freeze) sendInternalNotify(bizCtx *Context, strategy *Strategy) (err error) {
	mailsTo := []string{bizCtx.developer.Email}
	phonesTo := []string{}

	if bizCtx.developer.PhoneNumber != "" {
		phonesTo = append(phonesTo, bizCtx.developer.PhoneNumber)
	}

	// try to get internal owner
	if bizCtx.payload.Base.InternalOwner != "" {
		var user models.User
		err = c.Model.User.GetByEmail(bizCtx.payload.Base.InternalOwner, &user)
		if err != nil && err != qmgo.ErrNoSuchDocuments {
			return
		}

		if user.Email != bizCtx.user.Email {
			if user.Email != "" {
				mailsTo = append(mailsTo, user.Email)
			}
			if user.Mobile != "" {
				phonesTo = append(phonesTo, user.Mobile)
			}
		}
	}

	data := map[string]interface{}{
		"email":             bizCtx.developer.Email,
		"reason":            bizCtx.input.Reason,
		"internalTypeLabel": bizCtx.payload.Base.InternalType.Humanize(c.Localizer),
		"internalTypeQuota": bizCtx.payload.Base.InternalType.ConsumptionLimit().String(),
		"currency_type":     bizCtx.payload.Finance.CurrencyType.FallbackToCNY().String(),
		"ungenBillsMoney":   bizCtx.payload.Finance.UngenbillsMoney.String(),
	}

	var channelID = "internal_message"
	var tpl string
	if bizCtx.input.Type == account.DISABLED_TYPE_AUTO {
		tpl = "freeze_tpl_internal_auto"
	} else {
		tpl = "freeze_tpl_internal"
	}

	_, err = c.Notifier.SendTemplateEmail(bizCtx.ctx, &message.SendTemplateEmailReq{
		SendMailReq: message.SendMailReq{
			UID:  bizCtx.user.Uid,
			To:   mailsTo,
			Tag:  []string{"gaea", "freeze_internal_notify"},
			From: global.Env.Email.PortalEmail,
		},
		SendTemplateReq: message.SendTemplateReq{
			ChannelID:    channelID,
			TemplateID:   tpl,
			TemplateData: data,
		},
	})
	if err != nil {
		c.Log.Errorf("<Freeze.sendInternalNotify> c.Notifier.SendTemplateEmail(%d) with error: %s", bizCtx.user.Uid, err)
		return err
	}

	for _, phone := range phonesTo {
		// do not return if returns error
		_, err := c.Notifier.SendTemplateSMS(bizCtx.ctx, &message.SendTemplateSMSReq{
			SendSmsReq: message.SendSmsReq{
				UID:         bizCtx.user.Uid,
				PhoneNumber: phone,
			},
			SendTemplateReq: message.SendTemplateReq{
				ChannelID:    channelID,
				TemplateID:   tpl,
				TemplateData: data,
			},
		})
		if err != nil {
			c.Log.Errorf("<Freeze.sendInternalNotify> c.Notifier.SendTemplateSMS(%d) with error: %s", bizCtx.user.Uid, err)
		}
	}

	return nil
}

func (c *Freeze) sendUnFreezeNotification(uid uint32, childrenAffected bool) (err error) {
	info, err := c.AdminAccountService.FindInfoByUid(uid)
	if err != nil {
		c.Log.Errorf("<Freeze.sendUnFreezeNotification> c.AdminAccountService.FindInfoByUid(%d) failed. err: %s", uid, err)
		return
	}

	msg := message.SendMessageReq{
		UID:        int64(uid),
		ChannelID:  "13",
		TemplateID: "freeze_over",
	}

	if !info.UserType.IsParentUser() {
		childrenAffected = false
	}

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "账户解冻",
			Other: "账户解冻",
		},
	})

	msg.TemplateData = map[string]interface{}{
		"op":               opTemp,
		"name":             "",
		"email":            info.Email,
		"time":             time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultTimeLayout),
		"unsubscribe":      global.Env.HOST.Unsubscribe,
		"childrenAffected": childrenAffected,
	}
	msg.Params = map[string]interface{}{
		"tags":     []string{"gaea", "freeze_over"},
		"mailFrom": global.Env.Email.PortalEmail,
	}

	err = c.Notifier.SendMessage(c.Req.Context(), &msg)
	if err != nil {
		c.Log.Errorf("<Freeze.sendUnFreezeNotification> c.Notifier.SendMessage(msg) with error: %s", err)
		return
	}

	return err
}

func (c *Freeze) sendUnBufferedNotification(uid uint32) (err error) {
	info, err := c.AdminAccountService.FindInfoByUid(uid)
	if err != nil {
		c.Log.Errorf("<Freeze.sendUnFreezeNotification> c.AdminAccountService.FindInfoByUid(%d) failed. err: %s", uid, err)
		return
	}

	msg := message.SendMessageReq{
		UID:        int64(uid),
		ChannelID:  "13",
		TemplateID: "un_buffered",
	}

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "恢复服务",
			Other: "恢复服务",
		},
	})

	msg.TemplateData = map[string]interface{}{
		"op":          opTemp,
		"name":        "",
		"email":       info.Email,
		"time":        time.Now().In(tz.MustLocationFromCtx(c.Req.Context())).Format(DefaultTimeLayout),
		"unsubscribe": global.Env.HOST.Unsubscribe,
	}
	msg.Params = map[string]interface{}{
		"tags":     []string{"gaea", "un_buffered"},
		"mailFrom": global.Env.Email.PortalEmail,
	}

	err = c.Notifier.SendMessage(c.Req.Context(), &msg)
	if err != nil {
		c.Log.Errorf("<Freeze.sendUnBufferedNotification> c.Notifier.SendMessage(msg) with error: %s", err)
		return
	}

	return err
}

func (c *Freeze) SendClearBucketNotification(uid uint32, freezeDay int) (err error) {
	info, err := c.AdminAccountService.FindInfoByUid(uid)
	if err != nil {
		c.Log.Errorf("<Freeze.sendClearBucketNotification> c.AdminAccountService.FindInfoByUid(%d) failed. err: %s", uid, err)
		return
	}

	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "您的空间将被清理",
			Other: "您的空间将被清理",
		},
	})

	data := map[string]interface{}{
		"op":          opTemp,
		"email":       info.Email,
		"freezeDay":   freezeDay,
		"portalLink":  global.Env.HOST.PortalFrontend,
		"supportLink": fmt.Sprintf("%s?feedback=true", global.Env.HOST.PortalFrontend),
	}

	msg := message.SendMessageReq{
		UID:        int64(uid),
		ChannelID:  "13",
		TemplateID: "clear_bucket_notify",
	}
	msg.TemplateData = data
	msg.Params = map[string]interface{}{
		"tags":     []string{"gaea", "un_buffered"},
		"mailFrom": global.Env.Email.PortalEmail,
	}
	err = c.Notifier.SendMessage(c.Req.Context(), &msg)
	if err != nil {
		c.Log.Errorf("<Freeze.sendClearBucketNotification> c.Notifier.SendMessage(%d) with error: %s", uid, err)
		return
	}

	c.Log.Infof("<Freeze.sendClearBucketNotification> send clear bucket notification to user(%d)", uid)

	err = c.sendSalesClearBucketNotification(uid, freezeDay)
	if err != nil {
		// 打过日志了
		return err
	}

	return err
}

func (c *Freeze) ClearBucketNotify() (res *api.JsonResult) {
	res = api.NewJsonResult()

	var input struct {
		UIDs   []uint32 `json:"uids"`
		Day    int      `json:"day"`
		Notify bool     `json:"notify"`
	}
	c.Params.BindJsonBody(&input)

	if len(input.UIDs) == 0 {
		return
	}

	if input.Day <= 0 {
		res.Code = api.InvalidArgs
		res.Message = "day should be greater than 0"
		return
	}

	loc := tz.MustLocationFromCtx(c.Req.Context())
	from := time.Now().AddDate(0, 0, -input.Day)
	from = time.Date(from.Year(), from.Month(), from.Day(), 0, 0, 0, 0, loc)
	to := from.AddDate(0, 0, 1)

	param := &ListFrozenUserParam{
		From:                    from,
		To:                      to,
		IsTriggerDeleteResource: true,
	}

	uids, err := c.ListFrozenUserBetweenTimeRange(param)
	if err != nil {
		c.Log.Errorf("<Freeze.ClearBucketNotify> List frozen users between time (%+v) failed: %s", *param, err)
		res.Code = api.ResultError
		return
	}

	uidsMap := make(map[uint32]struct{})
	for _, uid := range uids {
		uidsMap[uid] = struct{}{}
	}

	shouldNotifyUIDs := make([]uint32, 0)
	for _, uid := range input.UIDs {
		if _, ok := uidsMap[uid]; ok {
			shouldNotifyUIDs = append(shouldNotifyUIDs, uid)
		}
	}

	if input.Notify {
		for _, uid := range shouldNotifyUIDs {
			go c.SendClearBucketNotification(uid, input.Day)
		}
	}

	res.Data = shouldNotifyUIDs
	return res
}

type balanceNotEnoughIn struct {
	Balance     types.Money `json:"balance"`
	Consumption types.Money `json:"consumption"`
	RemainDays  int         `json:"remain_days"`
}

// BalanceNotEnoughNotification 余额不足通知
// Deprecated 当前通知引用的模板都已弃用，近期无调用，后续有需求再适配此接口
func (c *Freeze) BalanceNotEnoughNotification(uid uint32) (res *api.JsonResult) {
	res = api.NewJsonResult()

	var in balanceNotEnoughIn
	c.Params.BindJsonBody(&in)

	c.Log.Warnf("balance not enough notification is deprecated, current request is %+v", in)

	res.Code = api.ResultError
	return res
}

func (c *Freeze) isReachRateLimit(ctx context.Context, uid uint32, period time.Duration) bool {

	var (
		limitKey          = fmt.Sprintf("freeze:limit:%d", uid)
		periodSeconds     = int(period.Seconds())
		prevPeriodSeconds int
	)

	err := c.Cache.Get(ctx, limitKey, &prevPeriodSeconds)

	if err == cache.ErrNotFound || prevPeriodSeconds > periodSeconds {
		// 如果缓存未设置、已过期 或者 缓存中的 period 大于 当前的 period，则更新缓存
		c.Cache.Set(ctx, limitKey, periodSeconds, periodSeconds)
		return false
	} else if err == nil {
		return true
	} else {
		c.Log.Errorf("<Freeze.isReachRateLimit> c.Cache.Get(%s) failed: %s", limitKey, err)
	}

	return false
}

func (c *Freeze) clearRateLimit(ctx context.Context, uid uint32) error {
	return c.Cache.Del(ctx, fmt.Sprintf("freeze:limit:%d", uid))
}

func (c *Freeze) changeFreezeBufferedHours(uid uint32, email string, freezeDate string) error {
	opTemp := c.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "七牛云修改欠费保护期成功",
			Other: "七牛云修改欠费保护期成功",
		},
	})

	data := map[string]interface{}{
		"op":                    opTemp,
		"email":                 email,
		"freezeDate":            freezeDate,
		"financialOverviewLink": global.Env.HOST.FinancialOverviewLink,
	}

	msg := message.SendMessageReq{
		UID:        int64(uid),
		ChannelID:  "13",
		TemplateID: "add_freeze_buffered_hours",
	}
	msg.TemplateData = data
	msg.Params = map[string]interface{}{
		"tags":     []string{"gaea", "add_freeze_buffered_hours"},
		"mailFrom": global.Env.Email.PortalEmail,
	}
	err := c.Notifier.SendMessage(c.Req.Context(), &msg)

	return err
}
