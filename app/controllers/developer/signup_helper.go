package developer

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/qbox/bo-base/v4/eventbus"
	messageSDK "github.com/qbox/pay-sdk/message"
	"github.com/qiniu/qmgo"
	gaeaEnums "qbox.us/biz/api/gaea/enums"
	"qbox.us/biz/component/api"
	"qiniu.io/gaea/app/code"
	"qiniu.io/gaea/app/env/global"
	"qiniu.io/gaea/app/models"
	"qiniu.io/gaea/app/utils"
)

// 旧版 invitation type
type InvitationType int

const (
	_InvitationTypeMin   InvitationType = 0
	InvitationTypeAgent  InvitationType = 1
	InvitationTypeSales  InvitationType = 2
	InvitationTypeFriend InvitationType = 3
	_InvitationTypeMax   InvitationType = 4
)

func (i InvitationType) IsValid() bool {
	return i > _InvitationTypeMin && i < _InvitationTypeMax
}

type signupInput struct {
	Email                   string         `json:"email"`
	Mobile                  string         `json:"mobile"`
	Password                string         `json:"password"`
	Fullname                string         `json:"fullname"`
	IdentityNumber          string         `json:"identity_number"`
	Website                 string         `json:"website"`
	EnterpriseLicenseNumber string         `json:"enterprise_license_number"`
	OrganizationNumber      string         `json:"organization_number"`
	SocialCode              string         `json:"social_code"`
	LocationProvince        string         `json:"location_province"` // deprecated
	LocationCity            string         `json:"location_city"`     // deprecated
	RegisterIP              string         `json:"reg_ip"`
	Code                    string         `json:"code"`
	SalesInviteCode         string         `json:"sales_invite_code"`
	Referrer                string         `json:"referrer"`
	SalesInviteEmail        string         `json:"sales_invite_email"` // 销售邀请邮箱
	Promotion               string         `json:"promotion"`          // 市场部注册活动代号, base64 encoding
	SignupSource            string         `json:"signup_source"`      // 注册来源标记，市场部移动端标记为 marketing_mobile https://jira.qiniu.io/browse/BO-6369
	InvitationType          InvitationType `json:"invitation_type"`    // 邀请注册类型(目前支持代理商邀请七牛用户，代迁移的包括销售邀请/好友邀请/云大师邀请等)
	InvitationKey           string         `json:"invitation_key"`     // 邀请注册的key值，需要结合邀请类型判断
}

func (i signupInput) Validate() bool {
	if i.Email == "" || i.Fullname == "" || i.Password == "" || i.Mobile == "" {
		return false
	}

	// 此处为国内手机号注册，因此暂不替换成国内国外通用的校验方法 `func base.IsValidPhone(phone string) bool`
	if !utils.IsValidEmail(i.Email) && !utils.IsValidPhone(i.Mobile) {
		return false
	}

	return true
}

func (c *DeveloperMgr) existsWithMobile(mobile string, res *api.JsonResult) error {
	// 测试手机号跳过次数校验
	if global.Env.TestMobile != "" {
		for _, m := range strings.Split(global.Env.TestMobile, ",") {
			if m == mobile {
				return nil
			}
		}
	}

	count, err := c.Model.Developer.ExistsWithPhoneNumber(mobile)
	if err != nil {
		c.Log.Errorf("<Developer.mobileExists> Developer.ExistsWithPhoneNumber(%s) failed: %s+v", mobile, err)
		res.Code = api.DatabaseError
		return err
	}

	if count >= models.MAX_RECORDS_PER_PHONE_NUMBER {
		res.Code = code.PhoneNumberBindLimit
		return errors.New(code.PhoneNumberBindLimit.Humanize())
	}

	return nil
}

func (c *DeveloperMgr) bindMobile(mobile string, dev *models.DeveloperModel, res *api.JsonResult) (err error) {
	err = c.Model.Developer.UpdateMobile(dev.Uid, mobile, true)
	if err != nil {
		res.Code = api.DatabaseError
		return
	}

	return
}

func encode(data []byte) string {
	return url.QueryEscape(base64.URLEncoding.EncodeToString(data))
}

func getMac(data []byte) []byte {
	dig := hmac.New(sha1.New, []byte(global.Env.Service.EmailActivateSecret))
	dig.Write(data)
	return dig.Sum(nil)
}

func genActiveUrlAndMac(email string, referrer string) (activateUrl, macString string) {
	data := []byte(email)
	mac := getMac(data)
	macString = base64.URLEncoding.EncodeToString(mac)
	source := encode(data)
	encodeMac := encode(mac)
	activateUrl = fmt.Sprintf("%s/api/gaea/email/confirm?d=%s&m=%s", global.Env.HOST.PortalGaeaBackend, source, encodeMac)

	if referrer != "" {
		if parsedUrl, err := url.Parse(referrer); err == nil {
			switch parsedUrl.Host {
			case "hub.qiniu.com":
				activateUrl += "&t=" + url.QueryEscape(referrer)
			}
		}
	}
	return
}

func (c *DeveloperMgr) sendActivate(uid uint32, email string, referrer string) (err error) {
	activateUrl, macString := genActiveUrlAndMac(email, referrer)

	emailActivateCode, err := c.Model.EmailActivateCode.FindByMac(macString)
	if err != nil {
		if err != qmgo.ErrNoSuchDocuments {
			c.Log.Error("EmailActivateCode.FindByMac: ", err)
			return
		}
	} else {
		err = c.Model.EmailActivateCode.Remove(emailActivateCode.Id)
		if err != nil {
			c.Log.Error("EmailActiveCode.Remove() failed:", err)
			return
		}
	}

	emailActivateCode = models.NewEmailActivateCodeModel()
	emailActivateCode.Mac = macString
	emailActivateCode.Email = email
	emailActivateCode.Uid = uid

	err = c.Model.EmailActivateCode.Save(emailActivateCode)
	if err != nil {
		c.Log.Error("emailActivateCode.Save: ", err)
		return
	}

	data := map[string]interface{}{
		"activate_url": activateUrl,
		"email":        email,
		"supportLink":  fmt.Sprintf("%s?feedback=true", global.Env.HOST.SupportLink),
	}

	c.Log.Infof("send activate email. uid: %d, data: %+v", uid, data)

	var tpl = "10_email_activate"

	err = c.Notifier.SendMessage(c.Req.Context(), &messageSDK.SendMessageReq{
		UID:          int64(uid),
		ChannelID:    "10",
		TemplateID:   tpl,
		TemplateData: data,
		Params: map[string]interface{}{
			"tags": []string{"gaea", tpl},
		},
	})

	if err != nil {
		c.Log.Errorf("<DeveloperMgr.sendActivate(%d)> c.Notifier.SendMessage() failed: %s", uid, err)
	}

	return
}

// 兼容已有字段，记录邀请来源信息
// 将已有的字段匹配到新的来源模型上
// https://jira.qiniu.io/browse/BO-6841
func (c *DeveloperMgr) matchInvitationFieldsToNewSourceModel(uid uint32, inviterUID uint32, input signupInput) (err error) {
	if input.SalesInviteEmail != "" {
		// 销售邀请七牛用户
		// 前端 signup 页面 url 中的 sales_email 字段
		return c.setInvitationTypeAndKey(uid, gaeaEnums.InviteBySalesEmail, input.SalesInviteEmail)
	} else if input.SalesInviteCode != "" {
		// 销售邀请七牛用户
		// 前端 signup 页面 url 中的 sales 字段
		return c.setInvitationTypeAndKey(uid, gaeaEnums.InviteBySalesHash, input.SalesInviteCode)
	} else if input.InvitationType.IsValid() {
		if input.InvitationType == InvitationTypeSales {
			// 销售邀请代理商
			err = c.setInvitationTypeAndKey(uid, gaeaEnums.InviteAgentBySales, input.InvitationKey)
			return
		} else if input.InvitationType == InvitationTypeAgent {
			// 代理商邀请
			// TODO
			// 代理商邀请前端传入的值是 invitation_type 和 invitation_key，存到数据库中对应的字段是 invite_channel 和 invite_code
			// 新的来源模型用 invitation_type 和 invitation_key 记录来源，这里的映射关系有些乱，后面需要修一下
			err = c.setInvitationTypeAndKey(uid, gaeaEnums.InviteByAgent, input.InvitationKey)
			return
		}
	} else if inviterUID != 0 {
		// 个人邀请
		err = c.setInvitationTypeAndKey(uid, gaeaEnums.InviteByPerson, strconv.FormatInt(int64(inviterUID), 10))
		return
	}
	return
}

func (c *DeveloperMgr) setInvitationTypeAndKey(uid uint32, invitationType gaeaEnums.InvitationType, invitationKey string) (err error) {
	if err = c.Model.Developer.SetInvitationTypeAndKey(uid, invitationType, invitationKey); err != nil {
		c.Log.Errorf("<c.setInvitationTypeAndKey> c.Model.Developer.SetInvitationTypeAndKey(%d, %d, %s) failed. err: %s.", uid, invitationType, invitationKey, err)
	}
	return
}

type SendActivateEmailInput struct {
	UID   uint32 `json:"uid"`
	Email string `json:"email"`
}

type SignUpMessage struct {
	UID          uint64            `json:"uid"`
	Email        string            `json:"email"`
	CurrencyType string            `json:"currency_type"`
	IP           string            `json:"ip"`
	Time         time.Time         `json:"time"`
	Cookies      map[string]string `json:"cookies"`
}

func (c *DeveloperMgr) publishSignUpMessage(req *http.Request, developer *models.DeveloperModel) error {
	cookies := make(map[string]string, 0)

	for _, cookie := range req.Cookies() {
		cookies[cookie.Name] = cookie.Value
	}
	message := SignUpMessage{
		UID:          uint64(developer.Uid),
		Email:        developer.Email,
		CurrencyType: developer.CurrencyType,
		IP:           developer.RegisterIp,
		Time:         time.Now(),
		Cookies:      cookies,
	}
	msg, err := json.Marshal(message)
	if err != nil {
		c.Log.Errorf("<Signup.publishSignUpMessage> Marshal failed, err: %s", err)
		return err
	}

	if c.EventBus == nil {
		err = errors.New("eventbus is nill")
		c.Log.Errorf("eventbus is nil, err: %s", err)
		return err
	}

	err = c.EventBus.Publish(eventbus.PortalUserSignUpTopic, msg)
	if err != nil {
		c.Log.Errorf("<DeveloperMgr.publishSignUpMessage> eventbus publish message failed: %+v, err: %s", message, err)
		return err
	}
	return nil
}
