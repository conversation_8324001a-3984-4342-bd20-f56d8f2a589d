package developer

import (
	"fmt"
	"testing"

	"qiniu.io/gaea/app/mock"
	"qiniu.io/gaea/app/mock/utils"

	"net/http"

	"github.com/teapots/teapot"
	"qbox.us/biz/services.v2/account"
)

func TestGetUserInfo(t *testing.T) {
	app := mock.NewMockApp(t)
	defer app.Close()
	app.Route(
		teapot.Router("/api/developer/userinfo", teapot.Get(&DeveloperMgr{}).Action("GetUserInfo")),
	)

	host := app.Run()
	url := fmt.Sprintf("%s/api/developer/userinfo", host)

	// test invalid args
	err := utils.DoRequest(http.MethodGet, url, nil, false, nil, nil)
	if err == nil {
		t.Error("expected: 400, got: nil")
	}
	// test find by uid
	var uid uint32 = 1
	var info1 account.Info
	url1 := fmt.Sprintf("%s?uid=%d", url, uid)
	err = utils.DoRequest(http.MethodGet, url1, nil, false, nil, &info1)
	if err != nil {
		t.Error(err)
	}

	if uid != info1.Uid {
		t.Errorf("expected: %d, got: %d", uid, info1.Uid)
	}

	var email string = "<EMAIL>"
	var info2 account.Info
	url2 := fmt.Sprintf("%s?email=%s", url, email)
	err = utils.DoRequest(http.MethodGet, url2, nil, false, nil, &info2)
	if err != nil {
		t.Error(err)
	}

	if email != info2.Email {
		t.Errorf("expected: %s, got: %s", email, info2.Email)
	}
}

func TestGetUserInfosByUids(t *testing.T) {
	app := mock.NewMockApp(t)
	defer app.Close()
	app.Route(
		teapot.Router("/api/developer/userinfos/by-uids", teapot.Post(&DeveloperMgr{}).Action("GetUserInfosByUids")),
	)
	host := app.Run()

	var acc account.AdminAccountService
	injector := app.Tea.Injector()
	err := injector.Find(&acc, "admin")
	if err != nil {
		t.Fatal(err)
	}

	info, err := acc.UserCreateByPassword("<EMAIL>", "12345!@#")
	if err != nil {
		t.Fatal(err)
	}

	url := fmt.Sprintf("%s/api/developer/userinfos/by-uids", host)
	err = utils.DoRequest(http.MethodPost, url, nil, true, nil, nil)
	if err == nil {
		t.Error("expected: 400, got: nil")
	}

	uids := []uint32{1, info.Uid}
	var infos []account.Info
	err = utils.DoRequest(http.MethodPost, url, uids, true, nil, &infos)
	if err != nil {
		t.Fatal(err)
	}

	if len(infos) != len(uids) {
		t.Errorf("expected: %d, got: %d", len(uids), len(infos))
	}
}
