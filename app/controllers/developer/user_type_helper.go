package developer

import (
	"context"
	"fmt"
	"time"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/qbox/pay-sdk/message"
	messageSDK "github.com/qbox/pay-sdk/message"

	"qbox.us/biz/services.v2/account"
	"qbox.us/biz/utils.v2/types"

	"qiniu.io/gaea/app/env/global"
	"qiniu.io/gaea/app/models"
)

const (
	DefaultTimeLayout = "01-02 15:04"
)

func (mgr *UserTypeMgr) sendUserEnableNotification(userinfo account.Info) error {
	msg := message.SendMessageReq{
		UID:        int64(userinfo.Uid),
		ChannelID:  "13",
		TemplateID: "freeze_over",
	}
	opTemp := mgr.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "账户解冻",
			Other: "账户解冻",
		},
	})
	msg.TemplateData = map[string]interface{}{
		"op":          opTemp,
		"name":        "",
		"email":       userinfo.Email,
		"time":        time.Now().In(mgr.loc).Format(DefaultTimeLayout),
		"unsubscribe": global.Env.HOST.Unsubscribe,
	}

	if err := mgr.notifier.SendMessage(context.Background(), &msg); err != nil {
		return fmt.Errorf("notificationService.SendMessage(%#v) failed: %s", msg, err)
	}

	return nil
}

func (mgr *UserTypeMgr) sendSalesEnableNotification(userinfo account.Info) error {
	var (
		salesEmail string
	)

	salesEmail, err := mgr.getSalesEmailByUID(userinfo.Uid)
	if err != nil {
		return err
	}

	data := map[string]interface{}{
		"email": userinfo.Email,
		"time":  time.Now().In(mgr.loc).Format(DefaultTimeLayout),
	}

	var channelID = "internal_message"
	var tpl = "freeze_tpl_unfreeze_sales_notify"

	_, err = mgr.notifier.SendTemplateEmail(context.Background(), &messageSDK.SendTemplateEmailReq{
		SendMailReq: messageSDK.SendMailReq{
			UID:  userinfo.Uid,
			To:   []string{salesEmail},
			Tag:  []string{"gaea", tpl},
			From: global.Env.Email.PortalEmail,
		},
		SendTemplateReq: messageSDK.SendTemplateReq{
			ChannelID:    channelID,
			TemplateID:   tpl,
			TemplateData: data,
		},
	})
	if err != nil {
		return fmt.Errorf("msgService.SendTemplateEmail(%d, %s) failed: %s", userinfo.Uid, salesEmail, err)
	}

	return nil
}

func (mgr *UserTypeMgr) sendSalesDisableNotification(userinfo account.Info, balance, consumption int64) (err error) {
	var (
		salesEmail string
		tpl        string
		channelID  = "internal_message"
		data       map[string]interface{}
	)

	salesEmail, err = mgr.getSalesEmailByUID(userinfo.Uid)
	if err != nil {
		return err
	}

	if userinfo.DisabledType == account.DISABLED_TYPE_AUTO {
		balanceStr := ""
		consumptionStr := ""
		if balanceStr != "" {
			balanceStr = types.Money(balance).String()
		}
		if consumptionStr != "" {
			consumptionStr = types.Money(consumption).String()
		}

		data = map[string]interface{}{
			"email":       userinfo.Email,
			"time":        time.Now().In(mgr.loc).Format(DefaultTimeLayout),
			"balance":     balanceStr,
			"consumption": consumptionStr,
		}

		tpl = "freeze_for_arrearage_sales_notify"
	} else {
		data = map[string]interface{}{
			"email":  userinfo.Email,
			"reason": userinfo.DisabledReason,
		}

		tpl = "freeze_for_policy_sales_notify"
	}

	_, err = mgr.notifier.SendTemplateEmail(context.Background(), &messageSDK.SendTemplateEmailReq{
		SendMailReq: messageSDK.SendMailReq{
			UID:  userinfo.Uid,
			To:   []string{salesEmail},
			Tag:  []string{"gaea", tpl},
			From: global.Env.Email.PortalEmail,
		},
		SendTemplateReq: messageSDK.SendTemplateReq{
			ChannelID:    channelID,
			TemplateID:   tpl,
			TemplateData: data,
		},
	})
	if err != nil {
		return fmt.Errorf("mgr.notifier.SendTemplateEmail(%d, %s) failed: %s", userinfo.Uid, salesEmail, err)
	}

	return nil
}

func (mgr *UserTypeMgr) sendUserDisableNotification(userinfo account.Info, balance, consumption int64) error {

	var (
		// send to user
		msg = message.SendMessageReq{
			UID:       int64(userinfo.Uid),
			ChannelID: "13",
		}
	)

	opTemp := mgr.Localizer.MustLocalize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID:    "账户冻结",
			Other: "账户冻结",
		},
	})

	if userinfo.DisabledType == account.DISABLED_TYPE_AUTO {
		msg.TemplateID = "freeze_for_arrearage"
		msg.TemplateData = map[string]interface{}{
			"op":               opTemp,
			"name":             "",
			"email":            userinfo.Email,
			"time":             time.Now().In(mgr.loc).Format(DefaultTimeLayout),
			"balance":          types.Money(balance).String(),
			"consumption":      types.Money(consumption).String(),
			"frozenPolicyLink": global.Env.HOST.FreezePolicyLink,
			"rechargeLink":     global.Env.HOST.RechargeLink,
			"supportLink":      global.Env.HOST.SupportLink,
			"unsubscribe":      global.Env.HOST.Unsubscribe,
		}
	} else {
		msg.TemplateID = "freeze_for_policy"
		msg.TemplateData = map[string]interface{}{
			"op":               opTemp,
			"name":             "",
			"email":            userinfo.Email,
			"reason":           userinfo.DisabledReason,
			"frozenPolicyLink": global.Env.HOST.FreezePolicyLink,
			"supportLink":      global.Env.HOST.SupportLink,
			"unsubscribe":      global.Env.HOST.Unsubscribe,
		}
	}

	if err := mgr.notifier.SendMessage(context.Background(), &msg); err != nil {
		return fmt.Errorf("notificationService.SendMessage(%#v) failed: %s", msg, err)
	}

	return nil
}

func (mgr *UserTypeMgr) Validate(userType account.UserType, newUserType account.UserType) bool {
	// unsupported operations:
	// 1. 	disable parent type
	// 2. 	enterprise to personal
	// 3. 	std to exp
	// 4.	exp to vip
	// 5. 	disable admin type
	// 6. 	disable vip
	// 7. 	disable qcos
	// 8. 	disable evm
	// 9. 	disable pili
	// 10. 	disable fusion
	if userType.IsParentUser() && !newUserType.IsParentUser() {
		return false
	}

	if userType.IsEnterpriseUser() && !newUserType.IsEnterpriseUser() {
		return false
	}

	if isStdUser(userType) && !isStdUser(newUserType) {
		return false
	}

	// BO-13352 https://jira.qiniu.io/browse/BO-13352
	// if userType&account.USER_TYPE_EXPUSER > 0 && newUserType&account.USER_TYPE_VIP > 0 {
	// 	return false
	// }

	if userType.IsAdmin() && !newUserType.IsAdmin() {
		return false
	}

	if userType&account.USER_TYPE_VIP > 0 && newUserType&account.USER_TYPE_VIP == 0 {
		return false
	}

	if userType.IsQCosUser() && !newUserType.IsQCosUser() {
		return false
	}

	if userType.IsCCUser() && !newUserType.IsCCUser() {
		return false
	}

	if userType.IsPiliUser() && !userType.IsPiliUser() {
		return false
	}

	if userType.IsFusionUser() && !userType.IsFusionUser() {
		return false
	}

	return true
}

func isStdUser(userType account.UserType) bool {
	return userType&account.USER_TYPE_STDUSER > 0 && userType&account.USER_TYPE_EXPUSER == 0
}

func (c *UserTypeMgr) getSalesEmailByUID(uid uint32) (email string, err error) {
	var (
		modelMgr models.ModelMgr
		user     models.User
	)

	err = c.Context.Find(&modelMgr, "")
	if err != nil {
		return
	}

	user, err = modelMgr.GetSalesByUid(uid)
	if err != nil {
		return
	}

	email = user.Email
	return email, err
}
