package developer

import (
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/qiniu/xlog.v1"
	"github.com/teapots/teapot"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/qbox/bo-base/v4/base"
	"github.com/qbox/bo-base/v4/intl"
	"github.com/qbox/bo-base/v4/retry"
	"github.com/qbox/pay-sdk/message"

	"qbox.us/api/fusion/fusion"
	"qbox.us/api/fusion/fusiondomain"
	"qbox.us/biz/services.v2/account"

	"qiniu.io/gaea/app/models"
	priceService "qiniu.io/gaea/app/services/price.v3"
	"qiniu.io/gaea/app/utils"
)

type UserTypeMgr struct {
	Log                 teapot.ReqLogger
	Context             teapot.Context
	AdminAccountService account.AdminAccountService
	Localizer           intl.Localizer
	notifier            message.Notifier
	loc                 *time.Location
}

type ExtraParam struct {
	NotifyUserWhenDisabled  *bool
	NotifySalesWhenDisabled *bool
	NotifyUserWhenEnabled   *bool
	NotifySalesWhenEnabled  *bool
	// used when send disabled mail
	Consumption *int64
	Balance     *int64
	Day         *base.Day // 免费额度生效起始时间。如果不填，默认以调用当天为起始点。
}

func NewUserTypeMgr(ctx teapot.Context, loc *time.Location) (userTypeMgr UserTypeMgr, err error) {
	var (
		log                 teapot.ReqLogger
		adminAccountService account.AdminAccountService
		localizer           intl.Localizer
		notifier            message.Notifier
	)

	if err = ctx.Find(&log, ""); err != nil {
		return
	}

	if err = ctx.Find(&adminAccountService, ""); err != nil {
		return
	}

	if err = ctx.Find(&localizer, ""); err != nil {
		return
	}

	if err = ctx.Find(&notifier, ""); err != nil {
		return
	}

	userTypeMgr = UserTypeMgr{
		Log:                 log,
		Context:             ctx,
		AdminAccountService: adminAccountService,
		Localizer:           localizer,
		loc:                 loc,
		notifier:            notifier,
	}

	return userTypeMgr, err
}

func (mgr *UserTypeMgr) Update(userinfo, newUserinfo account.Info, extraParams ExtraParam) error {
	var (
		// diff userType or changed userType
		userTypeChanged           = userinfo.UserType ^ newUserinfo.UserType
		stdUpgrade                = utils.IsExpUser(userinfo.UserType) && utils.IsStdUser(newUserinfo.UserType)
		disabled                  = !userinfo.UserType.IsDisabled() && newUserinfo.UserType.IsDisabled()
		parentUpgrade             = !userinfo.UserType.IsParentUser() && newUserinfo.UserType.IsParentUser()
		vipUpgrade                = !utils.IsVipUser(userinfo.UserType) && utils.IsVipUser(newUserinfo.UserType)
		onlyCanGetChildKeyChanged bool
		params                    = url.Values{}

		priceService priceService.IPriceV3Service
		modelMgr     *models.ModelMgr
		vendorInfo   *account.Info
	)

	if err := mgr.Context.Find(&priceService, ""); err != nil {
		return err
	}

	if err := mgr.Context.Find(&modelMgr, ""); err != nil {
		return err
	}

	if err := mgr.Context.Find(&vendorInfo, "oem"); err != nil {
		return err
	}

	if !mgr.Validate(userinfo.UserType, newUserinfo.UserType) {
		return errors.New("forbidden")
	}

	if parentUpgrade {
		params.Set("child_email_domain", newUserinfo.ChildEmailDomain)
		params.Set("can_get_child_key", strconv.FormatBool(newUserinfo.CanGetChildKey))
	} else if userinfo.CanGetChildKey != newUserinfo.CanGetChildKey {
		params.Set("can_get_child_key", strconv.FormatBool(newUserinfo.CanGetChildKey))
		onlyCanGetChildKeyChanged = true
	}

	if disabled {
		if _, err := mgr.AdminAccountService.UserDisable(newUserinfo.Uid, newUserinfo.DisabledReason, newUserinfo.DisabledType); err != nil {
			return fmt.Errorf("mgr.AdminAccountService.UserDisable(%d, %s, %d) failed: %s", newUserinfo.Uid, newUserinfo.DisabledReason, newUserinfo.DisabledType, err)
		}
	}

	if parentUpgrade || onlyCanGetChildKeyChanged {
		if _, err := mgr.AdminAccountService.UserUpdate(newUserinfo.Uid, params); err != nil {
			return fmt.Errorf("mgr.AdminAccountService.UserUpdate(%d, %+v) failed: %s", newUserinfo.Uid, params, err)
		}
	}

	if userinfo.UserType != newUserinfo.UserType {
		retryer := retry.New(
			retry.WithMaxRetry(3),
			retry.WithRandomWaitTime(time.Second, 2*time.Second),
		)

		err := retryer.Do(func() error {
			_, err1 := mgr.AdminAccountService.UserSetUserType(newUserinfo.Email, uint32(newUserinfo.UserType))
			return err1
		})
		if err != nil {
			return fmt.Errorf("mgr.AdminAccountService.UserSetUserType(%s, %d) failed: %s", newUserinfo.Email, newUserinfo.UserType, err)
		}
	}

	// std user
	if stdUpgrade {
		//update developer info
		change := bson.M{
			"upgrade_std_at": time.Now(),
		}
		if err := modelMgr.Developer.UpdateSetByUID(newUserinfo.Uid, change); err != nil {
			return fmt.Errorf("Developer.UpdateSetByUID(%d, %+v) failed: %s", newUserinfo.Uid, change, err)
		}
	}

	if vipUpgrade {
		change := bson.M{
			"upgrade_vip_at": time.Now(),
		}
		if err := modelMgr.Developer.UpdateSetByUID(newUserinfo.Uid, change); err != nil {
			return fmt.Errorf("Developer.UpdateSetByUID(%d, %+v) failed: %s", newUserinfo.Uid, change, err)
		}
	}

	// disable or enable operation
	if userTypeChanged&account.USER_TYPE_DISABLED > 0 {
		if newUserinfo.UserType.IsDisabled() {
			if err := mgr.handleDisable(newUserinfo, extraParams); err != nil {
				return err
			}
		} else {
			if err := mgr.handleEnable(newUserinfo, extraParams); err != nil {
				return err
			}
		}
	}

	return nil

}

func (mgr *UserTypeMgr) handleEnable(userinfo account.Info, extra ExtraParam) error {
	var (
		notifyUser  = extra.NotifyUserWhenEnabled == nil || *extra.NotifyUserWhenEnabled
		notifySales = extra.NotifySalesWhenEnabled == nil || *extra.NotifySalesWhenEnabled
	)

	mgr.enableProducts(userinfo)

	if !notifyUser && !notifySales {
		return nil
	}

	if notifyUser {
		if err := mgr.sendUserEnableNotification(userinfo); err != nil {
			mgr.Log.Errorf("<UserTypeMgr.handleEnable> mgr.sendUserEnableNotification(%#v) failed: %s", userinfo, err)
		}
	}

	if notifySales {
		if err := mgr.sendSalesEnableNotification(userinfo); err != nil {
			mgr.Log.Errorf("<UserTypeMgr.handleEnable> mgr.sendSalesDisableNotification(%#v) failed: %s", userinfo, err)
		}
	}

	return nil

}

func (mgr *UserTypeMgr) handleDisable(userinfo account.Info, extra ExtraParam) error {
	var (
		notifyUser  = extra.NotifyUserWhenDisabled == nil || *extra.NotifyUserWhenDisabled
		notifySales = extra.NotifySalesWhenDisabled == nil || *extra.NotifySalesWhenDisabled
		balance     int64
		consumption int64
	)

	mgr.disableProducts(userinfo)

	if !notifyUser && !notifySales {
		return nil
	}

	if extra.Balance != nil && extra.Consumption != nil {
		balance = *extra.Balance
		consumption = *extra.Consumption
	}

	if notifyUser {
		if err := mgr.sendUserDisableNotification(userinfo, balance, consumption); err != nil {
			mgr.Log.Errorf("<UserTypeMgr.handleDisable> mgr.sendUserDisableNotification(%#v, %d, %d) failed: %s", userinfo, balance, consumption, err)
		}
	}

	if notifySales {
		if err := mgr.sendSalesDisableNotification(userinfo, balance, consumption); err != nil {
			mgr.Log.Errorf("<UserTypeMgr.handleDisable> mgr.sendSalesDisableNotification(%#v, %d, %d) failed: %s", userinfo, balance, consumption, err)
		}
	}

	return nil
}

func (mgr *UserTypeMgr) disableProducts(userinfo account.Info) error {
	var (
		fusionService *fusiondomain.ClientV2
	)
	// fusion
	err := mgr.Context.Find(&fusionService, "")
	if err != nil {
		mgr.Log.Errorf("<UserTypeMgr.disableProducts> mgr.Context.Find($fusionService, \"\") failed: %s", err)
		return err
	}

	fusionReq := fusion.FreezeUserDomainsArgs{
		Uid:     userinfo.Uid,
		Message: userinfo.DisabledReason,
	}

	taskID, err := fusionService.Freeze(xlog.NewWith(mgr.Log), &fusionReq)
	if err != nil {
		mgr.Log.Errorf("<UserTypeMgr.disableProducts> fusionService.Freeze(mgr.Context, %v) failed: %s", fusionReq, err)
		return err
	}

	mgr.Log.Infof("<UserTypeMgr.disableProducts> fusionService.Freeze(mgr.Context, %d) taskID:%s", userinfo.Uid, taskID)

	return nil
}

func (mgr *UserTypeMgr) enableProducts(userinfo account.Info) error {
	var (
		fusionService *fusiondomain.ClientV2
	)
	// fusion
	err := mgr.Context.Find(&fusionService, "")
	if err != nil {
		mgr.Log.Errorf("<UserTypeMgr.enableProducts> mgr.Context.Find($fusionService, \"\") failed: %s", err)
		return err
	}

	fusionReq := fusion.FreezeUserDomainsArgs{
		Uid: userinfo.Uid,
	}

	taskID, err := fusionService.UnFreeze(xlog.NewWith(mgr.Log), &fusionReq)
	if err != nil {
		mgr.Log.Errorf("<UserTypeMgr.enableProducts> fusionService.UnFreeze(nil, %v) failed: %s", fusionReq, err)
		return err
	}

	mgr.Log.Infof("<UserTypeMgr.enableProducts> fusionService.UnFreeze(nil, %d) taskID: %s", userinfo.Uid, taskID)

	return nil
}
