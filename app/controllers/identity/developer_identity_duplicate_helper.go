package identity

import (
	"context"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/exp/slices"

	"qbox.us/biz/component/api"

	"qiniu.io/gaea/app/code"
	"qiniu.io/gaea/app/env/global"
	"qiniu.io/gaea/app/models"
)

const (
	maxRecordsPerIdentifyNumber         = 2
	maxRecordsPerAlipayUID              = 2
	maxRecordsPerEnterprise             = 3
	maxRecordsPerEnterpriseForWhitelist = 10
)

type IdentityDuplicateField string

const (
	fieldContactIdentityNumber  IdentityDuplicateField = "contact_identity_no"
	fieldAlipayUID              IdentityDuplicateField = "alipay_uid"
	fieldEnterpriseCode         IdentityDuplicateField = "enterprise_code"
	fieldAlipayUidBeforeUpgrade IdentityDuplicateField = "alipay_uid_before_upgrade"
)

// DuplicateHumanize 认证信息字段重复错误码
func (t IdentityDuplicateField) ErrorCode() api.Code {
	switch t {
	case fieldContactIdentityNumber:
		return code.IdentityIDCardNumberDuplicate
	case fieldAlipayUID:
		return code.IdentityAlipayUIDDuplicate
	default:
		return code.IdentityPreCheckFailed
	}
}

func (c *DeveloperIdentity) duplicateValidate(
	identity models.DeveloperIdentityModel,
) (attr IdentityDuplicateField, ok bool, err error) {
	var duplicateCnt int64

	if identity.IsEnterprise() {
		// 企业支付宝认证冲突校验 alipay_uid
		if identity.IsAlipayVerification() && identity.AlipayUid != "" {
			// 所有企业支付宝类型认证成功并使用当前支付宝的情况
			duplicateCnt, err = c.Model.DeveloperIdentity.Find(
				context.Background(),
				bson.M{
					"uid": bson.M{
						"$ne": identity.Uid,
					},
					"type": bson.M{
						"$in": []models.IdentityType{
							models.IdentityTypeEnterpriseBusinessAlipay,
							models.IdentityTypeEnterpriseOrganizationAlipay,
							models.IdentityTypeEnterpriseUnifiedSocialAlipay,
						},
					},
					"status":               models.DeveloperIdentitySuccess,
					string(fieldAlipayUID): identity.AlipayUid,
					// NOTE: 从父账号继承的认证信息，不占用可用次数
					"parent_uid": bson.M{
						"$lte": 0,
					},
				}).Count()

			if err != nil {
				return
			}

			if duplicateCnt >= maxRecordsPerAlipayUID {
				return fieldAlipayUID, false, nil
			}
		}

		if identity.EnterpriseCode != "" {
			maxRecordsPerEnterpriseTemp := global.Env.DeveloperIdentity.MaxRecordsPerEnterprise
			if maxRecordsPerEnterpriseTemp <= 0 {
				maxRecordsPerEnterpriseTemp = maxRecordsPerEnterprise
			}
			enterpriseCodeWhitelist := strings.Split(global.Env.DeveloperIdentity.EnterpriseCodeWhitelist, ",")
			if len(enterpriseCodeWhitelist) > 0 && slices.Contains(enterpriseCodeWhitelist, string(identity.EnterpriseCode)) {
				maxRecordsPerEnterpriseTemp = global.Env.DeveloperIdentity.MaxRecordsPerEnterpriseForWhitelist
				if maxRecordsPerEnterpriseTemp <= 0 {
					maxRecordsPerEnterpriseTemp = maxRecordsPerEnterpriseForWhitelist
				}
			}
			queryCondTemp := bson.M{
				"uid": bson.M{
					"$ne": identity.Uid,
				},
				"type": bson.M{
					"$in": []models.IdentityType{
						models.IdentityTypeEnterpriseBusiness,
						models.IdentityTypeEnterpriseOrganization,
						models.IdentityTypeEnterpriseUnifiedSocial,
						models.IdentityTypeSalesGuarantee,
						models.IdentityTypeEnterpriseLegalPerson,
					},
				},
				"status":          models.DeveloperIdentitySuccess,
				"enterprise_code": identity.EnterpriseCode,
			}
			// NOTE: 从父账号继承的认证信息，不占用可用次数
			// 此处拆成了两次插叙，避免 or 条件降低查询效率
			var duplicateCntTemp int64

			queryCond1 := queryCondTemp
			queryCond1["parent_uid"] = bson.M{
				"$lte": 0,
			}
			duplicateCntTemp, err = c.Model.DeveloperIdentity.Find(context.Background(), queryCond1).Count()
			if err != nil {
				return
			}
			duplicateCnt = duplicateCntTemp
			if duplicateCnt >= int64(maxRecordsPerEnterpriseTemp) {
				return fieldEnterpriseCode, false, nil
			}

			queryCond2 := queryCondTemp
			queryCond2["parent_uid"] = bson.M{
				"$exists": false,
			}
			duplicateCntTemp, err = c.Model.DeveloperIdentity.Find(context.Background(), queryCond2).Count()
			if err != nil {
				return
			}
			duplicateCnt += duplicateCntTemp
			if duplicateCnt >= int64(maxRecordsPerEnterpriseTemp) {
				return fieldEnterpriseCode, false, nil
			}
		}
	}

	// 个人认证都需要身份证信息, 冲突校验 contact_identity_no
	if identity.IsPersonal() {
		duplicateCnt, err = c.Model.DeveloperIdentity.Find(
			context.Background(),
			bson.M{
				"uid": bson.M{
					"$ne": identity.Uid,
				},
				// NOTE: 从父账号继承的认证信息，不占用可用次数
				// {$not: {$gt: 0}} 与 {$lte: 0} 不等价，后者不能匹配字段不存在的情况
				"parent_uid": bson.M{
					"$not": bson.M{"$gt": 0},
				},
				"$or": []bson.M{
					// 1. 认证类型为个人银行/个人支付宝/个人人脸活体，状态为成功 => 认证成功的个人身份认证会消耗该身份证的剩余可认证次数
					{
						"type": bson.M{
							"$in": []models.IdentityType{
								models.IdentityTypePersonalManual,
								models.IdentityTypePersonalAlipay,
								models.IdentityTypePersonalFaceActionLive,
								models.IdentityTypePersonalBankFourMeta,
							},
						},
						"status":                           models.DeveloperIdentitySuccess,
						string(fieldContactIdentityNumber): identity.ContactIdentityNo,
					},
					// 2. 认证类型不为个人银行/个人支付宝，状态为升级中/升级失败 => 处于升级阶段的个人身份认证会消耗该身份证的剩余可认证次数
					{
						"status": bson.M{
							"$in": []models.DeveloperIdentityStatus{
								models.DeveloperIdentityUpgrading,
								models.DeveloperIdentityUpgradeFailed,
							},
						},
						string(fieldContactIdentityNumber): identity.ContactIdentityNo,
					},
				},
			}).Count()

		if err != nil {
			return
		}

		if duplicateCnt >= maxRecordsPerIdentifyNumber {
			return fieldContactIdentityNumber, false, nil
		}

		// 个人支付宝认证冲突校验 alipay_uid
		if identity.IsAlipayVerification() && identity.AlipayUid != "" {
			duplicateCnt, err = c.Model.DeveloperIdentity.Find(
				context.Background(),
				bson.M{
					"uid": bson.M{
						"$ne": identity.Uid,
					},
					// NOTE: 从父账号继承的认证信息，不占用可用次数
					// {$not: {$gt: 0}} 与 {$lte: 0} 不等价，后者不能匹配字段不存在的情况
					"parent_uid": bson.M{
						"$not": bson.M{"$gt": 0},
					},
					"$or": []bson.M{
						// 1. 认证类型为个人支付宝，状态为成功 => 认证成功的个人支付宝身份认证会消耗该支付宝的剩余可认证次数
						{
							"type":                 identity.Type,
							"status":               models.DeveloperIdentitySuccess,
							string(fieldAlipayUID): identity.AlipayUid,
						},
						// 2. 认证类型不为个人支付宝，状态为升级中/升级失败 => 处于升级阶段的个人支付宝身份认证会消耗该支付宝的剩余可认证次数
						{
							"status": bson.M{
								"$in": []models.DeveloperIdentityStatus{
									models.DeveloperIdentityUpgrading,
									models.DeveloperIdentityUpgradeFailed,
								},
							},
							string(fieldAlipayUidBeforeUpgrade): identity.AlipayUid,
						},
					},
				}).Count()
		}

		if err != nil {
			return
		}

		if duplicateCnt >= maxRecordsPerAlipayUID {
			return fieldAlipayUID, false, nil
		}
	}

	return "", true, nil
}
