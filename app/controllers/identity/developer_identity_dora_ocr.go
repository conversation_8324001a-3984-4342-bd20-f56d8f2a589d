package identity

import (
	"context"
	"strings"
	"time"

	"github.com/qbox/sonic/sdk/trace"
	xlog "github.com/qiniu/xlog.v1"
	"qiniu.io/gaea/app/env/global"
	"qiniu.io/gaea/app/models"
	"qiniu.io/gaea/app/services/dora"
)

func (c *DeveloperIdentity) VerifyIDCard(identity *models.DeveloperIdentityModel) (ok bool, needReview bool, err error) {
	var (
		start time.Time
		end   time.Time

		ctx    = xlog.NewContextWith(trace.NewContext(c.Req.Context()), c.Log.ReqId())
		domain = global.Env.DeveloperIdentity.BucketDomain
	)

	ctx, cancel := context.WithTimeout(ctx, IdentifyDependentServiceResponseMaxTime)
	defer cancel()

	// 身份证人像面信息对比
	fURL := c.privateUrl(domain, identity.ContactIdentityPhoto)
	fImage, err := GetImageBase64(fURL)
	if err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> GetImageBase64(%s) failed, err: %s", fURL, err)
		return false, true, err
	}

	result, err := c.DoraService.IDCardOcr(ctx, fImage, global.Env.DeveloperIdentity.OCRAutoFlip)
	if err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> DoraService.IDCardOcr() failed, err: %s", err)
		return false, true, err
	}
	if result.Errorcode == 53090004 {
		identity.StatusNote = NoInfoInPhoto.Humanize()
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> IDNum disaccord, developer identity failed, err: %s", result.Errormsg)
		return false, true, nil
	}
	// 记录识别结果
	c.Log.Infof("<DeveloperIdentity.VerifyIDCard> ocrResult: %+v", result.OcrResult)

	// 身份证号是否一致
	identity.ContactIdentityNoOCR = models.IdentityNo(result.OcrResult.IDNo)
	if !strings.EqualFold(result.OcrResult.IDNo, string(identity.ContactIdentityNo)) {
		identity.StatusNote = IDCardInfoDisaccord.Humanize()
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> IDNum disaccord, developer identity failed, ocrInfo.IdNum: %s, contactIdentityNo: %s.", result.OcrResult.IDNo, string(identity.ContactIdentityNo))
		return false, false, nil
	}

	// 身份证姓名是否一致
	identity.ContactNameOCR = result.OcrResult.Name
	if strings.TrimSpace(result.OcrResult.Name) != strings.TrimSpace(identity.ContactName) {
		identity.StatusNote = IDCardInfoDisaccord.Humanize()
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> name disaccord, developer identity failed, ocrInfo.name: %s, contactname: %s.", result.OcrResult.Name, identity.ContactName)
		return false, false, nil
	}

	// 身份证国徽面信息对比
	bURL := c.privateUrl(domain, identity.ContactIdentityPhotoB)
	bImage, err := GetImageBase64(bURL)
	if err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> GetImageBase64(%s) failed, err: %s", bURL, err)
		return false, true, err
	}
	bResult, err := c.DoraService.IDCardOcr(ctx, bImage, global.Env.DeveloperIdentity.OCRAutoFlip)
	if err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> DoraService.IDCardOcr() failed, err: %s", err)
		return false, true, err
	}
	// 不是合法身份图片
	if bResult.Errorcode == 53090004 {
		identity.StatusNote = NoInfoInPhoto.Humanize()
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> IDNum disaccord, developer identity failed, err: %s", result.Errormsg)
		return false, true, nil
	}

	// 记录识别结果
	c.Log.Infof("<DeveloperIdentity.VerifyIDCard> ocrResult: %+v", bResult.OcrResult)

	// 证件是否在有效期内
	identity.ValidDate = bResult.OcrResult.Validthru
	validDateStrs := strings.Split(bResult.OcrResult.Validthru, "-")
	if validDateStrs == nil || len(validDateStrs) != 2 {
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> invalid validDateStrs(%+v), need review.", bResult.OcrResult.Validthru)
		return false, true, nil
	}
	if start, err = time.Parse(dora.IDCardBirthdayLayout, validDateStrs[0]); err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> invalid validDateStrs(%+v), need review.", validDateStrs)
		return false, true, nil
	}
	if end, err = time.Parse(dora.IDCardBirthdayLayout, validDateStrs[1]); err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> invalid validDateStrs(%+v), need review.", validDateStrs)
		return false, true, nil
	}
	if !start.IsZero() && !end.IsZero() {
		if end.Before(start) {
			c.Log.Errorf("<DeveloperIdentity.VerifyIDCard> end(%s) before start(%s), developer identity failed.", end, start)
			return false, false, nil
		}

		// 证件是否在有效期内 或者 证件将在1个月内过期
		if end.Before(time.Now().AddDate(0, 1, 0)) {
			identity.StatusNote = IDCardOverdue.Humanize()
			c.Log.Error("<DeveloperIdentity.VerifyIDCard> IDCard will expire in 1 month, developer identity failed.")
			return false, false, nil
		}
	} else {
		return false, true, nil
	}

	// 其他认证审核的信息保存下来
	identity.Gender = result.OcrResult.Gender
	identity.Issuedby = bResult.OcrResult.Issuedby

	return c.VerifyDeveloperFace(identity, bImage)
}

func (c *DeveloperIdentity) VerifyBizLicense(identity *models.DeveloperIdentityModel) (ok bool, needReview bool, err error) {
	ctx := xlog.NewContextWith(trace.NewContext(c.Req.Context()), c.Log.ReqId())
	ctx, cancel := context.WithTimeout(ctx, IdentifyDependentServiceResponseMaxTime)
	defer cancel()

	domain := global.Env.DeveloperIdentity.BucketDomain
	bizLicenseURL := c.privateUrl(domain, identity.EnterpriseCodeCopy)
	image, err := GetImageBase64(bizLicenseURL)
	if err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyBizLicense> GetImageBase64(%s) failed, err: %s", bizLicenseURL, err)
		ok = false
		needReview = true
		return
	}

	result, err := c.DoraService.BusinessLicenseOcr(ctx, image)
	if err != nil {
		c.Log.Errorf("<DeveloperIdentity.VerifyBizLicense> DoraService.BusinessLicenseOcr(%s) failed, err: %s", image, err)
		ok = false
		needReview = true
		return
	}
	if result.Errorcode != 0 {
		c.Log.Errorf("<DeveloperIdentity.VerifyBizLicense> DoraService.BusinessLicenseOcr failed, errMsg: %s, sessionId: %s", err, result.SessionId)
		ok = false
		needReview = true
		return
	}

	// 记录识别结果
	c.Log.Infof("<DeveloperIdentity.VerifyBizLicense> ocrResult: %+v", result.Items)

	// 营业执照姓名不一致
	if result.Items.Name.Value != "" {
		identity.CompanyAccountNameOCR = result.Items.Name.Value
		if identity.CompanyAccountName != result.Items.Name.Value {
			ok = false
			needReview = false
			identity.StatusNote = BizLicenseInfoDisaccord.Humanize()
			c.Log.Error("<DeveloperIdentity.VerifyBizLicense> name disaccord, enterprise identity failed.")
		}
	}

	// 企业注册号不一致
	if result.Items.CreditCode.Value != "" {
		identity.EnterpriseCodeOCR = models.EnterpriseCode(result.Items.CreditCode.Value)
		if !strings.EqualFold(result.Items.CreditCode.Value, string(identity.EnterpriseCode)) {
			ok = false
			needReview = false
			identity.StatusNote = BizLicenseInfoDisaccord.Humanize()
			c.Log.Error("<DeveloperIdentity.VerifyBizLicense> enterpriseCode disaccord, enterprise identity failed.")
		}
	}

	// 保留其他企业营业执照信息
	if result.Items.Address.Value != "" {
		identity.EnterpriseAddress = result.Items.Address.Value
	}
	if result.Items.Type.Value != "" {
		identity.EnterpriseType = result.Items.Type.Value
	}
	if result.Items.LegalRepresentative.Value != "" {
		identity.LegalPerson = result.Items.LegalRepresentative.Value
	}
	if result.Items.OperationTerm.Value != "" {
		identity.BizPeriod = result.Items.OperationTerm.Value
	}
	if result.Items.RegisteredCapital.Value != "" {
		identity.Capital = result.Items.RegisteredCapital.Value
	}
	if result.Items.BusinessScope.Value != "" {
		identity.Business = result.Items.BusinessScope.Value
	}

	// 企业认证均需要人工审核
	ok = true
	needReview = true
	return
}
