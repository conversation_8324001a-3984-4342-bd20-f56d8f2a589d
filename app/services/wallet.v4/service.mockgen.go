// Code generated by MockGen. DO NOT EDIT.
// Source: app/services/wallet.v4/service.go

// Package walletv4 is a generated GoMock package.
package walletv4

import (
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	base "github.com/qbox/bo-base/v4/base"
	enums "qiniu.io/gaea/app/enums"
)

// MockIWalletServiceV4 is a mock of IWalletServiceV4 interface.
type MockIWalletServiceV4 struct {
	ctrl     *gomock.Controller
	recorder *MockIWalletServiceV4MockRecorder
}

// MockIWalletServiceV4MockRecorder is the mock recorder for MockIWalletServiceV4.
type MockIWalletServiceV4MockRecorder struct {
	mock *MockIWalletServiceV4
}

// NewMockIWalletServiceV4 creates a new mock instance.
func NewMockIWalletServiceV4(ctrl *gomock.Controller) *MockIWalletServiceV4 {
	mock := &MockIWalletServiceV4{ctrl: ctrl}
	mock.recorder = &MockIWalletServiceV4MockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIWalletServiceV4) EXPECT() *MockIWalletServiceV4MockRecorder {
	return m.recorder
}

// AddUserCurrency mocks base method.
func (m *MockIWalletServiceV4) AddUserCurrency(req *ReqUserCurrencyAdd) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddUserCurrency", req)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddUserCurrency indicates an expected call of AddUserCurrency.
func (mr *MockIWalletServiceV4MockRecorder) AddUserCurrency(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddUserCurrency", reflect.TypeOf((*MockIWalletServiceV4)(nil).AddUserCurrency), req)
}

// BindCardToUser mocks base method.
func (m *MockIWalletServiceV4) BindCardToUser(uid uint64, card Card) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BindCardToUser", uid, card)
	ret0, _ := ret[0].(error)
	return ret0
}

// BindCardToUser indicates an expected call of BindCardToUser.
func (mr *MockIWalletServiceV4MockRecorder) BindCardToUser(uid, card interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BindCardToUser", reflect.TypeOf((*MockIWalletServiceV4)(nil).BindCardToUser), uid, card)
}

// GetBankVirtualAccountByUID mocks base method.
func (m *MockIWalletServiceV4) GetBankVirtualAccountByUID(uid uint32) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBankVirtualAccountByUID", uid)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBankVirtualAccountByUID indicates an expected call of GetBankVirtualAccountByUID.
func (mr *MockIWalletServiceV4MockRecorder) GetBankVirtualAccountByUID(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBankVirtualAccountByUID", reflect.TypeOf((*MockIWalletServiceV4)(nil).GetBankVirtualAccountByUID), uid)
}

// GetPaymentTransactionByEntryID mocks base method.
func (m *MockIWalletServiceV4) GetPaymentTransactionByEntryID(prefix, typ, excode string, uid int64) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentTransactionByEntryID", prefix, typ, excode, uid)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentTransactionByEntryID indicates an expected call of GetPaymentTransactionByEntryID.
func (mr *MockIWalletServiceV4MockRecorder) GetPaymentTransactionByEntryID(prefix, typ, excode, uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentTransactionByEntryID", reflect.TypeOf((*MockIWalletServiceV4)(nil).GetPaymentTransactionByEntryID), prefix, typ, excode, uid)
}

// GetPaymentTransactionByPaymentID mocks base method.
func (m *MockIWalletServiceV4) GetPaymentTransactionByPaymentID(paymentID string) (*PaymentTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentTransactionByPaymentID", paymentID)
	ret0, _ := ret[0].(*PaymentTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentTransactionByPaymentID indicates an expected call of GetPaymentTransactionByPaymentID.
func (mr *MockIWalletServiceV4MockRecorder) GetPaymentTransactionByPaymentID(paymentID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentTransactionByPaymentID", reflect.TypeOf((*MockIWalletServiceV4)(nil).GetPaymentTransactionByPaymentID), paymentID)
}

// GetSingleCurrencyTypeForUID mocks base method.
func (m *MockIWalletServiceV4) GetSingleCurrencyTypeForUID(uid uint64) (base.CurrencyType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingleCurrencyTypeForUID", uid)
	ret0, _ := ret[0].(base.CurrencyType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSingleCurrencyTypeForUID indicates an expected call of GetSingleCurrencyTypeForUID.
func (mr *MockIWalletServiceV4MockRecorder) GetSingleCurrencyTypeForUID(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingleCurrencyTypeForUID", reflect.TypeOf((*MockIWalletServiceV4)(nil).GetSingleCurrencyTypeForUID), uid)
}

// GetUserDefaultCard mocks base method.
func (m *MockIWalletServiceV4) GetUserDefaultCard(uid uint64) (*Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDefaultCard", uid)
	ret0, _ := ret[0].(*Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserDefaultCard indicates an expected call of GetUserDefaultCard.
func (mr *MockIWalletServiceV4MockRecorder) GetUserDefaultCard(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDefaultCard", reflect.TypeOf((*MockIWalletServiceV4)(nil).GetUserDefaultCard), uid)
}

// GetWithdrawableCashDetails mocks base method.
func (m *MockIWalletServiceV4) GetWithdrawableCashDetails(uid uint64) (*GetAvailableWithdrawCashDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWithdrawableCashDetails", uid)
	ret0, _ := ret[0].(*GetAvailableWithdrawCashDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWithdrawableCashDetails indicates an expected call of GetWithdrawableCashDetails.
func (mr *MockIWalletServiceV4MockRecorder) GetWithdrawableCashDetails(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithdrawableCashDetails", reflect.TypeOf((*MockIWalletServiceV4)(nil).GetWithdrawableCashDetails), uid)
}

// ListUserCards mocks base method.
func (m *MockIWalletServiceV4) ListUserCards(uid uint64) ([]Card, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserCards", uid)
	ret0, _ := ret[0].([]Card)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserCards indicates an expected call of ListUserCards.
func (mr *MockIWalletServiceV4MockRecorder) ListUserCards(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserCards", reflect.TypeOf((*MockIWalletServiceV4)(nil).ListUserCards), uid)
}

// ListUserCurrencyTypes mocks base method.
func (m *MockIWalletServiceV4) ListUserCurrencyTypes(uid uint64) ([]base.CurrencyType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUserCurrencyTypes", uid)
	ret0, _ := ret[0].([]base.CurrencyType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUserCurrencyTypes indicates an expected call of ListUserCurrencyTypes.
func (mr *MockIWalletServiceV4MockRecorder) ListUserCurrencyTypes(uid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUserCurrencyTypes", reflect.TypeOf((*MockIWalletServiceV4)(nil).ListUserCurrencyTypes), uid)
}

// ListWithdrawableAssets mocks base method.
func (m *MockIWalletServiceV4) ListWithdrawableAssets(req *WithdrawableAssetRequest) ([]*WithdrawableAsset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWithdrawableAssets", req)
	ret0, _ := ret[0].([]*WithdrawableAsset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWithdrawableAssets indicates an expected call of ListWithdrawableAssets.
func (mr *MockIWalletServiceV4MockRecorder) ListWithdrawableAssets(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWithdrawableAssets", reflect.TypeOf((*MockIWalletServiceV4)(nil).ListWithdrawableAssets), req)
}

// QueryOriginalAssetsByDeductPaymentIDs mocks base method.
func (m *MockIWalletServiceV4) QueryOriginalAssetsByDeductPaymentIDs(deductBillPaymentIDs, deductOrderPaymentIDs map[time.Time][]string, orderRefundedPaymentIDs []string) ([]OriginalAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryOriginalAssetsByDeductPaymentIDs", deductBillPaymentIDs, deductOrderPaymentIDs, orderRefundedPaymentIDs)
	ret0, _ := ret[0].([]OriginalAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryOriginalAssetsByDeductPaymentIDs indicates an expected call of QueryOriginalAssetsByDeductPaymentIDs.
func (mr *MockIWalletServiceV4MockRecorder) QueryOriginalAssetsByDeductPaymentIDs(deductBillPaymentIDs, deductOrderPaymentIDs, orderRefundedPaymentIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryOriginalAssetsByDeductPaymentIDs", reflect.TypeOf((*MockIWalletServiceV4)(nil).QueryOriginalAssetsByDeductPaymentIDs), deductBillPaymentIDs, deductOrderPaymentIDs, orderRefundedPaymentIDs)
}

// RechargeAndDeduct mocks base method.
func (m *MockIWalletServiceV4) RechargeAndDeduct(req RechargeRequest) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RechargeAndDeduct", req)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RechargeAndDeduct indicates an expected call of RechargeAndDeduct.
func (mr *MockIWalletServiceV4MockRecorder) RechargeAndDeduct(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RechargeAndDeduct", reflect.TypeOf((*MockIWalletServiceV4)(nil).RechargeAndDeduct), req)
}

// ReverseForAbandoned mocks base method.
func (m *MockIWalletServiceV4) ReverseForAbandoned(req *ReverseForAbandonedReq) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReverseForAbandoned", req)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReverseForAbandoned indicates an expected call of ReverseForAbandoned.
func (mr *MockIWalletServiceV4MockRecorder) ReverseForAbandoned(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReverseForAbandoned", reflect.TypeOf((*MockIWalletServiceV4)(nil).ReverseForAbandoned), req)
}

// SPDBCanUseBalanceQuery mocks base method.
func (m *MockIWalletServiceV4) SPDBCanUseBalanceQuery(accCode string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SPDBCanUseBalanceQuery", accCode)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SPDBCanUseBalanceQuery indicates an expected call of SPDBCanUseBalanceQuery.
func (mr *MockIWalletServiceV4MockRecorder) SPDBCanUseBalanceQuery(accCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SPDBCanUseBalanceQuery", reflect.TypeOf((*MockIWalletServiceV4)(nil).SPDBCanUseBalanceQuery), accCode)
}

// SPDBTransfer mocks base method.
func (m *MockIWalletServiceV4) SPDBTransfer(req *SPDBTransfer) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SPDBTransfer", req)
	ret0, _ := ret[0].(error)
	return ret0
}

// SPDBTransfer indicates an expected call of SPDBTransfer.
func (mr *MockIWalletServiceV4MockRecorder) SPDBTransfer(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SPDBTransfer", reflect.TypeOf((*MockIWalletServiceV4)(nil).SPDBTransfer), req)
}

// SPDBTransferResultQuery mocks base method.
func (m *MockIWalletServiceV4) SPDBTransferResultQuery(req *SPDBTransferResultQuery) (*SPDBTransferResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SPDBTransferResultQuery", req)
	ret0, _ := ret[0].(*SPDBTransferResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SPDBTransferResultQuery indicates an expected call of SPDBTransferResultQuery.
func (mr *MockIWalletServiceV4MockRecorder) SPDBTransferResultQuery(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SPDBTransferResultQuery", reflect.TypeOf((*MockIWalletServiceV4)(nil).SPDBTransferResultQuery), req)
}

// SetUserPaymentSeq mocks base method.
func (m *MockIWalletServiceV4) SetUserPaymentSeq(uid uint32, paymentSeq enums.PaymentSeqType, operator string, shouldImmediatelyPay, isSameAsParent bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserPaymentSeq", uid, paymentSeq, operator, shouldImmediatelyPay, isSameAsParent)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserPaymentSeq indicates an expected call of SetUserPaymentSeq.
func (mr *MockIWalletServiceV4MockRecorder) SetUserPaymentSeq(uid, paymentSeq, operator, shouldImmediatelyPay, isSameAsParent interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserPaymentSeq", reflect.TypeOf((*MockIWalletServiceV4)(nil).SetUserPaymentSeq), uid, paymentSeq, operator, shouldImmediatelyPay, isSameAsParent)
}

// UnbindUserCard mocks base method.
func (m *MockIWalletServiceV4) UnbindUserCard(uid uint64, card Card) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindUserCard", uid, card)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnbindUserCard indicates an expected call of UnbindUserCard.
func (mr *MockIWalletServiceV4MockRecorder) UnbindUserCard(uid, card interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindUserCard", reflect.TypeOf((*MockIWalletServiceV4)(nil).UnbindUserCard), uid, card)
}

// UpdateUserDefaultCard mocks base method.
func (m *MockIWalletServiceV4) UpdateUserDefaultCard(uid uint64, card Card) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserDefaultCard", uid, card)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserDefaultCard indicates an expected call of UpdateUserDefaultCard.
func (mr *MockIWalletServiceV4MockRecorder) UpdateUserDefaultCard(uid, card interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserDefaultCard", reflect.TypeOf((*MockIWalletServiceV4)(nil).UpdateUserDefaultCard), uid, card)
}

// Withdraw mocks base method.
func (m *MockIWalletServiceV4) Withdraw(req *WithdrawRequest) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Withdraw", req)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Withdraw indicates an expected call of Withdraw.
func (mr *MockIWalletServiceV4MockRecorder) Withdraw(req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Withdraw", reflect.TypeOf((*MockIWalletServiceV4)(nil).Withdraw), req)
}
