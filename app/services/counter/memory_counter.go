package counter

import (
	"context"
	"time"
)

type memoryCounter struct {
	limit           int
	penaltyDuration time.Duration

	value     int
	timestamp time.Time
}

func NewMemoryCounter(key string, limit int, penaltyDuration time.Duration) Counter {
	return &memoryCounter{
		limit:           limit,
		penaltyDuration: penaltyDuration,
	}
}

func (c *memoryCounter) Increase(ctx context.Context, add int) (err error) {
	if time.Now().Sub(c.timestamp) > c.penaltyDuration {
		c.Reset(ctx)
		c.timestamp = time.Now()
	}

	if c.value+add > c.limit {
		return LimitReachedError
	} else {
		c.value += add
	}

	return nil
}

func (c *memoryCounter) IsLimitReached(ctx context.Context) bool {
	return c.value >= c.limit
}

func (c *memoryCounter) Get(ctx context.Context) int {
	return c.value
}

func (c *memoryCounter) Reset(ctx context.Context) {
	c.value = 0
	c.timestamp = time.Time{}
}

type MemoryCounterService struct {
	counters map[string]Counter
}

func NewMemoryCounterService() CounterService {
	return &MemoryCounterService{
		counters: make(map[string]Counter),
	}
}

func (s *MemoryCounterService) Create(key string, limit int, penaltyDuration time.Duration) Counter {
	if counter, ok := s.counters[key]; ok {
		return counter
	} else {
		counter := NewMemoryCounter(key, limit, penaltyDuration)
		s.counters[key] = counter
		return counter
	}
}
