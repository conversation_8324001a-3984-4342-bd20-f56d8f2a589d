package workwx

import (
	workwxAPI "github.com/xen0n/go-workwx"
)

// WorkwxService 企业微信接口
type WorkwxService interface {
	// SendTextToChat 向应用群聊发送消息
	SendTextToChat(chatID string, text string, isSafe bool) error
	// SendTextToUsers 向用户发送消息
	SendTextToUsers(userIDs []string, text string, isSafe bool) error
	// SendMarkdownToUsers 向用户发送 Markdown 消息
	SendMarkdownToUsers(userIDs []string, text string, isSafe bool) error
	// SendMarkdownToChat 向应用群聊发送 Markdown 消息
	SendMarkdownToChat(chatID string, text string, isSafe bool) error
	// SpawnTokenRefresher 启动 token 刷新 goroutine
	SpawnTokenRefresher()
}

type workwxService struct {
	app *workwxAPI.WorkwxApp
}

func NewWorkwxService(
	corpID string,
	corpSecret string,
	agentID int64,
) WorkwxService {
	client := workwxAPI.New(corpID)
	app := client.WithApp(corpSecret, agentID)
	return &workwxService{app: app}
}

//
// impl WorkwxService for workwxService
//

// SendTextToChat 向应用群聊发送消息
func (s *workwxService) SendTextToChat(
	chatID string,
	text string,
	isSafe bool,
) error {
	recipient := workwxAPI.Recipient{
		ChatID: chatID,
	}
	return s.app.SendTextMessage(&recipient, text, isSafe)
}

// SendTextToUsers 向应用群聊发送消息
func (s *workwxService) SendTextToUsers(
	userIDs []string,
	text string,
	isSafe bool,
) error {
	recipient := workwxAPI.Recipient{
		UserIDs: userIDs,
	}
	return s.app.SendTextMessage(&recipient, text, isSafe)
}

// SendMarkdownToUsers 向 users 发送 Markdown 消息
func (s *workwxService) SendMarkdownToUsers(
	userIDds []string,
	text string,
	isSafe bool,
) error {
	recipient := workwxAPI.Recipient{
		UserIDs: userIDds,
	}
	return s.app.SendMarkdownMessage(&recipient, text, isSafe)
}

// SendMarkdownToChat 向应用群聊发送 Markdown 消息
func (s *workwxService) SendMarkdownToChat(
	chatID string,
	text string,
	isSafe bool,
) error {
	recipient := workwxAPI.Recipient{
		ChatID: chatID,
	}
	return s.app.SendMarkdownMessage(&recipient, text, isSafe)
}

// SpawnTokenRefresher 启动 token 刷新 goroutine
func (s *workwxService) SpawnTokenRefresher() {
	s.app.SpawnAccessTokenRefresher()
}
