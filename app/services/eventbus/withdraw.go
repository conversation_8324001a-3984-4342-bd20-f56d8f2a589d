package eventbus

import (
	"fmt"
	"time"

	"qiniu.io/gaea/app/env/global"

	"github.com/qbox/bo-base/v4/eventbus"
	"github.com/qbox/bo-base/v4/retry"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"qbox.us/api/pay/wallet.v3"
	"qbox.us/biz/utils.v2/log"

	"qiniu.io/gaea/app/controllers/withdraw"
	"qiniu.io/gaea/app/models"
	walletv4 "qiniu.io/gaea/app/services/wallet.v4"
	"qiniu.io/gaea/app/services/workwx"
)

// WithdrawHandler 提现 handler（只是钱包提现，不负责实际财务打款）
type WithdrawHandler struct {
	logger         log.Logger
	mgr            *models.ModelMgr
	walletV4Client walletv4.IWalletServiceV4
	eventBus       eventbus.EventBus
	workwxSrv      workwx.WorkwxService
}

func NewWithdrawHandler(
	logger log.Logger,
	mgr *models.ModelMgr,
	walletV4Client walletv4.IWalletServiceV4,
	eventBus eventbus.EventBus,
	workwxSrv workwx.WorkwxService,
) *WithdrawHandler {
	return &WithdrawHandler{
		walletV4Client: walletV4Client,
		mgr:            mgr,
		logger:         logger,
		eventBus:       eventBus,
		workwxSrv:      workwxSrv,
	}
}

func (c *WithdrawHandler) HandleWithAck(message []byte) (succeed bool, failedNackInterval time.Duration) {
	c.logger.Infof("<WithdrawHandler.HandleWithAck> start. message=%s", string(message))
	msg := &withdraw.DeductMsg{}
	msg = msg.Decode(message)

	objectId, err := primitive.ObjectIDFromHex(msg.ID)
	if err != nil {
		c.logger.Errorf("<WithdrawPayHandler.HandleWithAck> primitive.ObjectIDFromHex failed. id=%s, err=%s", msg.ID, err)
		return true, 0
	}
	idQuery := map[string]interface{}{
		"_id": objectId,
	}
	awtms, err := c.mgr.ApplyWithdrawTransactionMap.FindByCond(idQuery)
	if len(awtms) != 1 {
		c.logger.Errorf("<WithdrawHandler.HandleWithAck> FindByID failed. id: %s err: %s", msg.ID, err)
		return false, 5 * time.Minute
	}
	a := awtms[0]

	query := map[string]interface{}{
		"_id": objectId,
	}
	set := map[string]interface{}{}

	if a.Status.HasNotBeenDeducted() {
		var errMsg string
		req := &walletv4.WithdrawRequest{
			UID:                         msg.UID,
			Desc:                        msg.WithdrawType.Humanize(),
			Excode:                      msg.ID,
			Details:                     "",
			BusinessAt:                  time.Now(),
			DesignatedWithdrawPaymentId: msg.DepositPaymentID,
			AssetType:                   int32(msg.AssetType),
		}

		retryer := retry.New(
			retry.WithMaxRetry(3),
			retry.WithRandomWaitTime(time.Minute, 5*time.Minute),
		)

		status := models.AWTMStatusDeducted
		if msg.Money > 0 {
			req.Money = msg.Money
			req.Type = wallet.TxnTypeRefund
			req.EntryDesc = fmt.Sprintf("%s,relatedPaymentID:%s", msg.WithdrawType.Humanize(), msg.DepositPaymentID)
			var withdrawPaymentID string
			err = retryer.Do(func() error {
				var err1 error
				withdrawPaymentID, err1 = c.walletV4Client.Withdraw(req)
				return err1
			})
			if err != nil {
				errMsg = err.Error()
				c.logger.Errorf("<WithdrawHandler.HandleWithAck> Withdraw failed. req: %+v err: %s", req, err)
				status = models.AWTMStatusDeductionException
			} else {
				set["withdraw_payment_id"] = withdrawPaymentID
			}
		}
		if msg.NBOffsettingCash > 0 {
			req.Money = msg.NBOffsettingCash
			req.Type = wallet.TxnTypePresentWithdraw
			req.EntryDesc = fmt.Sprintf("withdraw offsetting nb,relatedPaymentID:%s", msg.DepositPaymentID)

			var offsettingPaymentID string
			err = retryer.Do(func() error {
				var err1 error
				offsettingPaymentID, err1 = c.walletV4Client.Withdraw(req)
				return err1
			})
			if err != nil {
				c.logger.Errorf("<WithdrawHandler.HandleWithAck> Withdraw failed. req: %+v err: %s", req, err)
				errMsg = err.Error()
				status = models.AWTMStatusDeductionException
			} else {
				set["offsetting_payment_id"] = offsettingPaymentID
			}
		}
		set["status"] = status
		set["updated_at"] = time.Now()
		err = c.mgr.ApplyWithdrawTransactionMap.BulkUpdateByMap(query, set)
		if err != nil {
			c.logger.Errorf("<WithdrawHandler.HandleWithAck> update status failed. id: %s err: %s", msg.ID, err)
			return false, time.Hour / 2
		}
		if status == models.AWTMStatusDeductionException {
			if err1 := c.workwxSrv.SendTextToChat(
				global.Env.Workwx.ChatIDWithdraw,
				fmt.Sprintf(
					`%s: 用户 %d 提现扣费异常，请及时处理。id=%s, err=%s`,
					time.Now().Format(time.DateTime),
					msg.UID,
					msg.ID,
					errMsg,
				),
				false,
			); err1 != nil {
				c.logger.Warnf(
					"<WithdrawHandler.HandleWithAck> workwx message sending failed: %+v",
					err1,
				)
			}
			return true, time.Duration(0)
		}
		a.Status = models.AWTMStatusDeducted
	}

	// 原路提现的打款会自动触发支付，线下提现的打款不能自动触发支付，需要人工修改
	if msg.WithdrawType == models.WithdrawTypeOriginal && a.Status.HasNotBeenPaid() && msg.Money > 0 {
		err = c.eventBus.Publish(eventbus.GaeaWithdrawPayTopic, message)
		if err != nil {
			c.logger.Errorf("<WithdrawHandler.HandleWithAck> seed pay msg failed. msg: %s err: %s", string(message), err)
			return false, time.Minute * 5
		}
	}
	return true, time.Duration(0)
}
