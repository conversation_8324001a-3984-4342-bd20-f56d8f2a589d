package verification

import (
	"context"
	"fmt"
	"io"
	"log"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/teapots/teapot"

	"qbox.us/biz/services.v2/account"

	"qiniu.io/gaea/app/services/cache"
	"qiniu.io/gaea/app/services/counter"
)

func newService(config *VerificationConfig) VerificationService {
	userInfo := &account.UserInfo{}
	logger := teapot.NewReqLogger(log.New(io.Discard, "", log.LstdFlags|log.Lmicroseconds), "", "")
	client := getRedisClient()
	key := fmt.Sprintf("gaea:admin:test:%s", time.Now().Format(time.RFC3339))
	store := cache.NewRedisCache(client, key)
	counterService := counter.NewMemoryCounterService()

	return NewVerificationService(config, userInfo, store, counterService, ********, logger, nil)
}

func TestGenerateAndVerify(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	// incorrect code
	incorrectCode := code + "233"
	ok, err := service.VerifyCode(ctx, incorrectCode)
	assert.False(t, ok)
	assert.Equal(t, CodeIncorrectErr, err)

	// correct code
	ok, err = service.VerifyCode(ctx, code)
	assert.True(t, ok)
	assert.NoError(t, err)

	// now code is consumed and cannot be used again
	ok, err = service.VerifyCode(ctx, code)
	assert.False(t, ok)
	assert.Equal(t, CodeExpiredErr, err)
}

func TestMaxAttemptsLimit(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     1,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	// incorrect code
	incorrectCode := code + "233"
	ok, err := service.VerifyCode(ctx, incorrectCode)
	assert.False(t, ok)
	assert.Equal(t, CodeIncorrectErr, err)

	// exceeded max attempts limit
	ok, err = service.VerifyCode(ctx, code)
	assert.False(t, ok)
	assert.Equal(t, MaxAttemptsExceedErr, err)
}

func TestAttemptsLimitResetAfterSuccess(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     1,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	// verify once
	ok, err := service.VerifyCode(ctx, code)
	if assert.NoError(t, err) {
		assert.True(t, ok)
	}

	// now attempt counter should be reset
	// future verification won't exceed limit

	code, err = service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	// verify again
	ok, err = service.VerifyCode(ctx, code)
	if assert.NoError(t, err) {
		assert.True(t, ok)
	}
}

func TestAttemptsLimitResetAfterPenaltyTime(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     1,
		AttemptPenaltyTime:  1,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}
	wrongCode := code + "0"

	// verify once with wrong code
	ok, err := service.VerifyCode(ctx, wrongCode)
	assert.False(t, ok)
	assert.Equal(t, CodeIncorrectErr, err)

	// verify again with wrong code
	// should exceed max attempts
	ok, err = service.VerifyCode(ctx, wrongCode)
	assert.False(t, ok)
	assert.Equal(t, MaxAttemptsExceedErr, err)

	// wait for penalty time
	time.Sleep(1 * time.Second)

	// verify again, should success since penalty time has passed
	ok, err = service.VerifyCode(ctx, code)
	if assert.NoError(t, err) {
		assert.True(t, ok)
	}
}

func TestMaxGenerationLimit(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 1,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	// exceeded max generation count
	_, err = service.GenerateCode(ctx)
	assert.Equal(t, MaxGenerationExceedErr, err)
}

func TestMaxGenerationLimitResetAfterPenaltyTime(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    1,
		MaxCountPerInterval: 1,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	// exceeded max generation count
	_, err = service.GenerateCode(ctx)
	assert.Equal(t, MaxGenerationExceedErr, err)

	// wait for penal time
	time.Sleep(1 * time.Second)

	// generate code again, counter should reset
	code, err = service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}
}

func TestExpiry(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              1,
		MaxCountInterval:    60,
		MaxCountPerInterval: 1,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	// wait for expiry
	time.Sleep(2 * time.Second)

	// code already expired
	ok, err := service.VerifyCode(ctx, code)
	assert.False(t, ok)
	assert.Equal(t, CodeExpiredErr, err)
}

func TestGenerateNotSameEachTime(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	// generate code
	code, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	code2, err := service.GenerateCode(ctx)
	if assert.NoError(t, err) {
		assert.True(t, code2 != code)
	}
}

func TestContextMatching(t *testing.T) {
	ctx := context.Background()
	config := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	service := newService(config)

	ctxVar := "ctx"

	// generate code
	code, err := service.GenerateCode(ctx, ctxVar)
	if assert.NoError(t, err) {
		assert.Equal(t, config.CodeLength, len(code))
	}

	wrongCtxVar := "ctx2"

	// wrong context var should fail
	ok, err := service.VerifyCode(ctx, code, wrongCtxVar)
	assert.False(t, ok)
	assert.Equal(t, ContextNotMatchErr, err)

	// same context var should pass
	ok, err = service.VerifyCode(ctx, code, ctxVar)
	if assert.NoError(t, err) {
		assert.True(t, ok)
	}
}

func TestSessionKeyLastVerifyTime(t *testing.T) {
	config1 := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	str := config1.SessionKeyLastVerifyTime("17749746109")
	assert.Equal(t, str, "test_last_verify_time17749746109")

	config2 := &VerificationConfig{
		Name:                "test",
		CodeChars:           []byte("**********"),
		CodeLength:          6,
		Expiry:              60,
		MaxCountInterval:    60,
		MaxCountPerInterval: 5,
		MaxAttemptCount:     5,
		AttemptPenaltyTime:  60,
	}
	str2 := config2.SessionKeyLastVerifyTime("17749746109", "17749746109")
	assert.Equal(t, str2, "test_last_verify_time17749746109_17749746109")
}

func getRedisClient() redis.UniversalClient {
	ctx := context.Background()
	// try new a client that uses redis sentinel fort automatic failover
	client := redis.NewFailoverClient(&redis.FailoverOptions{
		MasterName:    "master",
		SentinelAddrs: []string{":26379"},
	})
	if err := client.Ping(ctx).Err(); err == nil {
		return client
	} else {
		log.Printf("getRedisClient() NewFailoverClient failed: %s\n", err)
	}

	return redis.NewClient(&redis.Options{
		Addr: "127.0.0.1:6379",
	})
}
