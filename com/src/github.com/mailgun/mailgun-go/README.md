Mailgun with Go
===============

[![Build Status](https://travis-ci.org/mbanzon/mailgun.png?branch=master)](https://travis-ci.org/mbanzon/mailgun)
[![GoDoc](https://godoc.org/github.com/mailgun/mailgun-go?status.svg)](https://godoc.org/github.com/mailgun/mailgun-go)

Go library for sending mail with the Mailgun API.


See these examples on how how to use use the library with various parts of the Mailgun API:

* [Messages](https://gist.github.com/mbanzon/8179682 "mailgun-message-example.go")
* [E-mail validation](https://gist.github.com/mbanzon/8179989 "mailgun-validation-example.go")
* [Bounces](https://gist.github.com/mbanzon/8179951 "mailgun-bounces-example.go")
* [Stats](https://gist.github.com/mbanzon/8206266 "mailgun-stats-example.go")
* [File Attachment from Memory](https://gist.github.com/sym3tri/8a29ddecd65ec4f8ccfc)

More examples are coming soon.

The code is released under a 3-clause BSD license. See the LICENSE file for more information.
