package testutil

import (
	"flag"
	"github.com/crowdmob/goamz/aws"
	"gopkg.in/check.v1"
)

// Amazon must be used by all tested packages to determine whether to
// run functional tests against the real AWS servers.
var Amazon bool

func init() {
	flag.BoolVar(&Amazon, "amazon", false, "Enable tests against amazon server")
}

type LiveSuite struct {
	auth aws.Auth
}

func (s *LiveSuite) SetUpSuite(c *check.C) {
	if !Amazon {
		c.Skip("amazon tests not enabled (-amazon flag)")
	}
	auth, err := aws.EnvAuth()
	if err != nil {
		c.<PERSON>(err.<PERSON>rror())
	}
	s.auth = auth
}
