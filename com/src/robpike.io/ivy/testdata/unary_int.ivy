# Copyright 2014 The Go Authors. All rights reserved.
# Use of this source code is governed by a BSD-style
# license that can be found in the LICENSE file.

# Unary operations on small ints.

)seed 0
?10
	6

23
	23

+ 23
	23

+-23
	-23

- 23
	-23

--23
	23

- -23
	23

/23
	1/23

/-23
	-1/23

sgn -1
	-1

sgn 0
	0

sgn 75
	1

^1
	-2

^0x33
	-52

~0
	1

~1
	0

~3
	0

abs -10
	10

abs 0
	0

abs 10
	10

floor -1
	-1

floor 0
	0

floor 1
	1

ceil -1
	-1

ceil 0
	0

ceil 1
	1

iota 0
	

iota 1
	1

iota 10
	1 2 3 4 5 6 7 8 9 10

rho 0
	

rho 10
	

,3
	3

rev 3
	3

flip 3
	3
