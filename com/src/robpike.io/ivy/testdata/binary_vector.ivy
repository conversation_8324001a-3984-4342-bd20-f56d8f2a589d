# Copyright 2014 The Go Authors. All rights reserved.
# Use of this source code is governed by a BSD-style
# license that can be found in the LICENSE file.

# Binary operations with vectors on the left.

23 45 67 + 5
	28 50 72

23 45 67 + 1/3
	70/3 136/3 202/3

23 45 67 + iota 3
	24 47 70

23 45 67 + 12 33 56
	35 78 123

23 45 67 + 2 3 rho 12 33
	 35  78  79
	 56  57 100

23 45 67-5
	18 40 62

23 45 67 - 1e10
	-********** -********** -**********

23 45 67 - 1/3
	68/3 134/3 200/3

23 45 67 - iota 3
	22 43 64

23 45 67-2 3 rho iota 10
	22 43 64
	19 40 61

23 45 67*5
	115 225 335

23 45 67*1e10
	230000000000 450000000000 670000000000

23 45 67 * 1/3
	23/3 15 67/3

23 45 67 * iota 3
	23 90 201

23 45 67 * 2 3 rho iota 10
	 23  90 201
	 92 225 402

23 45 67 / 5
	23/5 9 67/5

23 45 67 / 1e10
	23/10000000000 9/********** 67/10000000000

23 45 67 / 1/3
	69 135 201

23 45 67 / iota 3
	23 45/2 67/3

23 45 67 / 2 3 rho iota 10
	  23 45/2 67/3
	23/4    9 67/6

23 45 67 div 4
	5 11 16

-23 45 67 div 1e10
	-1 0 0

23 45 67 idiv 4
	5 11 16

-23 45 67 idiv 4
	-5 11 16

23 45 67 mod 3
	2 0 1

-23 45 67 mod 3
	1 0 1

-23 45 67 imod 3
	-2 0 1

23 45 67 idiv 3
	7 15 22

2e11 idiv 1e10
	20

23 45 67 idiv iota 3
	23 22 22

23 45 67 idiv 2 3 rho iota 10
	23 22 22
	 5  9 11

23 45 67 imod 3
	2 0 1

2e11 imod 1e10+3
	9999999943

23 45 67 imod iota 3
	0 1 1

23 45 67 imod 2 3 rho iota 10
	0 1 1
	3 0 1

23 45 67 ** 5
	6436343 184528125 1350125107

23 45 67 ** iota 3
	23 2025 300763

23 45 67 ** 2 3 rho iota 10
	         23        2025      300763
	     279841   184528125 90458382169

23 45 67 & 7
	7 5 3

23 45 67 & 2+1e10
	2 0 2

23 45 67 & iota 3
	1 0 3

23 45 67 &  2 3 rho iota 10
	1 0 3
	4 5 2

23 45 67 | 7
	23 47 71

23 45 67 | 2+1e10
	10000000023 10000000047 10000000067

23 45 67 | iota 3
	23 47 67

22 45 67 |  2 3 rho iota 10
	23 47 67
	22 45 71

23 45 67 ^ 7
	16 42 68

23 45 67 ^ 2e0
	21 47 65

23 45 67 ^ iota 3
	22 47 64

23 45 67 ^  2 3 rho iota 10
	22 47 64
	19 40 69

23 45 67 << 5
	736 1440 2144

23 45 67 << iota 3
	46 180 536

23 45 67 <<  2 3 rho iota 10
	  46  180  536
	 368 1440 4288

23 45 67 >> 5
	0 1 2

23 45 67 >> iota 3
	11 11 8

23 45 67 >>  2 3 rho iota 10
	11 11  8
	 1  1  1

23 45 5 == 5
	0 0 1

23 1e10 67 == 1e10
	0 1 0

23 1/3 67 == 1/3
	0 1 0

23 45 67 == -1 + 23 45 67 + iota 3
	1 0 0

23 45 67 ==  2 3 rho 22 + iota 10
	1 0 0
	0 0 0

23 5 67 != 5
	1 0 1

23 1e10 67 != 1e10
	1 0 1

23 1/3 67 != 1/3
	1 0 1

23 45 67 != -1 + 23 45 67 + iota 3
	0 1 1

23 45 67 !=  2 3 rho 22 + iota 10
	0 1 1
	1 1 1

23 45 3 < 5
	0 0 1

23 3e10 67 < 2e10
	1 0 1

23 1/4 67 < 1/3
	0 1 0

23 45 67 < -1 + 23 45 67 + iota 3
	0 1 1

23 45 67 <  2 3 rho 22 + iota 6
	0 0 0
	1 0 0

23 5 67 <= 5
	0 1 0

23 4e10 67 <= 3e10
	1 0 1

23 45 67 <= 22 45 67 + 1/3
	0 1 1

23 45 67 <=  2 3 rho 22 + iota 10
	1 0 0
	1 0 0

23 5 67 > 5
	1 0 1

23 4e10 67 > 3e10
	0 1 0

23 -45 67 > 1/3
	1 0 1

23 45 67 > -2 + 23 45 67 + iota 3
	1 0 0

23 45 67 >  2 3 rho 21 + iota 10
	1 1 1
	0 1 1

23 4 67 >= 5
	1 0 1

23 3e10 67 >= 3e10
	0 1 0

23 1/4 67 >= 1/3
	1 0 1

23 45 67 >= -2 + 23 45 67 + iota 3
	1 1 0

23 45 67 >=  2 3 rho 22 + iota 10
	1 1 1
	0 1 1

23 0 67 and 5
	1 0 1

23 0 67 and 1e10
	1 0 1

23 0 67 and 1/3
	1 0 1

23 0 67 and  iota 3
	1 0 1

23 0 67 and 2 3 rho -1 + iota 10
	0 0 1
	1 0 1

23 0 67 or 5
	1 1 1

23 0 67 or 0
	1 0 1

23 45 67 or 1e10
	1 1 1

23 45 67 or 1/3
	1 1 1

23 45 67 or -2 + -2 + 23 45 67 + iota 3
	1 1 1

0 or -2 + iota 3
	1 0 1

23 45 67 or -2 + 2 3 rho 21 + iota 10
	1 1 1
	1 1 1

23 0 67 xor 3
	0 1 0

23 0 67 xor 0
	1 0 1

23 0 67 xor 1e10
	0 1 0

23 0 67 xor 1/3
	0 1 0

23 0 67 xor -1 + iota 3
	1 1 0

1 xor -2+ 2 3 rho iota 10
	0 1 0
	0 0 0

0 1 2 nand 0
	1 1 1

0 1 2 nand 1
	1 0 0

0 1 2 nand 1
	1 0 0

23 0 67 nand 1e10
	0 1 0

23 0 67 nand 1/3
	0 1 0

23 0 67 nand -2 + iota 3
	0 1 0

23 0 67  nand -2 + 2 3 rho iota 10
	0 1 0
	0 1 0

23 0 67  nor 0
	0 1 0

23 0 67  nor 1
	0 0 0

23 0 67 nor 0
	0 1 0

23 0 67  nor 1
	0 0 0

23 0 67 nor 1e10
	0 0 0

23 0 67  nor 1/3
	0 0 0

23 0 67  nor -2 + iota 3
	0 1 0

23 0 67  nor -2 + 2 3 rho iota 10
	0 1 0
	0 0 0

23 45 67 iota 23 45 67 3e10 4e10
	1 2 3 0 0 

23 3 67 min 5
	5 3 5

23 2e10 67 min 1e10
	23 10000000000 67

23 1/4 67 min 1/3
	1/3 1/4 1/3

23 45 67 min iota 3
	1 2 3

23 45 67 min  2 3 rho 20 + iota 10
	21 22 23
	23 25 26

23 2 67 max 5
	23 5 67

23 4e10 67 max 3e10
	30000000000 40000000000 30000000000

23 1/4 67 max 1/3
	23 1/3 67

23 45 67 max -2 + 23 45 67 + iota 3
	23 45 68

23 45 67 max 2 3 rho 21 + iota 10
	23 45 67
	25 45 67

23 45 67 , 5
	23 45 67 5

23 45 67 , 1e10
	23 45 67 10000000000

23 45 67 , 1/3
	23 45 67 1/3

23 45 67 , iota 3
	23 45 67 1 2 3

23 45 67 iota 1e10 23 45 67 3e10
	0 1 2 3 0

11 22 33[2]
	22

11 22 33[3 2]
	33 22

4 3 2 1[up 4 3 2 1]
	1 2 3 4

1 2 3 4[down 1 2 3 4]
	4 3 2 1
