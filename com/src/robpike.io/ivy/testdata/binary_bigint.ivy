# Copyright 2014 The Go Authors. All rights reserved.
# Use of this source code is governed by a BSD-style
# license that can be found in the LICENSE file.

# Binary operations with big ints on the left.

2e10 + 5
	20000000005

2e10 + 1/3
	60000000001/3

2e10 + iota 3
	20000000001 20000000002 20000000003

2e10+2 3 rho iota 10
	20000000001 20000000002 20000000003
	20000000004 20000000005 20000000006

2e10-5
	19999999995

2e10 - 1e10
	10000000000

2e10 - 1/3
	59999999999/3

2e10 - iota 3
	19999999999 19999999998 19999999997

2e10-2 3 rho iota 10
	19999999999 19999999998 19999999997
	19999999996 19999999995 19999999994

2e10*5
	100000000000

2e10*1e10
	200000000000000000000

2e10 * 1/3
	20000000000/3

2e10 * iota 3
	20000000000 **********0 60000000000

2e10 * 2 3 rho iota 10
	 20000000000  **********0  60000000000
	 80000000000 100000000000 120000000000

2e10 / 5
	**********

2e10 / 1e10
	2

2e10 / 1/3
	60000000000

2e10 / iota 3
	20000000000 10000000000 20000000000/3

2e10 / 2 3 rho iota 10
	  20000000000   10000000000 20000000000/3
	   **********    ********** 10000000000/3

2e10 div 4
	**********

-2e10 div 1e10
	-2

2e10 idiv 4
	**********

-2e10 idiv 4
	-**********

2e10 mod 3
	2

-2e10 mod 3
	1

-2e10 imod 3
	-2

2e10 idiv 3
	6666666666

2e11 idiv 1e10
	20

2e10 idiv iota 3
	20000000000 10000000000 6666666666

2e10 idiv 2 3 rho iota 10
	20000000000 10000000000  6666666666
	 **********  **********  3333333333

2e10 imod 3
	2

2e11 imod 1e10+3
	9999999943

2e10 imod iota 3
	0 0 2

2e10 imod 2 3 rho iota 10
	0 0 2
	0 0 2

2e10 ** 5
	3200000000000000000000000000000000000000000000000000

2e10 ** -2
	1/**********00000000000

2e10 ** iota 3
	20000000000 **********00000000000 8000000000000000000000000000000

2e10 ** 2 3 rho iota 10
	                                                   20000000000                                          **********00000000000                                8000000000000000000000000000000
	                    160000000000000000000000000000000000000000           3200000000000000000000000000000000000000000000000000 6**********000000000000000000000000000000000000000000000000000

(2e10+1) & 7
	1

(2e10+1) & 2+1e10
	245760

(2e10+1) & iota 3
	1 0 1

(2e10+1) &  2 3 rho iota 10
	1 0 1
	0 1 0

(2e10+1) | 7
	20000000007

(2e10+1) | 2+1e10
	29999754243

(2e10+1) | iota 3
	20000000001 20000000003 20000000003

(2e10+1) |  2 3 rho iota 10
	20000000001 20000000003 20000000003
	20000000005 20000000005 20000000007

(2e10+1) ^ 7
	20000000006

(2e10+1) ^ 2+1e10
	29999508483

(2e10+1) ^ iota 3
	20000000000 20000000003 20000000002

(2e10+1) ^  2 3 rho iota 10
	20000000000 20000000003 20000000002
	20000000005 20000000004 20000000007

(2e10+1) << 5
	640000000032

(2e10+1) << iota 3
	**********2 80000000004 160000000008

(2e10+1) <<  2 3 rho iota 10
	  **********2   80000000004  160000000008
	 320000000016  640000000032 1280000000064

2e10 >> 5
	625000000

2e10 >> iota 3
	10000000000 ********** 2500000000

2e10 >>  2 3 rho iota 10
	10000000000  **********  2500000000
	 1250000000   625000000   312500000

2e10 == 5
	0

2e10 == 1e10
	0

2e10 == 1/3
	0

2e10 == -1 + 2e10 + iota 3
	1 0 0

2e10 ==  2 3 rho -1 + 2e10 + iota 10
	1 0 0
	0 0 0

2e10 != 5
	1

2e10 != 1e10
	1

2e10 != 1/3
	1

2e10 != -1 + 2e10 + iota 3
	0 1 1

2e10 !=  2 3 rho -1 + 2e10 + iota 10
		0 1 1
		1 1 1

2e10 < 5
	0

2e10 < 3e10
	1

2e10 < 1/3
	0

2e10 < -1 + 2e10 + iota 3
	0 1 1

2e10 <  2 3 rho -1 + 2e10 + iota 6
	0 1 1
	1 1 1

2e10 <= 5
	0

2e10 <= 3e10
	1

2e10 <= 2e10 + 1/3
	1

2e10 <=  2 3 rho -1 + 2e10 + iota 10
	1 1 1
	1 1 1

2e10 > 5
	1

2e10 > 3e10
	0

2e10 > 1/3
	1

2e10 > -2 + 2e10 + iota 3
	1 0 0

2e10 >  2 3 rho -2 + 2e10 + iota 10
	1 0 0
	0 0 0

2e10 >= 5
	1

2e10 >= 3e10
	0

2e10 >= 1/3
	1

2e10 >= -2 + 2e10 + iota 3
	1 1 0

2e10 >=  2 3 rho -2 + 2e10 + iota 10
	1 1 0
	0 0 0

2e10 and 5
	1

2e10 and 1e10
	1

2e10 and 1/3
	1

2e10 and  iota 3
	1 1 1

2e10 and 2 3 rho iota 10
	1 1 1
	1 1 1

2e10 or 5
	1

2e10 or 1e10
	1

2e10 or 1/3
	1

2e10 or -2 + -2 + 2e10 + iota 3
	1 1 1

0 or -2 + iota 3
	1 0 1

2e10 or -2 + 2 3 rho -2 + 2e10 + iota 10
	1 1 1
	1 1 1

2e10 or -2 +2 3 rho -2 + 2e10 + iota 10
	1 1 1
	1 1 1

2e10 xor 3
	0

2e10 xor 1e10
	0

1 xor 1/3
	0

1 xor -2 + iota 3
	0 1 0

1 xor -2+ 2 3 rho iota 10
	0 1 0
	0 0 0

0 nand 0
	1

0 nand 1
	1

1 nand 0
	1

1 nand 1
	0

2e10 nand 1e10
	0

1 nand 1/3
	0

1 nand -2 + iota 3
	0 1 0

1 nand -2 + 2 3 rho iota 10
	0 1 0
	0 0 0

0 nor 0
	1

0 nor 1
	0

1 nor 0
	0

1 nor 1
	0

2e10 nor 1e10
	0

1 nor 1/3
	0

0 nor -2 + iota 3
	0 1 0

0 nor -2+ 2 3 rho iota 10
	0 1 0
	0 0 0

2e10 iota 2e10 3e10 4e10
	1 0 0 

2e10 min 5
	5

2e10 min 1e10
	10000000000

2e10 min 1/3
	1/3

2e10 min iota 3
	1 2 3

2e10 min  2 3 rho -2 + 2e10 + iota 10
	19999999999 20000000000 20000000000
	20000000000 20000000000 20000000000

2e10 max 5
	20000000000

2e10 max 3e10
	30000000000

2e10 max 1/3
	20000000000

2e10 max -2 + 2e10 + iota 3
	20000000000 20000000000 20000000001

2e10 max 2 3 rho -2 + 2e10 + iota 10
	20000000000 20000000000 20000000001
	20000000002 20000000003 20000000004

2e10 , 5
	20000000000 5

2e10 , 1e10
	20000000000 10000000000

2e10 , 1/3
	20000000000 1/3

2e10 , iota 3
	20000000000 1 2 3

2e10 iota 1e10 2e10 3e10
	0 1 0
