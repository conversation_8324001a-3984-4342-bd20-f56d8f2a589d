// This file was generated by go generate; DO NOT EDIT

package width

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *widthTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return widthValues[c0], 1
	case c0 < 0xC0:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := widthIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := widthIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = widthIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := widthIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = widthIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = widthIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *widthTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return widthValues[c0]
	}
	i := widthIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = widthIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = widthIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *widthTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return widthValues[c0], 1
	case c0 < 0xC0:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := widthIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := widthIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = widthIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := widthIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = widthIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = widthIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *widthTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return widthValues[c0]
	}
	i := widthIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = widthIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = widthIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// widthTrie. Total size: 10880 bytes (10.62 KiB). Checksum: 7248598b914622aa.
type widthTrie struct{}

func newWidthTrie(i int) *widthTrie {
	return &widthTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *widthTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	default:
		return uint16(widthValues[n<<6+uint32(b)])
	}
}

// widthValues: 75 blocks, 4800 entries, 9600 bytes
// The third block is the zero block.
var widthValues = [4800]uint16{
	// Block 0x0, offset 0x0
	0x20: 0x6001, 0x21: 0x6002, 0x22: 0x6002, 0x23: 0x6002,
	0x24: 0x6002, 0x25: 0x6002, 0x26: 0x6002, 0x27: 0x6002, 0x28: 0x6002, 0x29: 0x6002,
	0x2a: 0x6002, 0x2b: 0x6002, 0x2c: 0x6002, 0x2d: 0x6002, 0x2e: 0x6002, 0x2f: 0x6002,
	0x30: 0x6002, 0x31: 0x6002, 0x32: 0x6002, 0x33: 0x6002, 0x34: 0x6002, 0x35: 0x6002,
	0x36: 0x6002, 0x37: 0x6002, 0x38: 0x6002, 0x39: 0x6002, 0x3a: 0x6002, 0x3b: 0x6002,
	0x3c: 0x6002, 0x3d: 0x6002, 0x3e: 0x6002, 0x3f: 0x6002,
	// Block 0x1, offset 0x40
	0x40: 0x6003, 0x41: 0x6003, 0x42: 0x6003, 0x43: 0x6003, 0x44: 0x6003, 0x45: 0x6003,
	0x46: 0x6003, 0x47: 0x6003, 0x48: 0x6003, 0x49: 0x6003, 0x4a: 0x6003, 0x4b: 0x6003,
	0x4c: 0x6003, 0x4d: 0x6003, 0x4e: 0x6003, 0x4f: 0x6003, 0x50: 0x6003, 0x51: 0x6003,
	0x52: 0x6003, 0x53: 0x6003, 0x54: 0x6003, 0x55: 0x6003, 0x56: 0x6003, 0x57: 0x6003,
	0x58: 0x6003, 0x59: 0x6003, 0x5a: 0x6003, 0x5b: 0x6003, 0x5c: 0x6003, 0x5d: 0x6003,
	0x5e: 0x6003, 0x5f: 0x6003, 0x60: 0x6004, 0x61: 0x6004, 0x62: 0x6004, 0x63: 0x6004,
	0x64: 0x6004, 0x65: 0x6004, 0x66: 0x6004, 0x67: 0x6004, 0x68: 0x6004, 0x69: 0x6004,
	0x6a: 0x6004, 0x6b: 0x6004, 0x6c: 0x6004, 0x6d: 0x6004, 0x6e: 0x6004, 0x6f: 0x6004,
	0x70: 0x6004, 0x71: 0x6004, 0x72: 0x6004, 0x73: 0x6004, 0x74: 0x6004, 0x75: 0x6004,
	0x76: 0x6004, 0x77: 0x6004, 0x78: 0x6004, 0x79: 0x6004, 0x7a: 0x6004, 0x7b: 0x6004,
	0x7c: 0x6004, 0x7d: 0x6004, 0x7e: 0x6004,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xe1: 0x2000, 0xe2: 0x6005, 0xe3: 0x6005,
	0xe4: 0x2000, 0xe5: 0x6006, 0xe6: 0x6005, 0xe7: 0x2000, 0xe8: 0x2000,
	0xea: 0x2000, 0xec: 0x6007, 0xed: 0x2000, 0xee: 0x2000, 0xef: 0x6008,
	0xf0: 0x2000, 0xf1: 0x2000, 0xf2: 0x2000, 0xf3: 0x2000, 0xf4: 0x2000,
	0xf6: 0x2000, 0xf7: 0x2000, 0xf8: 0x2000, 0xf9: 0x2000, 0xfa: 0x2000,
	0xfc: 0x2000, 0xfd: 0x2000, 0xfe: 0x2000, 0xff: 0x2000,
	// Block 0x4, offset 0x100
	0x106: 0x2000,
	0x110: 0x2000,
	0x117: 0x2000,
	0x118: 0x2000,
	0x11e: 0x2000, 0x11f: 0x2000, 0x120: 0x2000, 0x121: 0x2000,
	0x126: 0x2000, 0x128: 0x2000, 0x129: 0x2000,
	0x12a: 0x2000, 0x12c: 0x2000, 0x12d: 0x2000,
	0x130: 0x2000, 0x132: 0x2000, 0x133: 0x2000,
	0x137: 0x2000, 0x138: 0x2000, 0x139: 0x2000, 0x13a: 0x2000,
	0x13c: 0x2000, 0x13e: 0x2000,
	// Block 0x5, offset 0x140
	0x141: 0x2000,
	0x151: 0x2000,
	0x153: 0x2000,
	0x15b: 0x2000,
	0x166: 0x2000, 0x167: 0x2000,
	0x16b: 0x2000,
	0x171: 0x2000, 0x172: 0x2000, 0x173: 0x2000,
	0x178: 0x2000,
	0x17f: 0x2000,
	// Block 0x6, offset 0x180
	0x180: 0x2000, 0x181: 0x2000, 0x182: 0x2000, 0x184: 0x2000,
	0x188: 0x2000, 0x189: 0x2000, 0x18a: 0x2000, 0x18b: 0x2000,
	0x18d: 0x2000,
	0x192: 0x2000, 0x193: 0x2000,
	0x1a6: 0x2000, 0x1a7: 0x2000,
	0x1ab: 0x2000,
	// Block 0x7, offset 0x1c0
	0x1ce: 0x2000, 0x1d0: 0x2000,
	0x1d2: 0x2000, 0x1d4: 0x2000, 0x1d6: 0x2000,
	0x1d8: 0x2000, 0x1da: 0x2000, 0x1dc: 0x2000,
	// Block 0x8, offset 0x200
	0x211: 0x2000,
	0x221: 0x2000,
	// Block 0x9, offset 0x240
	0x244: 0x2000,
	0x247: 0x2000, 0x249: 0x2000, 0x24a: 0x2000, 0x24b: 0x2000,
	0x24d: 0x2000, 0x250: 0x2000,
	0x258: 0x2000, 0x259: 0x2000, 0x25a: 0x2000, 0x25b: 0x2000, 0x25d: 0x2000,
	0x25f: 0x2000,
	// Block 0xa, offset 0x280
	0x280: 0x2000, 0x281: 0x2000, 0x282: 0x2000, 0x283: 0x2000, 0x284: 0x2000, 0x285: 0x2000,
	0x286: 0x2000, 0x287: 0x2000, 0x288: 0x2000, 0x289: 0x2000, 0x28a: 0x2000, 0x28b: 0x2000,
	0x28c: 0x2000, 0x28d: 0x2000, 0x28e: 0x2000, 0x28f: 0x2000, 0x290: 0x2000, 0x291: 0x2000,
	0x292: 0x2000, 0x293: 0x2000, 0x294: 0x2000, 0x295: 0x2000, 0x296: 0x2000, 0x297: 0x2000,
	0x298: 0x2000, 0x299: 0x2000, 0x29a: 0x2000, 0x29b: 0x2000, 0x29c: 0x2000, 0x29d: 0x2000,
	0x29e: 0x2000, 0x29f: 0x2000, 0x2a0: 0x2000, 0x2a1: 0x2000, 0x2a2: 0x2000, 0x2a3: 0x2000,
	0x2a4: 0x2000, 0x2a5: 0x2000, 0x2a6: 0x2000, 0x2a7: 0x2000, 0x2a8: 0x2000, 0x2a9: 0x2000,
	0x2aa: 0x2000, 0x2ab: 0x2000, 0x2ac: 0x2000, 0x2ad: 0x2000, 0x2ae: 0x2000, 0x2af: 0x2000,
	0x2b0: 0x2000, 0x2b1: 0x2000, 0x2b2: 0x2000, 0x2b3: 0x2000, 0x2b4: 0x2000, 0x2b5: 0x2000,
	0x2b6: 0x2000, 0x2b7: 0x2000, 0x2b8: 0x2000, 0x2b9: 0x2000, 0x2ba: 0x2000, 0x2bb: 0x2000,
	0x2bc: 0x2000, 0x2bd: 0x2000, 0x2be: 0x2000, 0x2bf: 0x2000,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x2000, 0x2c1: 0x2000, 0x2c2: 0x2000, 0x2c3: 0x2000, 0x2c4: 0x2000, 0x2c5: 0x2000,
	0x2c6: 0x2000, 0x2c7: 0x2000, 0x2c8: 0x2000, 0x2c9: 0x2000, 0x2ca: 0x2000, 0x2cb: 0x2000,
	0x2cc: 0x2000, 0x2cd: 0x2000, 0x2ce: 0x2000, 0x2cf: 0x2000, 0x2d0: 0x2000, 0x2d1: 0x2000,
	0x2d2: 0x2000, 0x2d3: 0x2000, 0x2d4: 0x2000, 0x2d5: 0x2000, 0x2d6: 0x2000, 0x2d7: 0x2000,
	0x2d8: 0x2000, 0x2d9: 0x2000, 0x2da: 0x2000, 0x2db: 0x2000, 0x2dc: 0x2000, 0x2dd: 0x2000,
	0x2de: 0x2000, 0x2df: 0x2000, 0x2e0: 0x2000, 0x2e1: 0x2000, 0x2e2: 0x2000, 0x2e3: 0x2000,
	0x2e4: 0x2000, 0x2e5: 0x2000, 0x2e6: 0x2000, 0x2e7: 0x2000, 0x2e8: 0x2000, 0x2e9: 0x2000,
	0x2ea: 0x2000, 0x2eb: 0x2000, 0x2ec: 0x2000, 0x2ed: 0x2000, 0x2ee: 0x2000, 0x2ef: 0x2000,
	// Block 0xc, offset 0x300
	0x311: 0x2000,
	0x312: 0x2000, 0x313: 0x2000, 0x314: 0x2000, 0x315: 0x2000, 0x316: 0x2000, 0x317: 0x2000,
	0x318: 0x2000, 0x319: 0x2000, 0x31a: 0x2000, 0x31b: 0x2000, 0x31c: 0x2000, 0x31d: 0x2000,
	0x31e: 0x2000, 0x31f: 0x2000, 0x320: 0x2000, 0x321: 0x2000, 0x323: 0x2000,
	0x324: 0x2000, 0x325: 0x2000, 0x326: 0x2000, 0x327: 0x2000, 0x328: 0x2000, 0x329: 0x2000,
	0x331: 0x2000, 0x332: 0x2000, 0x333: 0x2000, 0x334: 0x2000, 0x335: 0x2000,
	0x336: 0x2000, 0x337: 0x2000, 0x338: 0x2000, 0x339: 0x2000, 0x33a: 0x2000, 0x33b: 0x2000,
	0x33c: 0x2000, 0x33d: 0x2000, 0x33e: 0x2000, 0x33f: 0x2000,
	// Block 0xd, offset 0x340
	0x340: 0x2000, 0x341: 0x2000, 0x343: 0x2000, 0x344: 0x2000, 0x345: 0x2000,
	0x346: 0x2000, 0x347: 0x2000, 0x348: 0x2000, 0x349: 0x2000,
	// Block 0xe, offset 0x380
	0x381: 0x2000,
	0x390: 0x2000, 0x391: 0x2000,
	0x392: 0x2000, 0x393: 0x2000, 0x394: 0x2000, 0x395: 0x2000, 0x396: 0x2000, 0x397: 0x2000,
	0x398: 0x2000, 0x399: 0x2000, 0x39a: 0x2000, 0x39b: 0x2000, 0x39c: 0x2000, 0x39d: 0x2000,
	0x39e: 0x2000, 0x39f: 0x2000, 0x3a0: 0x2000, 0x3a1: 0x2000, 0x3a2: 0x2000, 0x3a3: 0x2000,
	0x3a4: 0x2000, 0x3a5: 0x2000, 0x3a6: 0x2000, 0x3a7: 0x2000, 0x3a8: 0x2000, 0x3a9: 0x2000,
	0x3aa: 0x2000, 0x3ab: 0x2000, 0x3ac: 0x2000, 0x3ad: 0x2000, 0x3ae: 0x2000, 0x3af: 0x2000,
	0x3b0: 0x2000, 0x3b1: 0x2000, 0x3b2: 0x2000, 0x3b3: 0x2000, 0x3b4: 0x2000, 0x3b5: 0x2000,
	0x3b6: 0x2000, 0x3b7: 0x2000, 0x3b8: 0x2000, 0x3b9: 0x2000, 0x3ba: 0x2000, 0x3bb: 0x2000,
	0x3bc: 0x2000, 0x3bd: 0x2000, 0x3be: 0x2000, 0x3bf: 0x2000,
	// Block 0xf, offset 0x3c0
	0x3c0: 0x2000, 0x3c1: 0x2000, 0x3c2: 0x2000, 0x3c3: 0x2000, 0x3c4: 0x2000, 0x3c5: 0x2000,
	0x3c6: 0x2000, 0x3c7: 0x2000, 0x3c8: 0x2000, 0x3c9: 0x2000, 0x3ca: 0x2000, 0x3cb: 0x2000,
	0x3cc: 0x2000, 0x3cd: 0x2000, 0x3ce: 0x2000, 0x3cf: 0x2000, 0x3d1: 0x2000,
	// Block 0x10, offset 0x400
	0x400: 0x4000, 0x401: 0x4000, 0x402: 0x4000, 0x403: 0x4000, 0x404: 0x4000, 0x405: 0x4000,
	0x406: 0x4000, 0x407: 0x4000, 0x408: 0x4000, 0x409: 0x4000, 0x40a: 0x4000, 0x40b: 0x4000,
	0x40c: 0x4000, 0x40d: 0x4000, 0x40e: 0x4000, 0x40f: 0x4000, 0x410: 0x4000, 0x411: 0x4000,
	0x412: 0x4000, 0x413: 0x4000, 0x414: 0x4000, 0x415: 0x4000, 0x416: 0x4000, 0x417: 0x4000,
	0x418: 0x4000, 0x419: 0x4000, 0x41a: 0x4000, 0x41b: 0x4000, 0x41c: 0x4000, 0x41d: 0x4000,
	0x41e: 0x4000, 0x41f: 0x4000, 0x420: 0x4000, 0x421: 0x4000, 0x422: 0x4000, 0x423: 0x4000,
	0x424: 0x4000, 0x425: 0x4000, 0x426: 0x4000, 0x427: 0x4000, 0x428: 0x4000, 0x429: 0x4000,
	0x42a: 0x4000, 0x42b: 0x4000, 0x42c: 0x4000, 0x42d: 0x4000, 0x42e: 0x4000, 0x42f: 0x4000,
	0x430: 0x4000, 0x431: 0x4000, 0x432: 0x4000, 0x433: 0x4000, 0x434: 0x4000, 0x435: 0x4000,
	0x436: 0x4000, 0x437: 0x4000, 0x438: 0x4000, 0x439: 0x4000, 0x43a: 0x4000, 0x43b: 0x4000,
	0x43c: 0x4000, 0x43d: 0x4000, 0x43e: 0x4000, 0x43f: 0x4000,
	// Block 0x11, offset 0x440
	0x440: 0x4000, 0x441: 0x4000, 0x442: 0x4000, 0x443: 0x4000, 0x444: 0x4000, 0x445: 0x4000,
	0x446: 0x4000, 0x447: 0x4000, 0x448: 0x4000, 0x449: 0x4000, 0x44a: 0x4000, 0x44b: 0x4000,
	0x44c: 0x4000, 0x44d: 0x4000, 0x44e: 0x4000, 0x44f: 0x4000, 0x450: 0x4000, 0x451: 0x4000,
	0x452: 0x4000, 0x453: 0x4000, 0x454: 0x4000, 0x455: 0x4000, 0x456: 0x4000, 0x457: 0x4000,
	0x458: 0x4000, 0x459: 0x4000, 0x45a: 0x4000, 0x45b: 0x4000, 0x45c: 0x4000, 0x45d: 0x4000,
	0x45e: 0x4000, 0x45f: 0x4000,
	// Block 0x12, offset 0x480
	0x490: 0x2000,
	0x493: 0x2000, 0x494: 0x2000, 0x495: 0x2000, 0x496: 0x2000,
	0x498: 0x2000, 0x499: 0x2000, 0x49c: 0x2000, 0x49d: 0x2000,
	0x4a0: 0x2000, 0x4a1: 0x2000, 0x4a2: 0x2000,
	0x4a4: 0x2000, 0x4a5: 0x2000, 0x4a6: 0x2000, 0x4a7: 0x2000,
	0x4b0: 0x2000, 0x4b2: 0x2000, 0x4b3: 0x2000, 0x4b5: 0x2000,
	0x4bb: 0x2000,
	0x4be: 0x2000,
	// Block 0x13, offset 0x4c0
	0x4f4: 0x2000,
	0x4ff: 0x2000,
	// Block 0x14, offset 0x500
	0x501: 0x2000, 0x502: 0x2000, 0x503: 0x2000, 0x504: 0x2000,
	0x529: 0xa009,
	0x52c: 0x2000,
	// Block 0x15, offset 0x540
	0x543: 0x2000, 0x545: 0x2000,
	0x549: 0x2000,
	0x553: 0x2000, 0x556: 0x2000,
	0x561: 0x2000, 0x562: 0x2000,
	0x566: 0x2000,
	0x56b: 0x2000,
	// Block 0x16, offset 0x580
	0x593: 0x2000, 0x594: 0x2000,
	0x59b: 0x2000, 0x59c: 0x2000, 0x59d: 0x2000,
	0x59e: 0x2000, 0x5a0: 0x2000, 0x5a1: 0x2000, 0x5a2: 0x2000, 0x5a3: 0x2000,
	0x5a4: 0x2000, 0x5a5: 0x2000, 0x5a6: 0x2000, 0x5a7: 0x2000, 0x5a8: 0x2000, 0x5a9: 0x2000,
	0x5aa: 0x2000, 0x5ab: 0x2000,
	0x5b0: 0x2000, 0x5b1: 0x2000, 0x5b2: 0x2000, 0x5b3: 0x2000, 0x5b4: 0x2000, 0x5b5: 0x2000,
	0x5b6: 0x2000, 0x5b7: 0x2000, 0x5b8: 0x2000, 0x5b9: 0x2000,
	// Block 0x17, offset 0x5c0
	0x5c9: 0x2000,
	0x5d0: 0x200a, 0x5d1: 0x200b,
	0x5d2: 0x200a, 0x5d3: 0x200c, 0x5d4: 0x2000, 0x5d5: 0x2000, 0x5d6: 0x2000, 0x5d7: 0x2000,
	0x5d8: 0x2000, 0x5d9: 0x2000,
	0x5f8: 0x2000, 0x5f9: 0x2000,
	// Block 0x18, offset 0x600
	0x612: 0x2000, 0x614: 0x2000,
	0x627: 0x2000,
	// Block 0x19, offset 0x640
	0x640: 0x2000, 0x642: 0x2000, 0x643: 0x2000,
	0x647: 0x2000, 0x648: 0x2000, 0x64b: 0x2000,
	0x64f: 0x2000, 0x651: 0x2000,
	0x655: 0x2000,
	0x65a: 0x2000, 0x65d: 0x2000,
	0x65e: 0x2000, 0x65f: 0x2000, 0x660: 0x2000, 0x663: 0x2000,
	0x665: 0x2000, 0x667: 0x2000, 0x668: 0x2000, 0x669: 0x2000,
	0x66a: 0x2000, 0x66b: 0x2000, 0x66c: 0x2000, 0x66e: 0x2000,
	0x674: 0x2000, 0x675: 0x2000,
	0x676: 0x2000, 0x677: 0x2000,
	0x67c: 0x2000, 0x67d: 0x2000,
	// Block 0x1a, offset 0x680
	0x688: 0x2000,
	0x68c: 0x2000,
	0x692: 0x2000,
	0x6a0: 0x2000, 0x6a1: 0x2000,
	0x6a4: 0x2000, 0x6a5: 0x2000, 0x6a6: 0x2000, 0x6a7: 0x2000,
	0x6aa: 0x2000, 0x6ab: 0x2000, 0x6ae: 0x2000, 0x6af: 0x2000,
	// Block 0x1b, offset 0x6c0
	0x6c2: 0x2000, 0x6c3: 0x2000,
	0x6c6: 0x2000, 0x6c7: 0x2000,
	0x6d5: 0x2000,
	0x6d9: 0x2000,
	0x6e5: 0x2000,
	0x6ff: 0x2000,
	// Block 0x1c, offset 0x700
	0x712: 0x2000,
	0x729: 0x4000,
	0x72a: 0x4000,
	// Block 0x1d, offset 0x740
	0x760: 0x2000, 0x761: 0x2000, 0x762: 0x2000, 0x763: 0x2000,
	0x764: 0x2000, 0x765: 0x2000, 0x766: 0x2000, 0x767: 0x2000, 0x768: 0x2000, 0x769: 0x2000,
	0x76a: 0x2000, 0x76b: 0x2000, 0x76c: 0x2000, 0x76d: 0x2000, 0x76e: 0x2000, 0x76f: 0x2000,
	0x770: 0x2000, 0x771: 0x2000, 0x772: 0x2000, 0x773: 0x2000, 0x774: 0x2000, 0x775: 0x2000,
	0x776: 0x2000, 0x777: 0x2000, 0x778: 0x2000, 0x779: 0x2000, 0x77a: 0x2000, 0x77b: 0x2000,
	0x77c: 0x2000, 0x77d: 0x2000, 0x77e: 0x2000, 0x77f: 0x2000,
	// Block 0x1e, offset 0x780
	0x780: 0x2000, 0x781: 0x2000, 0x782: 0x2000, 0x783: 0x2000, 0x784: 0x2000, 0x785: 0x2000,
	0x786: 0x2000, 0x787: 0x2000, 0x788: 0x2000, 0x789: 0x2000, 0x78a: 0x2000, 0x78b: 0x2000,
	0x78c: 0x2000, 0x78d: 0x2000, 0x78e: 0x2000, 0x78f: 0x2000, 0x790: 0x2000, 0x791: 0x2000,
	0x792: 0x2000, 0x793: 0x2000, 0x794: 0x2000, 0x795: 0x2000, 0x796: 0x2000, 0x797: 0x2000,
	0x798: 0x2000, 0x799: 0x2000, 0x79a: 0x2000, 0x79b: 0x2000, 0x79c: 0x2000, 0x79d: 0x2000,
	0x79e: 0x2000, 0x79f: 0x2000, 0x7a0: 0x2000, 0x7a1: 0x2000, 0x7a2: 0x2000, 0x7a3: 0x2000,
	0x7a4: 0x2000, 0x7a5: 0x2000, 0x7a6: 0x2000, 0x7a7: 0x2000, 0x7a8: 0x2000, 0x7a9: 0x2000,
	0x7ab: 0x2000, 0x7ac: 0x2000, 0x7ad: 0x2000, 0x7ae: 0x2000, 0x7af: 0x2000,
	0x7b0: 0x2000, 0x7b1: 0x2000, 0x7b2: 0x2000, 0x7b3: 0x2000, 0x7b4: 0x2000, 0x7b5: 0x2000,
	0x7b6: 0x2000, 0x7b7: 0x2000, 0x7b8: 0x2000, 0x7b9: 0x2000, 0x7ba: 0x2000, 0x7bb: 0x2000,
	0x7bc: 0x2000, 0x7bd: 0x2000, 0x7be: 0x2000, 0x7bf: 0x2000,
	// Block 0x1f, offset 0x7c0
	0x7c0: 0x2000, 0x7c1: 0x2000, 0x7c2: 0x200d, 0x7c3: 0x2000, 0x7c4: 0x2000, 0x7c5: 0x2000,
	0x7c6: 0x2000, 0x7c7: 0x2000, 0x7c8: 0x2000, 0x7c9: 0x2000, 0x7ca: 0x2000, 0x7cb: 0x2000,
	0x7cc: 0x2000, 0x7cd: 0x2000, 0x7ce: 0x2000, 0x7cf: 0x2000, 0x7d0: 0x2000, 0x7d1: 0x2000,
	0x7d2: 0x2000, 0x7d3: 0x2000, 0x7d4: 0x2000, 0x7d5: 0x2000, 0x7d6: 0x2000, 0x7d7: 0x2000,
	0x7d8: 0x2000, 0x7d9: 0x2000, 0x7da: 0x2000, 0x7db: 0x2000, 0x7dc: 0x2000, 0x7dd: 0x2000,
	0x7de: 0x2000, 0x7df: 0x2000, 0x7e0: 0x2000, 0x7e1: 0x2000, 0x7e2: 0x2000, 0x7e3: 0x2000,
	0x7e4: 0x2000, 0x7e5: 0x2000, 0x7e6: 0x2000, 0x7e7: 0x2000, 0x7e8: 0x2000, 0x7e9: 0x2000,
	0x7ea: 0x2000, 0x7eb: 0x2000, 0x7ec: 0x2000, 0x7ed: 0x2000, 0x7ee: 0x2000, 0x7ef: 0x2000,
	0x7f0: 0x2000, 0x7f1: 0x2000, 0x7f2: 0x2000, 0x7f3: 0x2000, 0x7f4: 0x2000, 0x7f5: 0x2000,
	0x7f6: 0x2000, 0x7f7: 0x2000, 0x7f8: 0x2000, 0x7f9: 0x2000, 0x7fa: 0x2000, 0x7fb: 0x2000,
	0x7fc: 0x2000, 0x7fd: 0x2000, 0x7fe: 0x2000, 0x7ff: 0x2000,
	// Block 0x20, offset 0x800
	0x800: 0x2000, 0x801: 0x2000, 0x802: 0x2000, 0x803: 0x2000, 0x804: 0x2000, 0x805: 0x2000,
	0x806: 0x2000, 0x807: 0x2000, 0x808: 0x2000, 0x809: 0x2000, 0x80a: 0x2000, 0x80b: 0x2000,
	0x810: 0x2000, 0x811: 0x2000,
	0x812: 0x2000, 0x813: 0x2000, 0x814: 0x2000, 0x815: 0x2000, 0x816: 0x2000, 0x817: 0x2000,
	0x818: 0x2000, 0x819: 0x2000, 0x81a: 0x2000, 0x81b: 0x2000, 0x81c: 0x2000, 0x81d: 0x2000,
	0x81e: 0x2000, 0x81f: 0x2000, 0x820: 0x2000, 0x821: 0x2000, 0x822: 0x2000, 0x823: 0x2000,
	0x824: 0x2000, 0x825: 0x2000, 0x826: 0x2000, 0x827: 0x2000, 0x828: 0x2000, 0x829: 0x2000,
	0x82a: 0x2000, 0x82b: 0x2000, 0x82c: 0x2000, 0x82d: 0x2000, 0x82e: 0x2000, 0x82f: 0x2000,
	0x830: 0x2000, 0x831: 0x2000, 0x832: 0x2000, 0x833: 0x2000,
	// Block 0x21, offset 0x840
	0x840: 0x2000, 0x841: 0x2000, 0x842: 0x2000, 0x843: 0x2000, 0x844: 0x2000, 0x845: 0x2000,
	0x846: 0x2000, 0x847: 0x2000, 0x848: 0x2000, 0x849: 0x2000, 0x84a: 0x2000, 0x84b: 0x2000,
	0x84c: 0x2000, 0x84d: 0x2000, 0x84e: 0x2000, 0x84f: 0x2000,
	0x852: 0x2000, 0x853: 0x2000, 0x854: 0x2000, 0x855: 0x2000,
	0x860: 0x200e, 0x861: 0x2000, 0x863: 0x2000,
	0x864: 0x2000, 0x865: 0x2000, 0x866: 0x2000, 0x867: 0x2000, 0x868: 0x2000, 0x869: 0x2000,
	0x872: 0x2000, 0x873: 0x2000,
	0x876: 0x2000, 0x877: 0x2000,
	0x87c: 0x2000, 0x87d: 0x2000,
	// Block 0x22, offset 0x880
	0x880: 0x2000, 0x881: 0x2000,
	0x886: 0x2000, 0x887: 0x2000, 0x888: 0x2000, 0x88b: 0x200f,
	0x88e: 0x2000, 0x88f: 0x2000, 0x890: 0x2000, 0x891: 0x2000,
	0x8a2: 0x2000, 0x8a3: 0x2000,
	0x8a4: 0x2000, 0x8a5: 0x2000,
	0x8af: 0x2000,
	// Block 0x23, offset 0x8c0
	0x8c5: 0x2000,
	0x8c6: 0x2000, 0x8c9: 0x2000,
	0x8ce: 0x2000, 0x8cf: 0x2000,
	0x8d4: 0x2000, 0x8d5: 0x2000,
	0x8dc: 0x2000,
	0x8de: 0x2000,
	// Block 0x24, offset 0x900
	0x900: 0x2000, 0x902: 0x2000,
	0x920: 0x2000, 0x921: 0x2000, 0x923: 0x2000,
	0x924: 0x2000, 0x925: 0x2000, 0x927: 0x2000, 0x928: 0x2000, 0x929: 0x2000,
	0x92a: 0x2000, 0x92c: 0x2000, 0x92d: 0x2000, 0x92f: 0x2000,
	// Block 0x25, offset 0x940
	0x95e: 0x2000, 0x95f: 0x2000,
	0x97e: 0x2000, 0x97f: 0x2000,
	// Block 0x26, offset 0x980
	0x984: 0x2000, 0x985: 0x2000,
	0x986: 0x2000, 0x987: 0x2000, 0x988: 0x2000, 0x989: 0x2000, 0x98a: 0x2000, 0x98b: 0x2000,
	0x98c: 0x2000, 0x98d: 0x2000, 0x98f: 0x2000, 0x990: 0x2000, 0x991: 0x2000,
	0x992: 0x2000, 0x993: 0x2000, 0x994: 0x2000, 0x995: 0x2000, 0x996: 0x2000, 0x997: 0x2000,
	0x998: 0x2000, 0x999: 0x2000, 0x99a: 0x2000, 0x99b: 0x2000, 0x99c: 0x2000, 0x99d: 0x2000,
	0x99e: 0x2000, 0x99f: 0x2000, 0x9a0: 0x2000, 0x9a1: 0x2000, 0x9a3: 0x2000,
	0x9a8: 0x2000, 0x9a9: 0x2000,
	0x9aa: 0x2000, 0x9ab: 0x2000, 0x9ac: 0x2000, 0x9ad: 0x2000, 0x9ae: 0x2000, 0x9af: 0x2000,
	0x9b0: 0x2000, 0x9b1: 0x2000, 0x9b2: 0x2000, 0x9b3: 0x2000, 0x9b4: 0x2000, 0x9b5: 0x2000,
	0x9b6: 0x2000, 0x9b7: 0x2000, 0x9b8: 0x2000, 0x9b9: 0x2000, 0x9ba: 0x2000, 0x9bb: 0x2000,
	0x9bc: 0x2000, 0x9bd: 0x2000, 0x9be: 0x2000, 0x9bf: 0x2000,
	// Block 0x27, offset 0x9c0
	0x9fd: 0x2000,
	// Block 0x28, offset 0xa00
	0xa17: 0x2000,
	0xa36: 0x2000, 0xa37: 0x2000, 0xa38: 0x2000, 0xa39: 0x2000, 0xa3a: 0x2000, 0xa3b: 0x2000,
	0xa3c: 0x2000, 0xa3d: 0x2000, 0xa3e: 0x2000, 0xa3f: 0x2000,
	// Block 0x29, offset 0xa40
	0xa66: 0x6000, 0xa67: 0x6000, 0xa68: 0x6000, 0xa69: 0x6000,
	0xa6a: 0x6000, 0xa6b: 0x6000, 0xa6c: 0x6000, 0xa6d: 0x6000,
	// Block 0x2a, offset 0xa80
	0xa85: 0x6010,
	0xa86: 0x6011,
	// Block 0x2b, offset 0xac0
	0xad5: 0x2000, 0xad6: 0x2000, 0xad7: 0x2000,
	0xad8: 0x2000, 0xad9: 0x2000,
	// Block 0x2c, offset 0xb00
	0xb00: 0x4000, 0xb01: 0x4000, 0xb02: 0x4000, 0xb03: 0x4000, 0xb04: 0x4000, 0xb05: 0x4000,
	0xb06: 0x4000, 0xb07: 0x4000, 0xb08: 0x4000, 0xb09: 0x4000, 0xb0a: 0x4000, 0xb0b: 0x4000,
	0xb0c: 0x4000, 0xb0d: 0x4000, 0xb0e: 0x4000, 0xb0f: 0x4000, 0xb10: 0x4000, 0xb11: 0x4000,
	0xb12: 0x4000, 0xb13: 0x4000, 0xb14: 0x4000, 0xb15: 0x4000, 0xb16: 0x4000, 0xb17: 0x4000,
	0xb18: 0x4000, 0xb19: 0x4000, 0xb1b: 0x4000, 0xb1c: 0x4000, 0xb1d: 0x4000,
	0xb1e: 0x4000, 0xb1f: 0x4000, 0xb20: 0x4000, 0xb21: 0x4000, 0xb22: 0x4000, 0xb23: 0x4000,
	0xb24: 0x4000, 0xb25: 0x4000, 0xb26: 0x4000, 0xb27: 0x4000, 0xb28: 0x4000, 0xb29: 0x4000,
	0xb2a: 0x4000, 0xb2b: 0x4000, 0xb2c: 0x4000, 0xb2d: 0x4000, 0xb2e: 0x4000, 0xb2f: 0x4000,
	0xb30: 0x4000, 0xb31: 0x4000, 0xb32: 0x4000, 0xb33: 0x4000, 0xb34: 0x4000, 0xb35: 0x4000,
	0xb36: 0x4000, 0xb37: 0x4000, 0xb38: 0x4000, 0xb39: 0x4000, 0xb3a: 0x4000, 0xb3b: 0x4000,
	0xb3c: 0x4000, 0xb3d: 0x4000, 0xb3e: 0x4000, 0xb3f: 0x4000,
	// Block 0x2d, offset 0xb40
	0xb40: 0x4000, 0xb41: 0x4000, 0xb42: 0x4000, 0xb43: 0x4000, 0xb44: 0x4000, 0xb45: 0x4000,
	0xb46: 0x4000, 0xb47: 0x4000, 0xb48: 0x4000, 0xb49: 0x4000, 0xb4a: 0x4000, 0xb4b: 0x4000,
	0xb4c: 0x4000, 0xb4d: 0x4000, 0xb4e: 0x4000, 0xb4f: 0x4000, 0xb50: 0x4000, 0xb51: 0x4000,
	0xb52: 0x4000, 0xb53: 0x4000, 0xb54: 0x4000, 0xb55: 0x4000, 0xb56: 0x4000, 0xb57: 0x4000,
	0xb58: 0x4000, 0xb59: 0x4000, 0xb5a: 0x4000, 0xb5b: 0x4000, 0xb5c: 0x4000, 0xb5d: 0x4000,
	0xb5e: 0x4000, 0xb5f: 0x4000, 0xb60: 0x4000, 0xb61: 0x4000, 0xb62: 0x4000, 0xb63: 0x4000,
	0xb64: 0x4000, 0xb65: 0x4000, 0xb66: 0x4000, 0xb67: 0x4000, 0xb68: 0x4000, 0xb69: 0x4000,
	0xb6a: 0x4000, 0xb6b: 0x4000, 0xb6c: 0x4000, 0xb6d: 0x4000, 0xb6e: 0x4000, 0xb6f: 0x4000,
	0xb70: 0x4000, 0xb71: 0x4000, 0xb72: 0x4000, 0xb73: 0x4000,
	// Block 0x2e, offset 0xb80
	0xb80: 0x4000, 0xb81: 0x4000, 0xb82: 0x4000, 0xb83: 0x4000, 0xb84: 0x4000, 0xb85: 0x4000,
	0xb86: 0x4000, 0xb87: 0x4000, 0xb88: 0x4000, 0xb89: 0x4000, 0xb8a: 0x4000, 0xb8b: 0x4000,
	0xb8c: 0x4000, 0xb8d: 0x4000, 0xb8e: 0x4000, 0xb8f: 0x4000, 0xb90: 0x4000, 0xb91: 0x4000,
	0xb92: 0x4000, 0xb93: 0x4000, 0xb94: 0x4000, 0xb95: 0x4000,
	0xbb0: 0x4000, 0xbb1: 0x4000, 0xbb2: 0x4000, 0xbb3: 0x4000, 0xbb4: 0x4000, 0xbb5: 0x4000,
	0xbb6: 0x4000, 0xbb7: 0x4000, 0xbb8: 0x4000, 0xbb9: 0x4000, 0xbba: 0x4000, 0xbbb: 0x4000,
	// Block 0x2f, offset 0xbc0
	0xbc0: 0x9012, 0xbc1: 0x4013, 0xbc2: 0x4014, 0xbc3: 0x4000, 0xbc4: 0x4000, 0xbc5: 0x4000,
	0xbc6: 0x4000, 0xbc7: 0x4000, 0xbc8: 0x4000, 0xbc9: 0x4000, 0xbca: 0x4000, 0xbcb: 0x4000,
	0xbcc: 0x4015, 0xbcd: 0x4015, 0xbce: 0x4000, 0xbcf: 0x4000, 0xbd0: 0x4000, 0xbd1: 0x4000,
	0xbd2: 0x4000, 0xbd3: 0x4000, 0xbd4: 0x4000, 0xbd5: 0x4000, 0xbd6: 0x4000, 0xbd7: 0x4000,
	0xbd8: 0x4000, 0xbd9: 0x4000, 0xbda: 0x4000, 0xbdb: 0x4000, 0xbdc: 0x4000, 0xbdd: 0x4000,
	0xbde: 0x4000, 0xbdf: 0x4000, 0xbe0: 0x4000, 0xbe1: 0x4000, 0xbe2: 0x4000, 0xbe3: 0x4000,
	0xbe4: 0x4000, 0xbe5: 0x4000, 0xbe6: 0x4000, 0xbe7: 0x4000, 0xbe8: 0x4000, 0xbe9: 0x4000,
	0xbea: 0x4000, 0xbeb: 0x4000, 0xbec: 0x4000, 0xbed: 0x4000, 0xbee: 0x4000, 0xbef: 0x4000,
	0xbf0: 0x4000, 0xbf1: 0x4000, 0xbf2: 0x4000, 0xbf3: 0x4000, 0xbf4: 0x4000, 0xbf5: 0x4000,
	0xbf6: 0x4000, 0xbf7: 0x4000, 0xbf8: 0x4000, 0xbf9: 0x4000, 0xbfa: 0x4000, 0xbfb: 0x4000,
	0xbfc: 0x4000, 0xbfd: 0x4000, 0xbfe: 0x4000,
	// Block 0x30, offset 0xc00
	0xc01: 0x4000, 0xc02: 0x4000, 0xc03: 0x4000, 0xc04: 0x4000, 0xc05: 0x4000,
	0xc06: 0x4000, 0xc07: 0x4000, 0xc08: 0x4000, 0xc09: 0x4000, 0xc0a: 0x4000, 0xc0b: 0x4000,
	0xc0c: 0x4000, 0xc0d: 0x4000, 0xc0e: 0x4000, 0xc0f: 0x4000, 0xc10: 0x4000, 0xc11: 0x4000,
	0xc12: 0x4000, 0xc13: 0x4000, 0xc14: 0x4000, 0xc15: 0x4000, 0xc16: 0x4000, 0xc17: 0x4000,
	0xc18: 0x4000, 0xc19: 0x4000, 0xc1a: 0x4000, 0xc1b: 0x4000, 0xc1c: 0x4000, 0xc1d: 0x4000,
	0xc1e: 0x4000, 0xc1f: 0x4000, 0xc20: 0x4000, 0xc21: 0x4000, 0xc22: 0x4000, 0xc23: 0x4000,
	0xc24: 0x4000, 0xc25: 0x4000, 0xc26: 0x4000, 0xc27: 0x4000, 0xc28: 0x4000, 0xc29: 0x4000,
	0xc2a: 0x4000, 0xc2b: 0x4000, 0xc2c: 0x4000, 0xc2d: 0x4000, 0xc2e: 0x4000, 0xc2f: 0x4000,
	0xc30: 0x4000, 0xc31: 0x4000, 0xc32: 0x4000, 0xc33: 0x4000, 0xc34: 0x4000, 0xc35: 0x4000,
	0xc36: 0x4000, 0xc37: 0x4000, 0xc38: 0x4000, 0xc39: 0x4000, 0xc3a: 0x4000, 0xc3b: 0x4000,
	0xc3c: 0x4000, 0xc3d: 0x4000, 0xc3e: 0x4000, 0xc3f: 0x4000,
	// Block 0x31, offset 0xc40
	0xc40: 0x4000, 0xc41: 0x4000, 0xc42: 0x4000, 0xc43: 0x4000, 0xc44: 0x4000, 0xc45: 0x4000,
	0xc46: 0x4000, 0xc47: 0x4000, 0xc48: 0x4000, 0xc49: 0x4000, 0xc4a: 0x4000, 0xc4b: 0x4000,
	0xc4c: 0x4000, 0xc4d: 0x4000, 0xc4e: 0x4000, 0xc4f: 0x4000, 0xc50: 0x4000, 0xc51: 0x4000,
	0xc52: 0x4000, 0xc53: 0x4000, 0xc54: 0x4000, 0xc55: 0x4000, 0xc56: 0x4000,
	0xc59: 0x4016, 0xc5a: 0x4017, 0xc5b: 0x4000, 0xc5c: 0x4000, 0xc5d: 0x4000,
	0xc5e: 0x4000, 0xc5f: 0x4000, 0xc60: 0x4000, 0xc61: 0x4018, 0xc62: 0x4019, 0xc63: 0x401a,
	0xc64: 0x401b, 0xc65: 0x401c, 0xc66: 0x401d, 0xc67: 0x401e, 0xc68: 0x401f, 0xc69: 0x4020,
	0xc6a: 0x4021, 0xc6b: 0x4022, 0xc6c: 0x4000, 0xc6d: 0x4010, 0xc6e: 0x4000, 0xc6f: 0x4023,
	0xc70: 0x4000, 0xc71: 0x4024, 0xc72: 0x4000, 0xc73: 0x4025, 0xc74: 0x4000, 0xc75: 0x4026,
	0xc76: 0x4000, 0xc77: 0x401a, 0xc78: 0x4000, 0xc79: 0x4027, 0xc7a: 0x4000, 0xc7b: 0x4028,
	0xc7c: 0x4000, 0xc7d: 0x4020, 0xc7e: 0x4000, 0xc7f: 0x4029,
	// Block 0x32, offset 0xc80
	0xc80: 0x4000, 0xc81: 0x402a, 0xc82: 0x4000, 0xc83: 0x402b, 0xc84: 0x402c, 0xc85: 0x4000,
	0xc86: 0x4017, 0xc87: 0x4000, 0xc88: 0x402d, 0xc89: 0x4000, 0xc8a: 0x402e, 0xc8b: 0x402f,
	0xc8c: 0x4030, 0xc8d: 0x4017, 0xc8e: 0x4016, 0xc8f: 0x4017, 0xc90: 0x4000, 0xc91: 0x4000,
	0xc92: 0x4031, 0xc93: 0x4000, 0xc94: 0x4000, 0xc95: 0x4031, 0xc96: 0x4000, 0xc97: 0x4000,
	0xc98: 0x4032, 0xc99: 0x4000, 0xc9a: 0x4000, 0xc9b: 0x4032, 0xc9c: 0x4000, 0xc9d: 0x4000,
	0xc9e: 0x4033, 0xc9f: 0x402e, 0xca0: 0x4034, 0xca1: 0x4035, 0xca2: 0x4034, 0xca3: 0x4036,
	0xca4: 0x4037, 0xca5: 0x4024, 0xca6: 0x4035, 0xca7: 0x4025, 0xca8: 0x4038, 0xca9: 0x4038,
	0xcaa: 0x4039, 0xcab: 0x4039, 0xcac: 0x403a, 0xcad: 0x403a, 0xcae: 0x4000, 0xcaf: 0x4035,
	0xcb0: 0x4000, 0xcb1: 0x4000, 0xcb2: 0x403b, 0xcb3: 0x403c, 0xcb4: 0x4000, 0xcb5: 0x4000,
	0xcb6: 0x4000, 0xcb7: 0x4000, 0xcb8: 0x4000, 0xcb9: 0x4000, 0xcba: 0x4000, 0xcbb: 0x403d,
	0xcbc: 0x401c, 0xcbd: 0x4000, 0xcbe: 0x4000, 0xcbf: 0x4000,
	// Block 0x33, offset 0xcc0
	0xcc5: 0x4000,
	0xcc6: 0x4000, 0xcc7: 0x4000, 0xcc8: 0x4000, 0xcc9: 0x4000, 0xcca: 0x4000, 0xccb: 0x4000,
	0xccc: 0x4000, 0xccd: 0x4000, 0xcce: 0x4000, 0xccf: 0x4000, 0xcd0: 0x4000, 0xcd1: 0x4000,
	0xcd2: 0x4000, 0xcd3: 0x4000, 0xcd4: 0x4000, 0xcd5: 0x4000, 0xcd6: 0x4000, 0xcd7: 0x4000,
	0xcd8: 0x4000, 0xcd9: 0x4000, 0xcda: 0x4000, 0xcdb: 0x4000, 0xcdc: 0x4000, 0xcdd: 0x4000,
	0xcde: 0x4000, 0xcdf: 0x4000, 0xce0: 0x4000, 0xce1: 0x4000, 0xce2: 0x4000, 0xce3: 0x4000,
	0xce4: 0x4000, 0xce5: 0x4000, 0xce6: 0x4000, 0xce7: 0x4000, 0xce8: 0x4000, 0xce9: 0x4000,
	0xcea: 0x4000, 0xceb: 0x4000, 0xcec: 0x4000, 0xced: 0x4000,
	0xcf1: 0x403e, 0xcf2: 0x403e, 0xcf3: 0x403e, 0xcf4: 0x403e, 0xcf5: 0x403e,
	0xcf6: 0x403e, 0xcf7: 0x403e, 0xcf8: 0x403e, 0xcf9: 0x403e, 0xcfa: 0x403e, 0xcfb: 0x403e,
	0xcfc: 0x403e, 0xcfd: 0x403e, 0xcfe: 0x403e, 0xcff: 0x403e,
	// Block 0x34, offset 0xd00
	0xd00: 0x4037, 0xd01: 0x4037, 0xd02: 0x4037, 0xd03: 0x4037, 0xd04: 0x4037, 0xd05: 0x4037,
	0xd06: 0x4037, 0xd07: 0x4037, 0xd08: 0x4037, 0xd09: 0x4037, 0xd0a: 0x4037, 0xd0b: 0x4037,
	0xd0c: 0x4037, 0xd0d: 0x4037, 0xd0e: 0x4037, 0xd0f: 0x400e, 0xd10: 0x403f, 0xd11: 0x4040,
	0xd12: 0x4041, 0xd13: 0x4040, 0xd14: 0x403f, 0xd15: 0x4042, 0xd16: 0x4043, 0xd17: 0x4044,
	0xd18: 0x4040, 0xd19: 0x4041, 0xd1a: 0x4040, 0xd1b: 0x4045, 0xd1c: 0x4009, 0xd1d: 0x4045,
	0xd1e: 0x4046, 0xd1f: 0x4045, 0xd20: 0x4047, 0xd21: 0x400b, 0xd22: 0x400a, 0xd23: 0x400c,
	0xd24: 0x4048, 0xd25: 0x4000, 0xd26: 0x4000, 0xd27: 0x4000, 0xd28: 0x4000, 0xd29: 0x4000,
	0xd2a: 0x4000, 0xd2b: 0x4000, 0xd2c: 0x4000, 0xd2d: 0x4000, 0xd2e: 0x4000, 0xd2f: 0x4000,
	0xd30: 0x4000, 0xd31: 0x4000, 0xd32: 0x4000, 0xd33: 0x4000, 0xd34: 0x4000, 0xd35: 0x4000,
	0xd36: 0x4000, 0xd37: 0x4000, 0xd38: 0x4000, 0xd39: 0x4000, 0xd3a: 0x4000, 0xd3b: 0x4000,
	0xd3c: 0x4000, 0xd3d: 0x4000, 0xd3e: 0x4000, 0xd3f: 0x4000,
	// Block 0x35, offset 0xd40
	0xd40: 0x4000, 0xd41: 0x4000, 0xd42: 0x4000, 0xd43: 0x4000, 0xd44: 0x4000, 0xd45: 0x4000,
	0xd46: 0x4000, 0xd47: 0x4000, 0xd48: 0x4000, 0xd49: 0x4000, 0xd4a: 0x4000, 0xd4b: 0x4000,
	0xd4c: 0x4000, 0xd4d: 0x4000, 0xd4e: 0x4000, 0xd50: 0x4000, 0xd51: 0x4000,
	0xd52: 0x4000, 0xd53: 0x4000, 0xd54: 0x4000, 0xd55: 0x4000, 0xd56: 0x4000, 0xd57: 0x4000,
	0xd58: 0x4000, 0xd59: 0x4000, 0xd5a: 0x4000, 0xd5b: 0x4000, 0xd5c: 0x4000, 0xd5d: 0x4000,
	0xd5e: 0x4000, 0xd5f: 0x4000, 0xd60: 0x4000, 0xd61: 0x4000, 0xd62: 0x4000, 0xd63: 0x4000,
	0xd64: 0x4000, 0xd65: 0x4000, 0xd66: 0x4000, 0xd67: 0x4000, 0xd68: 0x4000, 0xd69: 0x4000,
	0xd6a: 0x4000, 0xd6b: 0x4000, 0xd6c: 0x4000, 0xd6d: 0x4000, 0xd6e: 0x4000, 0xd6f: 0x4000,
	0xd70: 0x4000, 0xd71: 0x4000, 0xd72: 0x4000, 0xd73: 0x4000, 0xd74: 0x4000, 0xd75: 0x4000,
	0xd76: 0x4000, 0xd77: 0x4000, 0xd78: 0x4000, 0xd79: 0x4000, 0xd7a: 0x4000,
	// Block 0x36, offset 0xd80
	0xd80: 0x4000, 0xd81: 0x4000, 0xd82: 0x4000, 0xd83: 0x4000, 0xd84: 0x4000, 0xd85: 0x4000,
	0xd86: 0x4000, 0xd87: 0x4000, 0xd88: 0x4000, 0xd89: 0x4000, 0xd8a: 0x4000, 0xd8b: 0x4000,
	0xd8c: 0x4000, 0xd8d: 0x4000, 0xd8e: 0x4000, 0xd8f: 0x4000, 0xd90: 0x4000, 0xd91: 0x4000,
	0xd92: 0x4000, 0xd93: 0x4000, 0xd94: 0x4000, 0xd95: 0x4000, 0xd96: 0x4000, 0xd97: 0x4000,
	0xd98: 0x4000, 0xd99: 0x4000, 0xd9a: 0x4000, 0xd9b: 0x4000, 0xd9c: 0x4000, 0xd9d: 0x4000,
	0xd9e: 0x4000, 0xd9f: 0x4000, 0xda0: 0x4000, 0xda1: 0x4000, 0xda2: 0x4000, 0xda3: 0x4000,
	0xdb0: 0x4000, 0xdb1: 0x4000, 0xdb2: 0x4000, 0xdb3: 0x4000, 0xdb4: 0x4000, 0xdb5: 0x4000,
	0xdb6: 0x4000, 0xdb7: 0x4000, 0xdb8: 0x4000, 0xdb9: 0x4000, 0xdba: 0x4000, 0xdbb: 0x4000,
	0xdbc: 0x4000, 0xdbd: 0x4000, 0xdbe: 0x4000, 0xdbf: 0x4000,
	// Block 0x37, offset 0xdc0
	0xdc0: 0x4000, 0xdc1: 0x4000, 0xdc2: 0x4000, 0xdc3: 0x4000, 0xdc4: 0x4000, 0xdc5: 0x4000,
	0xdc6: 0x4000, 0xdc7: 0x4000, 0xdc8: 0x4000, 0xdc9: 0x4000, 0xdca: 0x4000, 0xdcb: 0x4000,
	0xdcc: 0x4000, 0xdcd: 0x4000, 0xdce: 0x4000, 0xdcf: 0x4000, 0xdd0: 0x4000, 0xdd1: 0x4000,
	0xdd2: 0x4000, 0xdd3: 0x4000, 0xdd4: 0x4000, 0xdd5: 0x4000, 0xdd6: 0x4000, 0xdd7: 0x4000,
	0xdd8: 0x4000, 0xdd9: 0x4000, 0xdda: 0x4000, 0xddb: 0x4000, 0xddc: 0x4000, 0xddd: 0x4000,
	0xdde: 0x4000, 0xde0: 0x4000, 0xde1: 0x4000, 0xde2: 0x4000, 0xde3: 0x4000,
	0xde4: 0x4000, 0xde5: 0x4000, 0xde6: 0x4000, 0xde7: 0x4000, 0xde8: 0x4000, 0xde9: 0x4000,
	0xdea: 0x4000, 0xdeb: 0x4000, 0xdec: 0x4000, 0xded: 0x4000, 0xdee: 0x4000, 0xdef: 0x4000,
	0xdf0: 0x4000, 0xdf1: 0x4000, 0xdf2: 0x4000, 0xdf3: 0x4000, 0xdf4: 0x4000, 0xdf5: 0x4000,
	0xdf6: 0x4000, 0xdf7: 0x4000, 0xdf8: 0x4000, 0xdf9: 0x4000, 0xdfa: 0x4000, 0xdfb: 0x4000,
	0xdfc: 0x4000, 0xdfd: 0x4000, 0xdfe: 0x4000, 0xdff: 0x4000,
	// Block 0x38, offset 0xe00
	0xe00: 0x4000, 0xe01: 0x4000, 0xe02: 0x4000, 0xe03: 0x4000, 0xe04: 0x4000, 0xe05: 0x4000,
	0xe06: 0x4000, 0xe07: 0x4000, 0xe08: 0x2000, 0xe09: 0x2000, 0xe0a: 0x2000, 0xe0b: 0x2000,
	0xe0c: 0x2000, 0xe0d: 0x2000, 0xe0e: 0x2000, 0xe0f: 0x2000, 0xe10: 0x4000, 0xe11: 0x4000,
	0xe12: 0x4000, 0xe13: 0x4000, 0xe14: 0x4000, 0xe15: 0x4000, 0xe16: 0x4000, 0xe17: 0x4000,
	0xe18: 0x4000, 0xe19: 0x4000, 0xe1a: 0x4000, 0xe1b: 0x4000, 0xe1c: 0x4000, 0xe1d: 0x4000,
	0xe1e: 0x4000, 0xe1f: 0x4000, 0xe20: 0x4000, 0xe21: 0x4000, 0xe22: 0x4000, 0xe23: 0x4000,
	0xe24: 0x4000, 0xe25: 0x4000, 0xe26: 0x4000, 0xe27: 0x4000, 0xe28: 0x4000, 0xe29: 0x4000,
	0xe2a: 0x4000, 0xe2b: 0x4000, 0xe2c: 0x4000, 0xe2d: 0x4000, 0xe2e: 0x4000, 0xe2f: 0x4000,
	0xe30: 0x4000, 0xe31: 0x4000, 0xe32: 0x4000, 0xe33: 0x4000, 0xe34: 0x4000, 0xe35: 0x4000,
	0xe36: 0x4000, 0xe37: 0x4000, 0xe38: 0x4000, 0xe39: 0x4000, 0xe3a: 0x4000, 0xe3b: 0x4000,
	0xe3c: 0x4000, 0xe3d: 0x4000, 0xe3e: 0x4000, 0xe3f: 0x4000,
	// Block 0x39, offset 0xe40
	0xe40: 0x4000, 0xe41: 0x4000, 0xe42: 0x4000, 0xe43: 0x4000, 0xe44: 0x4000, 0xe45: 0x4000,
	0xe46: 0x4000, 0xe47: 0x4000, 0xe48: 0x4000, 0xe49: 0x4000, 0xe4a: 0x4000, 0xe4b: 0x4000,
	0xe4c: 0x4000, 0xe4d: 0x4000, 0xe4e: 0x4000, 0xe4f: 0x4000, 0xe50: 0x4000, 0xe51: 0x4000,
	0xe52: 0x4000, 0xe53: 0x4000, 0xe54: 0x4000, 0xe55: 0x4000, 0xe56: 0x4000, 0xe57: 0x4000,
	0xe58: 0x4000, 0xe59: 0x4000, 0xe5a: 0x4000, 0xe5b: 0x4000, 0xe5c: 0x4000, 0xe5d: 0x4000,
	0xe5e: 0x4000, 0xe5f: 0x4000, 0xe60: 0x4000, 0xe61: 0x4000, 0xe62: 0x4000, 0xe63: 0x4000,
	0xe64: 0x4000, 0xe65: 0x4000, 0xe66: 0x4000, 0xe67: 0x4000, 0xe68: 0x4000, 0xe69: 0x4000,
	0xe6a: 0x4000, 0xe6b: 0x4000, 0xe6c: 0x4000, 0xe6d: 0x4000, 0xe6e: 0x4000, 0xe6f: 0x4000,
	0xe70: 0x4000, 0xe71: 0x4000, 0xe72: 0x4000, 0xe73: 0x4000, 0xe74: 0x4000, 0xe75: 0x4000,
	0xe76: 0x4000, 0xe77: 0x4000, 0xe78: 0x4000, 0xe79: 0x4000, 0xe7a: 0x4000, 0xe7b: 0x4000,
	0xe7c: 0x4000, 0xe7d: 0x4000, 0xe7e: 0x4000,
	// Block 0x3a, offset 0xe80
	0xe80: 0x4000, 0xe81: 0x4000, 0xe82: 0x4000, 0xe83: 0x4000, 0xe84: 0x4000, 0xe85: 0x4000,
	0xe86: 0x4000, 0xe87: 0x4000, 0xe88: 0x4000, 0xe89: 0x4000, 0xe8a: 0x4000, 0xe8b: 0x4000,
	0xe8c: 0x4000, 0xe90: 0x4000, 0xe91: 0x4000,
	0xe92: 0x4000, 0xe93: 0x4000, 0xe94: 0x4000, 0xe95: 0x4000, 0xe96: 0x4000, 0xe97: 0x4000,
	0xe98: 0x4000, 0xe99: 0x4000, 0xe9a: 0x4000, 0xe9b: 0x4000, 0xe9c: 0x4000, 0xe9d: 0x4000,
	0xe9e: 0x4000, 0xe9f: 0x4000, 0xea0: 0x4000, 0xea1: 0x4000, 0xea2: 0x4000, 0xea3: 0x4000,
	0xea4: 0x4000, 0xea5: 0x4000, 0xea6: 0x4000, 0xea7: 0x4000, 0xea8: 0x4000, 0xea9: 0x4000,
	0xeaa: 0x4000, 0xeab: 0x4000, 0xeac: 0x4000, 0xead: 0x4000, 0xeae: 0x4000, 0xeaf: 0x4000,
	0xeb0: 0x4000, 0xeb1: 0x4000, 0xeb2: 0x4000, 0xeb3: 0x4000, 0xeb4: 0x4000, 0xeb5: 0x4000,
	0xeb6: 0x4000, 0xeb7: 0x4000, 0xeb8: 0x4000, 0xeb9: 0x4000, 0xeba: 0x4000, 0xebb: 0x4000,
	0xebc: 0x4000, 0xebd: 0x4000, 0xebe: 0x4000, 0xebf: 0x4000,
	// Block 0x3b, offset 0xec0
	0xec0: 0x4000, 0xec1: 0x4000, 0xec2: 0x4000, 0xec3: 0x4000, 0xec4: 0x4000, 0xec5: 0x4000,
	0xec6: 0x4000,
	// Block 0x3c, offset 0xf00
	0xf20: 0x4000, 0xf21: 0x4000, 0xf22: 0x4000, 0xf23: 0x4000,
	0xf24: 0x4000, 0xf25: 0x4000, 0xf26: 0x4000, 0xf27: 0x4000, 0xf28: 0x4000, 0xf29: 0x4000,
	0xf2a: 0x4000, 0xf2b: 0x4000, 0xf2c: 0x4000, 0xf2d: 0x4000, 0xf2e: 0x4000, 0xf2f: 0x4000,
	0xf30: 0x4000, 0xf31: 0x4000, 0xf32: 0x4000, 0xf33: 0x4000, 0xf34: 0x4000, 0xf35: 0x4000,
	0xf36: 0x4000, 0xf37: 0x4000, 0xf38: 0x4000, 0xf39: 0x4000, 0xf3a: 0x4000, 0xf3b: 0x4000,
	0xf3c: 0x4000,
	// Block 0x3d, offset 0xf40
	0xf40: 0x4000, 0xf41: 0x4000, 0xf42: 0x4000, 0xf43: 0x4000, 0xf44: 0x4000, 0xf45: 0x4000,
	0xf46: 0x4000, 0xf47: 0x4000, 0xf48: 0x4000, 0xf49: 0x4000, 0xf4a: 0x4000, 0xf4b: 0x4000,
	0xf4c: 0x4000, 0xf4d: 0x4000, 0xf4e: 0x4000, 0xf4f: 0x4000, 0xf50: 0x4000, 0xf51: 0x4000,
	0xf52: 0x4000, 0xf53: 0x4000, 0xf54: 0x4000, 0xf55: 0x4000, 0xf56: 0x4000, 0xf57: 0x4000,
	0xf58: 0x4000, 0xf59: 0x4000, 0xf5a: 0x4000, 0xf5b: 0x4000, 0xf5c: 0x4000, 0xf5d: 0x4000,
	0xf5e: 0x4000, 0xf5f: 0x4000, 0xf60: 0x4000, 0xf61: 0x4000, 0xf62: 0x4000, 0xf63: 0x4000,
	// Block 0x3e, offset 0xf80
	0xf80: 0x2000, 0xf81: 0x2000, 0xf82: 0x2000, 0xf83: 0x2000, 0xf84: 0x2000, 0xf85: 0x2000,
	0xf86: 0x2000, 0xf87: 0x2000, 0xf88: 0x2000, 0xf89: 0x2000, 0xf8a: 0x2000, 0xf8b: 0x2000,
	0xf8c: 0x2000, 0xf8d: 0x2000, 0xf8e: 0x2000, 0xf8f: 0x2000, 0xf90: 0x4000, 0xf91: 0x4000,
	0xf92: 0x4000, 0xf93: 0x4000, 0xf94: 0x4000, 0xf95: 0x4000, 0xf96: 0x4000, 0xf97: 0x4000,
	0xf98: 0x4000, 0xf99: 0x4000,
	0xfb0: 0x4000, 0xfb1: 0x4000, 0xfb2: 0x4000, 0xfb3: 0x4000, 0xfb4: 0x4000, 0xfb5: 0x4000,
	0xfb6: 0x4000, 0xfb7: 0x4000, 0xfb8: 0x4000, 0xfb9: 0x4000, 0xfba: 0x4000, 0xfbb: 0x4000,
	0xfbc: 0x4000, 0xfbd: 0x4000, 0xfbe: 0x4000, 0xfbf: 0x4000,
	// Block 0x3f, offset 0xfc0
	0xfc0: 0x4000, 0xfc1: 0x4000, 0xfc2: 0x4000, 0xfc3: 0x4000, 0xfc4: 0x4000, 0xfc5: 0x4000,
	0xfc6: 0x4000, 0xfc7: 0x4000, 0xfc8: 0x4000, 0xfc9: 0x4000, 0xfca: 0x4000, 0xfcb: 0x4000,
	0xfcc: 0x4000, 0xfcd: 0x4000, 0xfce: 0x4000, 0xfcf: 0x4000, 0xfd0: 0x4000, 0xfd1: 0x4000,
	0xfd2: 0x4000, 0xfd4: 0x4000, 0xfd5: 0x4000, 0xfd6: 0x4000, 0xfd7: 0x4000,
	0xfd8: 0x4000, 0xfd9: 0x4000, 0xfda: 0x4000, 0xfdb: 0x4000, 0xfdc: 0x4000, 0xfdd: 0x4000,
	0xfde: 0x4000, 0xfdf: 0x4000, 0xfe0: 0x4000, 0xfe1: 0x4000, 0xfe2: 0x4000, 0xfe3: 0x4000,
	0xfe4: 0x4000, 0xfe5: 0x4000, 0xfe6: 0x4000, 0xfe8: 0x4000, 0xfe9: 0x4000,
	0xfea: 0x4000, 0xfeb: 0x4000,
	// Block 0x40, offset 0x1000
	0x1001: 0x9012, 0x1002: 0x9012, 0x1003: 0x9012, 0x1004: 0x9012, 0x1005: 0x9012,
	0x1006: 0x9012, 0x1007: 0x9012, 0x1008: 0x9012, 0x1009: 0x9012, 0x100a: 0x9012, 0x100b: 0x9012,
	0x100c: 0x9012, 0x100d: 0x9012, 0x100e: 0x9012, 0x100f: 0x9012, 0x1010: 0x9012, 0x1011: 0x9012,
	0x1012: 0x9012, 0x1013: 0x9012, 0x1014: 0x9012, 0x1015: 0x9012, 0x1016: 0x9012, 0x1017: 0x9012,
	0x1018: 0x9012, 0x1019: 0x9012, 0x101a: 0x9012, 0x101b: 0x9012, 0x101c: 0x9012, 0x101d: 0x9012,
	0x101e: 0x9012, 0x101f: 0x9012, 0x1020: 0x9049, 0x1021: 0x9049, 0x1022: 0x9049, 0x1023: 0x9049,
	0x1024: 0x9049, 0x1025: 0x9049, 0x1026: 0x9049, 0x1027: 0x9049, 0x1028: 0x9049, 0x1029: 0x9049,
	0x102a: 0x9049, 0x102b: 0x9049, 0x102c: 0x9049, 0x102d: 0x9049, 0x102e: 0x9049, 0x102f: 0x9049,
	0x1030: 0x9049, 0x1031: 0x9049, 0x1032: 0x9049, 0x1033: 0x9049, 0x1034: 0x9049, 0x1035: 0x9049,
	0x1036: 0x9049, 0x1037: 0x9049, 0x1038: 0x9049, 0x1039: 0x9049, 0x103a: 0x9049, 0x103b: 0x9049,
	0x103c: 0x9049, 0x103d: 0x9049, 0x103e: 0x9049, 0x103f: 0x9049,
	// Block 0x41, offset 0x1040
	0x1040: 0x9049, 0x1041: 0x9049, 0x1042: 0x9049, 0x1043: 0x9049, 0x1044: 0x9049, 0x1045: 0x9049,
	0x1046: 0x9049, 0x1047: 0x9049, 0x1048: 0x9049, 0x1049: 0x9049, 0x104a: 0x9049, 0x104b: 0x9049,
	0x104c: 0x9049, 0x104d: 0x9049, 0x104e: 0x9049, 0x104f: 0x9049, 0x1050: 0x9049, 0x1051: 0x9049,
	0x1052: 0x9049, 0x1053: 0x9049, 0x1054: 0x9049, 0x1055: 0x9049, 0x1056: 0x9049, 0x1057: 0x9049,
	0x1058: 0x9049, 0x1059: 0x9049, 0x105a: 0x9049, 0x105b: 0x9049, 0x105c: 0x9049, 0x105d: 0x9049,
	0x105e: 0x9049, 0x105f: 0x904a, 0x1060: 0x904b, 0x1061: 0xb04c, 0x1062: 0xb04d, 0x1063: 0xb04d,
	0x1064: 0xb04e, 0x1065: 0xb04f, 0x1066: 0xb050, 0x1067: 0xb051, 0x1068: 0xb052, 0x1069: 0xb053,
	0x106a: 0xb054, 0x106b: 0xb055, 0x106c: 0xb056, 0x106d: 0xb057, 0x106e: 0xb058, 0x106f: 0xb059,
	0x1070: 0xb05a, 0x1071: 0xb05b, 0x1072: 0xb05c, 0x1073: 0xb05d, 0x1074: 0xb05e, 0x1075: 0xb05f,
	0x1076: 0xb060, 0x1077: 0xb061, 0x1078: 0xb062, 0x1079: 0xb063, 0x107a: 0xb064, 0x107b: 0xb065,
	0x107c: 0xb052, 0x107d: 0xb066, 0x107e: 0xb067, 0x107f: 0xb055,
	// Block 0x42, offset 0x1080
	0x1080: 0xb068, 0x1081: 0xb069, 0x1082: 0xb06a, 0x1083: 0xb06b, 0x1084: 0xb05a, 0x1085: 0xb056,
	0x1086: 0xb06c, 0x1087: 0xb06d, 0x1088: 0xb06b, 0x1089: 0xb06e, 0x108a: 0xb06b, 0x108b: 0xb06f,
	0x108c: 0xb06f, 0x108d: 0xb070, 0x108e: 0xb070, 0x108f: 0xb071, 0x1090: 0xb056, 0x1091: 0xb072,
	0x1092: 0xb073, 0x1093: 0xb072, 0x1094: 0xb074, 0x1095: 0xb073, 0x1096: 0xb075, 0x1097: 0xb075,
	0x1098: 0xb076, 0x1099: 0xb076, 0x109a: 0xb077, 0x109b: 0xb077, 0x109c: 0xb073, 0x109d: 0xb078,
	0x109e: 0xb079, 0x109f: 0xb067, 0x10a0: 0xb07a, 0x10a1: 0xb07b, 0x10a2: 0xb07b, 0x10a3: 0xb07b,
	0x10a4: 0xb07b, 0x10a5: 0xb07b, 0x10a6: 0xb07b, 0x10a7: 0xb07b, 0x10a8: 0xb07b, 0x10a9: 0xb07b,
	0x10aa: 0xb07b, 0x10ab: 0xb07b, 0x10ac: 0xb07b, 0x10ad: 0xb07b, 0x10ae: 0xb07b, 0x10af: 0xb07b,
	0x10b0: 0xb07c, 0x10b1: 0xb07c, 0x10b2: 0xb07c, 0x10b3: 0xb07c, 0x10b4: 0xb07c, 0x10b5: 0xb07c,
	0x10b6: 0xb07c, 0x10b7: 0xb07c, 0x10b8: 0xb07c, 0x10b9: 0xb07c, 0x10ba: 0xb07c, 0x10bb: 0xb07c,
	0x10bc: 0xb07c, 0x10bd: 0xb07c, 0x10be: 0xb07c,
	// Block 0x43, offset 0x10c0
	0x10c2: 0xb07d, 0x10c3: 0xb07e, 0x10c4: 0xb07f, 0x10c5: 0xb080,
	0x10c6: 0xb07f, 0x10c7: 0xb07e, 0x10ca: 0xb081, 0x10cb: 0xb082,
	0x10cc: 0xb083, 0x10cd: 0xb07f, 0x10ce: 0xb080, 0x10cf: 0xb07f,
	0x10d2: 0xb084, 0x10d3: 0xb085, 0x10d4: 0xb084, 0x10d5: 0xb086, 0x10d6: 0xb084, 0x10d7: 0xb087,
	0x10da: 0xb088, 0x10db: 0xb089, 0x10dc: 0xb08a,
	0x10e0: 0x908b, 0x10e1: 0x908b, 0x10e2: 0x908c, 0x10e3: 0x908d,
	0x10e4: 0x908b, 0x10e5: 0x908e, 0x10e6: 0x908f, 0x10e8: 0xb090, 0x10e9: 0xb091,
	0x10ea: 0xb092, 0x10eb: 0xb091, 0x10ec: 0xb093, 0x10ed: 0xb094, 0x10ee: 0xb095,
	0x10fd: 0x2000,
	// Block 0x44, offset 0x1100
	0x1100: 0x4000, 0x1101: 0x4000,
	// Block 0x45, offset 0x1140
	0x1140: 0x2000, 0x1141: 0x2000, 0x1142: 0x2000, 0x1143: 0x2000, 0x1144: 0x2000, 0x1145: 0x2000,
	0x1146: 0x2000, 0x1147: 0x2000, 0x1148: 0x2000, 0x1149: 0x2000, 0x114a: 0x2000,
	0x1150: 0x2000, 0x1151: 0x2000,
	0x1152: 0x2000, 0x1153: 0x2000, 0x1154: 0x2000, 0x1155: 0x2000, 0x1156: 0x2000, 0x1157: 0x2000,
	0x1158: 0x2000, 0x1159: 0x2000, 0x115a: 0x2000, 0x115b: 0x2000, 0x115c: 0x2000, 0x115d: 0x2000,
	0x115e: 0x2000, 0x115f: 0x2000, 0x1160: 0x2000, 0x1161: 0x2000, 0x1162: 0x2000, 0x1163: 0x2000,
	0x1164: 0x2000, 0x1165: 0x2000, 0x1166: 0x2000, 0x1167: 0x2000, 0x1168: 0x2000, 0x1169: 0x2000,
	0x116a: 0x2000, 0x116b: 0x2000, 0x116c: 0x2000, 0x116d: 0x2000,
	0x1170: 0x2000, 0x1171: 0x2000, 0x1172: 0x2000, 0x1173: 0x2000, 0x1174: 0x2000, 0x1175: 0x2000,
	0x1176: 0x2000, 0x1177: 0x2000, 0x1178: 0x2000, 0x1179: 0x2000, 0x117a: 0x2000, 0x117b: 0x2000,
	0x117c: 0x2000, 0x117d: 0x2000, 0x117e: 0x2000, 0x117f: 0x2000,
	// Block 0x46, offset 0x1180
	0x1180: 0x2000, 0x1181: 0x2000, 0x1182: 0x2000, 0x1183: 0x2000, 0x1184: 0x2000, 0x1185: 0x2000,
	0x1186: 0x2000, 0x1187: 0x2000, 0x1188: 0x2000, 0x1189: 0x2000, 0x118a: 0x2000, 0x118b: 0x2000,
	0x118c: 0x2000, 0x118d: 0x2000, 0x118e: 0x2000, 0x118f: 0x2000, 0x1190: 0x2000, 0x1191: 0x2000,
	0x1192: 0x2000, 0x1193: 0x2000, 0x1194: 0x2000, 0x1195: 0x2000, 0x1196: 0x2000, 0x1197: 0x2000,
	0x1198: 0x2000, 0x1199: 0x2000, 0x119a: 0x2000, 0x119b: 0x2000, 0x119c: 0x2000, 0x119d: 0x2000,
	0x119e: 0x2000, 0x119f: 0x2000, 0x11a0: 0x2000, 0x11a1: 0x2000, 0x11a2: 0x2000, 0x11a3: 0x2000,
	0x11a4: 0x2000, 0x11a5: 0x2000, 0x11a6: 0x2000, 0x11a7: 0x2000, 0x11a8: 0x2000, 0x11a9: 0x2000,
	0x11b0: 0x2000, 0x11b1: 0x2000, 0x11b2: 0x2000, 0x11b3: 0x2000, 0x11b4: 0x2000, 0x11b5: 0x2000,
	0x11b6: 0x2000, 0x11b7: 0x2000, 0x11b8: 0x2000, 0x11b9: 0x2000, 0x11ba: 0x2000, 0x11bb: 0x2000,
	0x11bc: 0x2000, 0x11bd: 0x2000, 0x11be: 0x2000, 0x11bf: 0x2000,
	// Block 0x47, offset 0x11c0
	0x11c0: 0x2000, 0x11c1: 0x2000, 0x11c2: 0x2000, 0x11c3: 0x2000, 0x11c4: 0x2000, 0x11c5: 0x2000,
	0x11c6: 0x2000, 0x11c7: 0x2000, 0x11c8: 0x2000, 0x11c9: 0x2000, 0x11ca: 0x2000, 0x11cb: 0x2000,
	0x11cc: 0x2000, 0x11cd: 0x2000, 0x11ce: 0x2000, 0x11cf: 0x2000, 0x11d0: 0x2000, 0x11d1: 0x2000,
	0x11d2: 0x2000, 0x11d3: 0x2000, 0x11d4: 0x2000, 0x11d5: 0x2000, 0x11d6: 0x2000, 0x11d7: 0x2000,
	0x11d8: 0x2000, 0x11d9: 0x2000, 0x11da: 0x2000,
	// Block 0x48, offset 0x1200
	0x1200: 0x4000, 0x1201: 0x4000, 0x1202: 0x4000,
	0x1210: 0x4000, 0x1211: 0x4000,
	0x1212: 0x4000, 0x1213: 0x4000, 0x1214: 0x4000, 0x1215: 0x4000, 0x1216: 0x4000, 0x1217: 0x4000,
	0x1218: 0x4000, 0x1219: 0x4000, 0x121a: 0x4000, 0x121b: 0x4000, 0x121c: 0x4000, 0x121d: 0x4000,
	0x121e: 0x4000, 0x121f: 0x4000, 0x1220: 0x4000, 0x1221: 0x4000, 0x1222: 0x4000, 0x1223: 0x4000,
	0x1224: 0x4000, 0x1225: 0x4000, 0x1226: 0x4000, 0x1227: 0x4000, 0x1228: 0x4000, 0x1229: 0x4000,
	0x122a: 0x4000, 0x122b: 0x4000, 0x122c: 0x4000, 0x122d: 0x4000, 0x122e: 0x4000, 0x122f: 0x4000,
	0x1230: 0x4000, 0x1231: 0x4000, 0x1232: 0x4000, 0x1233: 0x4000, 0x1234: 0x4000, 0x1235: 0x4000,
	0x1236: 0x4000, 0x1237: 0x4000, 0x1238: 0x4000, 0x1239: 0x4000, 0x123a: 0x4000,
	// Block 0x49, offset 0x1240
	0x1240: 0x4000, 0x1241: 0x4000, 0x1242: 0x4000, 0x1243: 0x4000, 0x1244: 0x4000, 0x1245: 0x4000,
	0x1246: 0x4000, 0x1247: 0x4000, 0x1248: 0x4000,
	0x1250: 0x4000, 0x1251: 0x4000,
	// Block 0x4a, offset 0x1280
	0x1280: 0x2000, 0x1281: 0x2000, 0x1282: 0x2000, 0x1283: 0x2000, 0x1284: 0x2000, 0x1285: 0x2000,
	0x1286: 0x2000, 0x1287: 0x2000, 0x1288: 0x2000, 0x1289: 0x2000, 0x128a: 0x2000, 0x128b: 0x2000,
	0x128c: 0x2000, 0x128d: 0x2000, 0x128e: 0x2000, 0x128f: 0x2000, 0x1290: 0x2000, 0x1291: 0x2000,
	0x1292: 0x2000, 0x1293: 0x2000, 0x1294: 0x2000, 0x1295: 0x2000, 0x1296: 0x2000, 0x1297: 0x2000,
	0x1298: 0x2000, 0x1299: 0x2000, 0x129a: 0x2000, 0x129b: 0x2000, 0x129c: 0x2000, 0x129d: 0x2000,
	0x129e: 0x2000, 0x129f: 0x2000, 0x12a0: 0x2000, 0x12a1: 0x2000, 0x12a2: 0x2000, 0x12a3: 0x2000,
	0x12a4: 0x2000, 0x12a5: 0x2000, 0x12a6: 0x2000, 0x12a7: 0x2000, 0x12a8: 0x2000, 0x12a9: 0x2000,
	0x12aa: 0x2000, 0x12ab: 0x2000, 0x12ac: 0x2000, 0x12ad: 0x2000, 0x12ae: 0x2000, 0x12af: 0x2000,
	0x12b0: 0x2000, 0x12b1: 0x2000, 0x12b2: 0x2000, 0x12b3: 0x2000, 0x12b4: 0x2000, 0x12b5: 0x2000,
	0x12b6: 0x2000, 0x12b7: 0x2000, 0x12b8: 0x2000, 0x12b9: 0x2000, 0x12ba: 0x2000, 0x12bb: 0x2000,
	0x12bc: 0x2000, 0x12bd: 0x2000,
}

// widthIndex: 20 blocks, 1280 entries, 1280 bytes
// Block 0 is the zero block.
var widthIndex = [1280]uint8{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x01, 0xc3: 0x02, 0xc4: 0x03, 0xc5: 0x04, 0xc7: 0x05,
	0xc9: 0x06, 0xcb: 0x07, 0xcc: 0x08, 0xcd: 0x09, 0xce: 0x0a, 0xcf: 0x0b,
	0xd0: 0x0c, 0xd1: 0x0d,
	0xe1: 0x02, 0xe2: 0x03, 0xe3: 0x04, 0xe4: 0x05, 0xe5: 0x06, 0xe6: 0x06, 0xe7: 0x06,
	0xe8: 0x06, 0xe9: 0x06, 0xea: 0x07, 0xeb: 0x06, 0xec: 0x06, 0xed: 0x08, 0xee: 0x09, 0xef: 0x0a,
	0xf0: 0x0d, 0xf3: 0x10, 0xf4: 0x11,
	// Block 0x4, offset 0x100
	0x104: 0x0e, 0x105: 0x0f,
	// Block 0x5, offset 0x140
	0x140: 0x10, 0x141: 0x11, 0x142: 0x12, 0x144: 0x13, 0x145: 0x14, 0x146: 0x15, 0x147: 0x16,
	0x148: 0x17, 0x149: 0x18, 0x14a: 0x19, 0x14c: 0x1a,
	0x151: 0x1b, 0x152: 0x08, 0x153: 0x1c, 0x154: 0x1d, 0x155: 0x1e, 0x156: 0x1f, 0x157: 0x20,
	0x158: 0x21, 0x159: 0x22, 0x15a: 0x23, 0x15b: 0x24, 0x15c: 0x25, 0x15d: 0x26, 0x15f: 0x27,
	0x166: 0x28,
	0x16d: 0x29,
	0x17a: 0x2a, 0x17b: 0x2b, 0x17c: 0x0e, 0x17d: 0x0e, 0x17e: 0x0e, 0x17f: 0x2c,
	// Block 0x6, offset 0x180
	0x180: 0x2d, 0x181: 0x2e, 0x182: 0x2f, 0x183: 0x30, 0x184: 0x31, 0x185: 0x32, 0x186: 0x33, 0x187: 0x34,
	0x188: 0x35, 0x189: 0x36, 0x18a: 0x0e, 0x18b: 0x37, 0x18c: 0x0e, 0x18d: 0x0e, 0x18e: 0x0e, 0x18f: 0x0e,
	0x190: 0x0e, 0x191: 0x0e, 0x192: 0x0e, 0x193: 0x0e, 0x194: 0x0e, 0x195: 0x0e, 0x196: 0x0e, 0x197: 0x0e,
	0x198: 0x0e, 0x199: 0x0e, 0x19a: 0x0e, 0x19b: 0x0e, 0x19c: 0x0e, 0x19d: 0x0e, 0x19e: 0x0e, 0x19f: 0x0e,
	0x1a0: 0x0e, 0x1a1: 0x0e, 0x1a2: 0x0e, 0x1a3: 0x0e, 0x1a4: 0x0e, 0x1a5: 0x0e, 0x1a6: 0x0e, 0x1a7: 0x0e,
	0x1a8: 0x0e, 0x1a9: 0x0e, 0x1aa: 0x0e, 0x1ab: 0x0e, 0x1ac: 0x0e, 0x1ad: 0x0e, 0x1ae: 0x0e, 0x1af: 0x0e,
	0x1b0: 0x0e, 0x1b1: 0x0e, 0x1b2: 0x0e, 0x1b3: 0x0e, 0x1b4: 0x0e, 0x1b5: 0x0e, 0x1b6: 0x0e, 0x1b7: 0x0e,
	0x1b8: 0x0e, 0x1b9: 0x0e, 0x1ba: 0x0e, 0x1bb: 0x0e, 0x1bc: 0x0e, 0x1bd: 0x0e, 0x1be: 0x0e, 0x1bf: 0x0e,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x0e, 0x1c1: 0x0e, 0x1c2: 0x0e, 0x1c3: 0x0e, 0x1c4: 0x0e, 0x1c5: 0x0e, 0x1c6: 0x0e, 0x1c7: 0x0e,
	0x1c8: 0x0e, 0x1c9: 0x0e, 0x1ca: 0x0e, 0x1cb: 0x0e, 0x1cc: 0x0e, 0x1cd: 0x0e, 0x1ce: 0x0e, 0x1cf: 0x0e,
	0x1d0: 0x0e, 0x1d1: 0x0e, 0x1d2: 0x0e, 0x1d3: 0x0e, 0x1d4: 0x0e, 0x1d5: 0x0e, 0x1d6: 0x0e, 0x1d7: 0x0e,
	0x1d8: 0x0e, 0x1d9: 0x0e, 0x1da: 0x0e, 0x1db: 0x0e, 0x1dc: 0x0e, 0x1dd: 0x0e, 0x1de: 0x0e, 0x1df: 0x0e,
	0x1e0: 0x0e, 0x1e1: 0x0e, 0x1e2: 0x0e, 0x1e3: 0x0e, 0x1e4: 0x0e, 0x1e5: 0x0e, 0x1e6: 0x0e, 0x1e7: 0x0e,
	0x1e8: 0x0e, 0x1e9: 0x0e, 0x1ea: 0x0e, 0x1eb: 0x0e, 0x1ec: 0x0e, 0x1ed: 0x0e, 0x1ee: 0x0e, 0x1ef: 0x0e,
	0x1f0: 0x0e, 0x1f1: 0x0e, 0x1f2: 0x0e, 0x1f3: 0x0e, 0x1f4: 0x0e, 0x1f5: 0x0e, 0x1f6: 0x0e,
	0x1f8: 0x0e, 0x1f9: 0x0e, 0x1fa: 0x0e, 0x1fb: 0x0e, 0x1fc: 0x0e, 0x1fd: 0x0e, 0x1fe: 0x0e, 0x1ff: 0x0e,
	// Block 0x8, offset 0x200
	0x200: 0x0e, 0x201: 0x0e, 0x202: 0x0e, 0x203: 0x0e, 0x204: 0x0e, 0x205: 0x0e, 0x206: 0x0e, 0x207: 0x0e,
	0x208: 0x0e, 0x209: 0x0e, 0x20a: 0x0e, 0x20b: 0x0e, 0x20c: 0x0e, 0x20d: 0x0e, 0x20e: 0x0e, 0x20f: 0x0e,
	0x210: 0x0e, 0x211: 0x0e, 0x212: 0x0e, 0x213: 0x0e, 0x214: 0x0e, 0x215: 0x0e, 0x216: 0x0e, 0x217: 0x0e,
	0x218: 0x0e, 0x219: 0x0e, 0x21a: 0x0e, 0x21b: 0x0e, 0x21c: 0x0e, 0x21d: 0x0e, 0x21e: 0x0e, 0x21f: 0x0e,
	0x220: 0x0e, 0x221: 0x0e, 0x222: 0x0e, 0x223: 0x0e, 0x224: 0x0e, 0x225: 0x0e, 0x226: 0x0e, 0x227: 0x0e,
	0x228: 0x0e, 0x229: 0x0e, 0x22a: 0x0e, 0x22b: 0x0e, 0x22c: 0x0e, 0x22d: 0x0e, 0x22e: 0x0e, 0x22f: 0x0e,
	0x230: 0x0e, 0x231: 0x0e, 0x232: 0x0e, 0x233: 0x0e, 0x234: 0x0e, 0x235: 0x0e, 0x236: 0x0e, 0x237: 0x0e,
	0x238: 0x0e, 0x239: 0x0e, 0x23a: 0x0e, 0x23b: 0x0e, 0x23c: 0x0e, 0x23d: 0x0e, 0x23e: 0x0e, 0x23f: 0x0e,
	// Block 0x9, offset 0x240
	0x240: 0x0e, 0x241: 0x0e, 0x242: 0x0e, 0x243: 0x0e, 0x244: 0x0e, 0x245: 0x0e, 0x246: 0x0e, 0x247: 0x0e,
	0x248: 0x0e, 0x249: 0x0e, 0x24a: 0x0e, 0x24b: 0x0e, 0x24c: 0x0e, 0x24d: 0x0e, 0x24e: 0x0e, 0x24f: 0x0e,
	0x250: 0x0e, 0x251: 0x0e, 0x252: 0x38, 0x253: 0x39,
	0x265: 0x3a,
	0x270: 0x0e, 0x271: 0x0e, 0x272: 0x0e, 0x273: 0x0e, 0x274: 0x0e, 0x275: 0x0e, 0x276: 0x0e, 0x277: 0x0e,
	0x278: 0x0e, 0x279: 0x0e, 0x27a: 0x0e, 0x27b: 0x0e, 0x27c: 0x0e, 0x27d: 0x0e, 0x27e: 0x0e, 0x27f: 0x0e,
	// Block 0xa, offset 0x280
	0x280: 0x0e, 0x281: 0x0e, 0x282: 0x0e, 0x283: 0x0e, 0x284: 0x0e, 0x285: 0x0e, 0x286: 0x0e, 0x287: 0x0e,
	0x288: 0x0e, 0x289: 0x0e, 0x28a: 0x0e, 0x28b: 0x0e, 0x28c: 0x0e, 0x28d: 0x0e, 0x28e: 0x0e, 0x28f: 0x0e,
	0x290: 0x0e, 0x291: 0x0e, 0x292: 0x0e, 0x293: 0x0e, 0x294: 0x0e, 0x295: 0x0e, 0x296: 0x0e, 0x297: 0x0e,
	0x298: 0x0e, 0x299: 0x0e, 0x29a: 0x0e, 0x29b: 0x0e, 0x29c: 0x0e, 0x29d: 0x0e, 0x29e: 0x3b,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x08, 0x2c1: 0x08, 0x2c2: 0x08, 0x2c3: 0x08, 0x2c4: 0x08, 0x2c5: 0x08, 0x2c6: 0x08, 0x2c7: 0x08,
	0x2c8: 0x08, 0x2c9: 0x08, 0x2ca: 0x08, 0x2cb: 0x08, 0x2cc: 0x08, 0x2cd: 0x08, 0x2ce: 0x08, 0x2cf: 0x08,
	0x2d0: 0x08, 0x2d1: 0x08, 0x2d2: 0x08, 0x2d3: 0x08, 0x2d4: 0x08, 0x2d5: 0x08, 0x2d6: 0x08, 0x2d7: 0x08,
	0x2d8: 0x08, 0x2d9: 0x08, 0x2da: 0x08, 0x2db: 0x08, 0x2dc: 0x08, 0x2dd: 0x08, 0x2de: 0x08, 0x2df: 0x08,
	0x2e0: 0x08, 0x2e1: 0x08, 0x2e2: 0x08, 0x2e3: 0x08, 0x2e4: 0x08, 0x2e5: 0x08, 0x2e6: 0x08, 0x2e7: 0x08,
	0x2e8: 0x08, 0x2e9: 0x08, 0x2ea: 0x08, 0x2eb: 0x08, 0x2ec: 0x08, 0x2ed: 0x08, 0x2ee: 0x08, 0x2ef: 0x08,
	0x2f0: 0x08, 0x2f1: 0x08, 0x2f2: 0x08, 0x2f3: 0x08, 0x2f4: 0x08, 0x2f5: 0x08, 0x2f6: 0x08, 0x2f7: 0x08,
	0x2f8: 0x08, 0x2f9: 0x08, 0x2fa: 0x08, 0x2fb: 0x08, 0x2fc: 0x08, 0x2fd: 0x08, 0x2fe: 0x08, 0x2ff: 0x08,
	// Block 0xc, offset 0x300
	0x300: 0x08, 0x301: 0x08, 0x302: 0x08, 0x303: 0x08, 0x304: 0x08, 0x305: 0x08, 0x306: 0x08, 0x307: 0x08,
	0x308: 0x08, 0x309: 0x08, 0x30a: 0x08, 0x30b: 0x08, 0x30c: 0x08, 0x30d: 0x08, 0x30e: 0x08, 0x30f: 0x08,
	0x310: 0x08, 0x311: 0x08, 0x312: 0x08, 0x313: 0x08, 0x314: 0x08, 0x315: 0x08, 0x316: 0x08, 0x317: 0x08,
	0x318: 0x08, 0x319: 0x08, 0x31a: 0x08, 0x31b: 0x08, 0x31c: 0x08, 0x31d: 0x08, 0x31e: 0x08, 0x31f: 0x08,
	0x320: 0x08, 0x321: 0x08, 0x322: 0x08, 0x323: 0x08, 0x324: 0x0e, 0x325: 0x0e, 0x326: 0x0e, 0x327: 0x0e,
	0x328: 0x0e, 0x329: 0x0e, 0x32a: 0x0e, 0x32b: 0x0e,
	0x338: 0x3c, 0x339: 0x3d, 0x33c: 0x3e, 0x33d: 0x3f, 0x33e: 0x40, 0x33f: 0x41,
	// Block 0xd, offset 0x340
	0x340: 0x42,
	// Block 0xe, offset 0x380
	0x384: 0x43, 0x385: 0x44, 0x386: 0x45,
	0x388: 0x46, 0x389: 0x47,
	// Block 0xf, offset 0x3c0
	0x3db: 0x0b, 0x3df: 0x0c,
	0x3e0: 0x06, 0x3e1: 0x06, 0x3e2: 0x06, 0x3e3: 0x06, 0x3e4: 0x06, 0x3e5: 0x06, 0x3e6: 0x06, 0x3e7: 0x06,
	0x3e8: 0x06, 0x3e9: 0x06, 0x3ea: 0x06, 0x3eb: 0x06, 0x3ec: 0x06, 0x3ed: 0x06, 0x3ee: 0x06, 0x3ef: 0x06,
	0x3f0: 0x06, 0x3f1: 0x06, 0x3f2: 0x06, 0x3f3: 0x06, 0x3f4: 0x06, 0x3f5: 0x06, 0x3f6: 0x06, 0x3f7: 0x06,
	0x3f8: 0x06, 0x3f9: 0x06, 0x3fa: 0x06, 0x3fb: 0x06, 0x3fc: 0x06, 0x3fd: 0x06, 0x3fe: 0x06, 0x3ff: 0x06,
	// Block 0x10, offset 0x400
	0x404: 0x08, 0x405: 0x08, 0x406: 0x08, 0x407: 0x09,
	// Block 0x11, offset 0x440
	0x440: 0x08, 0x441: 0x08, 0x442: 0x08, 0x443: 0x08, 0x444: 0x08, 0x445: 0x08, 0x446: 0x08, 0x447: 0x08,
	0x448: 0x08, 0x449: 0x08, 0x44a: 0x08, 0x44b: 0x08, 0x44c: 0x08, 0x44d: 0x08, 0x44e: 0x08, 0x44f: 0x08,
	0x450: 0x08, 0x451: 0x08, 0x452: 0x08, 0x453: 0x08, 0x454: 0x08, 0x455: 0x08, 0x456: 0x08, 0x457: 0x08,
	0x458: 0x08, 0x459: 0x08, 0x45a: 0x08, 0x45b: 0x08, 0x45c: 0x08, 0x45d: 0x08, 0x45e: 0x08, 0x45f: 0x08,
	0x460: 0x08, 0x461: 0x08, 0x462: 0x08, 0x463: 0x08, 0x464: 0x08, 0x465: 0x08, 0x466: 0x08, 0x467: 0x08,
	0x468: 0x08, 0x469: 0x08, 0x46a: 0x08, 0x46b: 0x08, 0x46c: 0x08, 0x46d: 0x08, 0x46e: 0x08, 0x46f: 0x08,
	0x470: 0x08, 0x471: 0x08, 0x472: 0x08, 0x473: 0x08, 0x474: 0x08, 0x475: 0x08, 0x476: 0x08, 0x477: 0x08,
	0x478: 0x08, 0x479: 0x08, 0x47a: 0x08, 0x47b: 0x08, 0x47c: 0x08, 0x47d: 0x08, 0x47e: 0x08, 0x47f: 0x48,
	// Block 0x12, offset 0x480
	0x4a0: 0x0e,
	0x4b0: 0x09, 0x4b1: 0x09, 0x4b2: 0x09, 0x4b3: 0x09, 0x4b4: 0x09, 0x4b5: 0x09, 0x4b6: 0x09, 0x4b7: 0x09,
	0x4b8: 0x09, 0x4b9: 0x09, 0x4ba: 0x09, 0x4bb: 0x09, 0x4bc: 0x09, 0x4bd: 0x09, 0x4be: 0x09, 0x4bf: 0x0f,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x09, 0x4c1: 0x09, 0x4c2: 0x09, 0x4c3: 0x09, 0x4c4: 0x09, 0x4c5: 0x09, 0x4c6: 0x09, 0x4c7: 0x09,
	0x4c8: 0x09, 0x4c9: 0x09, 0x4ca: 0x09, 0x4cb: 0x09, 0x4cc: 0x09, 0x4cd: 0x09, 0x4ce: 0x09, 0x4cf: 0x0f,
}

// inverseData contains 4-byte entries of the following format:
//   <length> <modified UTF-8-encoded rune> <0 padding>
// The last byte of the UTF-8-encoded rune is xor-ed with the last byte of the
// UTF-8 encoding of the original rune. Mappings often have the following
// pattern:
//   Ａ -> A  (U+FF21 -> U+0041)
//   Ｂ -> B  (U+FF22 -> U+0042)
//   ...
// By xor-ing the last byte the same entry can be shared by many mappings. This
// reduces the total number of distinct entries by about two thirds.
// The resulting entry for the aforementioned mappings is
//   { 0x01, 0xE0, 0x00, 0x00 }
// Using this entry to map U+FF21 (UTF-8 [EF BC A1]), we get
//   E0 ^ A1 = 41.
// Similarly, for U+FF22 (UTF-8 [EF BC A2]), we get
//   E0 ^ A2 = 42.
// Note that because of the xor-ing, the byte sequence stored in the entry is
// not valid UTF-8.
var inverseData = [150][4]byte{
	{0x00, 0x00, 0x00, 0x00},
	{0x03, 0xe3, 0x80, 0xa0},
	{0x03, 0xef, 0xbc, 0xa0},
	{0x03, 0xef, 0xbc, 0xe0},
	{0x03, 0xef, 0xbd, 0xe0},
	{0x03, 0xef, 0xbf, 0x02},
	{0x03, 0xef, 0xbf, 0x00},
	{0x03, 0xef, 0xbf, 0x0e},
	{0x03, 0xef, 0xbf, 0x0c},
	{0x03, 0xef, 0xbf, 0x0f},
	{0x03, 0xef, 0xbf, 0x39},
	{0x03, 0xef, 0xbf, 0x3b},
	{0x03, 0xef, 0xbf, 0x3f},
	{0x03, 0xef, 0xbf, 0x2a},
	{0x03, 0xef, 0xbf, 0x0d},
	{0x03, 0xef, 0xbf, 0x25},
	{0x03, 0xef, 0xbd, 0x1a},
	{0x03, 0xef, 0xbd, 0x26},
	{0x01, 0xa0, 0x00, 0x00},
	{0x03, 0xef, 0xbd, 0x25},
	{0x03, 0xef, 0xbd, 0x23},
	{0x03, 0xef, 0xbd, 0x2e},
	{0x03, 0xef, 0xbe, 0x07},
	{0x03, 0xef, 0xbe, 0x05},
	{0x03, 0xef, 0xbd, 0x06},
	{0x03, 0xef, 0xbd, 0x13},
	{0x03, 0xef, 0xbd, 0x0b},
	{0x03, 0xef, 0xbd, 0x16},
	{0x03, 0xef, 0xbd, 0x0c},
	{0x03, 0xef, 0xbd, 0x15},
	{0x03, 0xef, 0xbd, 0x0d},
	{0x03, 0xef, 0xbd, 0x1c},
	{0x03, 0xef, 0xbd, 0x02},
	{0x03, 0xef, 0xbd, 0x1f},
	{0x03, 0xef, 0xbd, 0x1d},
	{0x03, 0xef, 0xbd, 0x17},
	{0x03, 0xef, 0xbd, 0x08},
	{0x03, 0xef, 0xbd, 0x09},
	{0x03, 0xef, 0xbd, 0x0e},
	{0x03, 0xef, 0xbd, 0x04},
	{0x03, 0xef, 0xbd, 0x05},
	{0x03, 0xef, 0xbe, 0x3f},
	{0x03, 0xef, 0xbe, 0x00},
	{0x03, 0xef, 0xbd, 0x2c},
	{0x03, 0xef, 0xbe, 0x06},
	{0x03, 0xef, 0xbe, 0x0c},
	{0x03, 0xef, 0xbe, 0x0f},
	{0x03, 0xef, 0xbe, 0x0d},
	{0x03, 0xef, 0xbe, 0x0b},
	{0x03, 0xef, 0xbe, 0x19},
	{0x03, 0xef, 0xbe, 0x15},
	{0x03, 0xef, 0xbe, 0x11},
	{0x03, 0xef, 0xbe, 0x31},
	{0x03, 0xef, 0xbe, 0x33},
	{0x03, 0xef, 0xbd, 0x0f},
	{0x03, 0xef, 0xbe, 0x30},
	{0x03, 0xef, 0xbe, 0x3e},
	{0x03, 0xef, 0xbe, 0x32},
	{0x03, 0xef, 0xbe, 0x36},
	{0x03, 0xef, 0xbd, 0x14},
	{0x03, 0xef, 0xbe, 0x2e},
	{0x03, 0xef, 0xbd, 0x1e},
	{0x03, 0xef, 0xbe, 0x10},
	{0x03, 0xef, 0xbf, 0x13},
	{0x03, 0xef, 0xbf, 0x15},
	{0x03, 0xef, 0xbf, 0x17},
	{0x03, 0xef, 0xbf, 0x1f},
	{0x03, 0xef, 0xbf, 0x1d},
	{0x03, 0xef, 0xbf, 0x1b},
	{0x03, 0xef, 0xbf, 0x09},
	{0x03, 0xef, 0xbf, 0x0b},
	{0x03, 0xef, 0xbf, 0x37},
	{0x03, 0xef, 0xbe, 0x04},
	{0x01, 0xe0, 0x00, 0x00},
	{0x03, 0xe2, 0xa6, 0x1a},
	{0x03, 0xe2, 0xa6, 0x26},
	{0x03, 0xe3, 0x80, 0x23},
	{0x03, 0xe3, 0x80, 0x2e},
	{0x03, 0xe3, 0x80, 0x25},
	{0x03, 0xe3, 0x83, 0x1e},
	{0x03, 0xe3, 0x83, 0x14},
	{0x03, 0xe3, 0x82, 0x06},
	{0x03, 0xe3, 0x82, 0x0b},
	{0x03, 0xe3, 0x82, 0x0c},
	{0x03, 0xe3, 0x82, 0x0d},
	{0x03, 0xe3, 0x82, 0x02},
	{0x03, 0xe3, 0x83, 0x0f},
	{0x03, 0xe3, 0x83, 0x08},
	{0x03, 0xe3, 0x83, 0x09},
	{0x03, 0xe3, 0x83, 0x2c},
	{0x03, 0xe3, 0x83, 0x0c},
	{0x03, 0xe3, 0x82, 0x13},
	{0x03, 0xe3, 0x82, 0x16},
	{0x03, 0xe3, 0x82, 0x15},
	{0x03, 0xe3, 0x82, 0x1c},
	{0x03, 0xe3, 0x82, 0x1f},
	{0x03, 0xe3, 0x82, 0x1d},
	{0x03, 0xe3, 0x82, 0x1a},
	{0x03, 0xe3, 0x82, 0x17},
	{0x03, 0xe3, 0x82, 0x08},
	{0x03, 0xe3, 0x82, 0x09},
	{0x03, 0xe3, 0x82, 0x0e},
	{0x03, 0xe3, 0x82, 0x04},
	{0x03, 0xe3, 0x82, 0x05},
	{0x03, 0xe3, 0x82, 0x3f},
	{0x03, 0xe3, 0x83, 0x00},
	{0x03, 0xe3, 0x83, 0x06},
	{0x03, 0xe3, 0x83, 0x05},
	{0x03, 0xe3, 0x83, 0x0d},
	{0x03, 0xe3, 0x83, 0x0b},
	{0x03, 0xe3, 0x83, 0x07},
	{0x03, 0xe3, 0x83, 0x19},
	{0x03, 0xe3, 0x83, 0x15},
	{0x03, 0xe3, 0x83, 0x11},
	{0x03, 0xe3, 0x83, 0x31},
	{0x03, 0xe3, 0x83, 0x33},
	{0x03, 0xe3, 0x83, 0x30},
	{0x03, 0xe3, 0x83, 0x3e},
	{0x03, 0xe3, 0x83, 0x32},
	{0x03, 0xe3, 0x83, 0x36},
	{0x03, 0xe3, 0x83, 0x2e},
	{0x03, 0xe3, 0x82, 0x07},
	{0x03, 0xe3, 0x85, 0x04},
	{0x03, 0xe3, 0x84, 0x10},
	{0x03, 0xe3, 0x85, 0x30},
	{0x03, 0xe3, 0x85, 0x0d},
	{0x03, 0xe3, 0x85, 0x13},
	{0x03, 0xe3, 0x85, 0x15},
	{0x03, 0xe3, 0x85, 0x17},
	{0x03, 0xe3, 0x85, 0x1f},
	{0x03, 0xe3, 0x85, 0x1d},
	{0x03, 0xe3, 0x85, 0x1b},
	{0x03, 0xe3, 0x85, 0x09},
	{0x03, 0xe3, 0x85, 0x0f},
	{0x03, 0xe3, 0x85, 0x0b},
	{0x03, 0xe3, 0x85, 0x37},
	{0x03, 0xe3, 0x85, 0x3b},
	{0x03, 0xe3, 0x85, 0x39},
	{0x03, 0xe3, 0x85, 0x3f},
	{0x02, 0xc2, 0x02, 0x00},
	{0x02, 0xc2, 0x0e, 0x00},
	{0x02, 0xc2, 0x0c, 0x00},
	{0x02, 0xc2, 0x00, 0x00},
	{0x03, 0xe2, 0x82, 0x0f},
	{0x03, 0xe2, 0x94, 0x2a},
	{0x03, 0xe2, 0x86, 0x39},
	{0x03, 0xe2, 0x86, 0x3b},
	{0x03, 0xe2, 0x86, 0x3f},
	{0x03, 0xe2, 0x96, 0x0d},
	{0x03, 0xe2, 0x97, 0x25},
}

// Total table size 11480 bytes (11KiB)
