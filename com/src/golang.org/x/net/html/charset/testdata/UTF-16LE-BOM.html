<!DOCTYPE html>
<html  lang="en" >
<head>
<title>UTF-16LE BOM</title>
<link rel='author' title='<PERSON>' href='mailto:<EMAIL>'>
<link rel='help' href='http://www.w3.org/TR/html5/syntax.html#the-input-byte-stream'>
<script src="http://w3c-test.org/resources/testharness.js"></script>
<script src="http://w3c-test.org/resources/testharnessreport.js"></script>
<meta name='flags' content='http'>
<style type='text/css'>
.test div { width: 50px; }
</style>

<link rel="stylesheet" type="text/css" href="encodingtests-15.css">
</head>
<body>

<div class='test'><div id='box' class='ÃœÃ€Ãš'>&#xA0;</div></div>

<!-- Notes: 
No encoding information is declared in the HTTP header or inside the document, other than in the BOM. The text of a class name in the test contains the following sequence of bytes: 0xC3 0xc0 0x53 0xc1 0xC3 0xc0 0xAC 0xc20 0xC3 0xc0 0x61 0xc1. The external, UTF-8-encoded stylesheet contains a selector with a sequence of characters that will only match the class name in the HTML if the page is read as UTF-16BE.
-->

<script> 
test(function () {
    assert_equals(document.getElementById('box').offsetWidth, 100);
	}, 'A page with no encoding declarations, but with a UTF-16 little-endian BOM will be recognized as UTF-16.');
</script>

<div id="log"></div>

</body>
</html>
