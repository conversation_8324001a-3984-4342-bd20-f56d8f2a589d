// generated by go run gen.go -test; DO NOT EDIT

package atom

var testAtomList = []string{
	"a",
	"abbr",
	"abbr",
	"accept",
	"accept-charset",
	"accesskey",
	"action",
	"address",
	"align",
	"alt",
	"annotation",
	"annotation-xml",
	"applet",
	"area",
	"article",
	"aside",
	"async",
	"audio",
	"autocomplete",
	"autofocus",
	"autoplay",
	"b",
	"base",
	"basefont",
	"bdi",
	"bdo",
	"bgsound",
	"big",
	"blink",
	"blockquote",
	"body",
	"br",
	"button",
	"canvas",
	"caption",
	"center",
	"challenge",
	"charset",
	"checked",
	"cite",
	"cite",
	"class",
	"code",
	"col",
	"colgroup",
	"color",
	"cols",
	"colspan",
	"command",
	"command",
	"content",
	"contenteditable",
	"contextmenu",
	"controls",
	"coords",
	"crossorigin",
	"data",
	"data",
	"datalist",
	"datetime",
	"dd",
	"default",
	"defer",
	"del",
	"desc",
	"details",
	"dfn",
	"dialog",
	"dir",
	"dirname",
	"disabled",
	"div",
	"dl",
	"download",
	"draggable",
	"dropzone",
	"dt",
	"em",
	"embed",
	"enctype",
	"face",
	"fieldset",
	"figcaption",
	"figure",
	"font",
	"footer",
	"for",
	"foreignObject",
	"foreignobject",
	"form",
	"form",
	"formaction",
	"formenctype",
	"formmethod",
	"formnovalidate",
	"formtarget",
	"frame",
	"frameset",
	"h1",
	"h2",
	"h3",
	"h4",
	"h5",
	"h6",
	"head",
	"header",
	"headers",
	"height",
	"hgroup",
	"hidden",
	"high",
	"hr",
	"href",
	"hreflang",
	"html",
	"http-equiv",
	"i",
	"icon",
	"id",
	"iframe",
	"image",
	"img",
	"input",
	"inputmode",
	"ins",
	"isindex",
	"ismap",
	"itemid",
	"itemprop",
	"itemref",
	"itemscope",
	"itemtype",
	"kbd",
	"keygen",
	"keytype",
	"kind",
	"label",
	"label",
	"lang",
	"legend",
	"li",
	"link",
	"list",
	"listing",
	"loop",
	"low",
	"malignmark",
	"manifest",
	"map",
	"mark",
	"marquee",
	"math",
	"max",
	"maxlength",
	"media",
	"mediagroup",
	"menu",
	"menuitem",
	"meta",
	"meter",
	"method",
	"mglyph",
	"mi",
	"min",
	"minlength",
	"mn",
	"mo",
	"ms",
	"mtext",
	"multiple",
	"muted",
	"name",
	"nav",
	"nobr",
	"noembed",
	"noframes",
	"noscript",
	"novalidate",
	"object",
	"ol",
	"onabort",
	"onafterprint",
	"onautocomplete",
	"onautocompleteerror",
	"onbeforeprint",
	"onbeforeunload",
	"onblur",
	"oncancel",
	"oncanplay",
	"oncanplaythrough",
	"onchange",
	"onclick",
	"onclose",
	"oncontextmenu",
	"oncuechange",
	"ondblclick",
	"ondrag",
	"ondragend",
	"ondragenter",
	"ondragleave",
	"ondragover",
	"ondragstart",
	"ondrop",
	"ondurationchange",
	"onemptied",
	"onended",
	"onerror",
	"onfocus",
	"onhashchange",
	"oninput",
	"oninvalid",
	"onkeydown",
	"onkeypress",
	"onkeyup",
	"onlanguagechange",
	"onload",
	"onloadeddata",
	"onloadedmetadata",
	"onloadstart",
	"onmessage",
	"onmousedown",
	"onmousemove",
	"onmouseout",
	"onmouseover",
	"onmouseup",
	"onmousewheel",
	"onoffline",
	"ononline",
	"onpagehide",
	"onpageshow",
	"onpause",
	"onplay",
	"onplaying",
	"onpopstate",
	"onprogress",
	"onratechange",
	"onreset",
	"onresize",
	"onscroll",
	"onseeked",
	"onseeking",
	"onselect",
	"onshow",
	"onsort",
	"onstalled",
	"onstorage",
	"onsubmit",
	"onsuspend",
	"ontimeupdate",
	"ontoggle",
	"onunload",
	"onvolumechange",
	"onwaiting",
	"open",
	"optgroup",
	"optimum",
	"option",
	"output",
	"p",
	"param",
	"pattern",
	"ping",
	"placeholder",
	"plaintext",
	"poster",
	"pre",
	"preload",
	"progress",
	"prompt",
	"public",
	"q",
	"radiogroup",
	"readonly",
	"rel",
	"required",
	"reversed",
	"rows",
	"rowspan",
	"rp",
	"rt",
	"ruby",
	"s",
	"samp",
	"sandbox",
	"scope",
	"scoped",
	"script",
	"seamless",
	"section",
	"select",
	"selected",
	"shape",
	"size",
	"sizes",
	"small",
	"sortable",
	"sorted",
	"source",
	"spacer",
	"span",
	"span",
	"spellcheck",
	"src",
	"srcdoc",
	"srclang",
	"start",
	"step",
	"strike",
	"strong",
	"style",
	"style",
	"sub",
	"summary",
	"sup",
	"svg",
	"system",
	"tabindex",
	"table",
	"target",
	"tbody",
	"td",
	"template",
	"textarea",
	"tfoot",
	"th",
	"thead",
	"time",
	"title",
	"title",
	"tr",
	"track",
	"translate",
	"tt",
	"type",
	"typemustmatch",
	"u",
	"ul",
	"usemap",
	"value",
	"var",
	"video",
	"wbr",
	"width",
	"wrap",
	"xmp",
}
