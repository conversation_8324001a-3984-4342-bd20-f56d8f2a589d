<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">

  <title>Go 1 Release Notes - The Go Programming Language</title>

<link type="text/css" rel="stylesheet" href="/doc/style.css">
<script type="text/javascript" src="/doc/godocs.js"></script>

<link rel="search" type="application/opensearchdescription+xml" title="godoc" href="/opensearch.xml" />

<script type="text/javascript">
var _gaq = _gaq || [];
_gaq.push(["_setAccount", "UA-********-2"]);
_gaq.push(["_trackPageview"]);
</script>
</head>
<body>

<div id="topbar"><div class="container wide">

<form method="GET" action="/search">
<div id="menu">
<a href="/doc/">Documents</a>
<a href="/ref/">References</a>
<a href="/pkg/">Packages</a>
<a href="/project/">The Project</a>
<a href="/help/">Help</a>
<input type="text" id="search" name="q" class="inactive" value="Search">
</div>
<div id="heading"><a href="/">The Go Programming Language</a></div>
</form>

</div></div>

<div id="page" class="wide">


  <div id="plusone"><g:plusone size="small" annotation="none"></g:plusone></div>
  <h1>Go 1 Release Notes</h1>




<div id="nav"></div>




<h2 id="introduction">Introduction to Go 1</h2>

<p>
Go version 1, Go 1 for short, defines a language and a set of core libraries
that provide a stable foundation for creating reliable products, projects, and
publications.
</p>

<p>
The driving motivation for Go 1 is stability for its users. People should be able to
write Go programs and expect that they will continue to compile and run without
change, on a time scale of years, including in production environments such as
Google App Engine. Similarly, people should be able to write books about Go, be
able to say which version of Go the book is describing, and have that version
number still be meaningful much later.
</p>

<p>
Code that compiles in Go 1 should, with few exceptions, continue to compile and
run throughout the lifetime of that version, even as we issue updates and bug
fixes such as Go version 1.1, 1.2, and so on. Other than critical fixes, changes
made to the language and library for subsequent releases of Go 1 may
add functionality but will not break existing Go 1 programs.
<a href="go1compat.html">The Go 1 compatibility document</a>
explains the compatibility guidelines in more detail.
</p>

<p>
Go 1 is a representation of Go as it used today, not a wholesale rethinking of
the language. We avoided designing new features and instead focused on cleaning
up problems and inconsistencies and improving portability. There are a number
changes to the Go language and packages that we had considered for some time and
prototyped but not released primarily because they are significant and
backwards-incompatible. Go 1 was an opportunity to get them out, which is
helpful for the long term, but also means that Go 1 introduces incompatibilities
for old programs. Fortunately, the <code>go</code> <code>fix</code> tool can
automate much of the work needed to bring programs up to the Go 1 standard.
</p>

<p>
This document outlines the major changes in Go 1 that will affect programmers
updating existing code; its reference point is the prior release, r60 (tagged as
r60.3). It also explains how to update code from r60 to run under Go 1.
</p>

<h2 id="language">Changes to the language</h2>

<h3 id="append">Append</h3>

<p>
The <code>append</code> predeclared variadic function makes it easy to grow a slice
by adding elements to the end.
A common use is to add bytes to the end of a byte slice when generating output.
However, <code>append</code> did not provide a way to append a string to a <code>[]byte</code>,
which is another common case.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/greeting := ..byte/` `/append.*hello/`}}
-->    greeting := []byte{}
    greeting = append(greeting, []byte(&#34;hello &#34;)...)</pre>

<p>
By analogy with the similar property of <code>copy</code>, Go 1
permits a string to be appended (byte-wise) directly to a byte
slice, reducing the friction between strings and byte slices.
The conversion is no longer necessary:
</p>

<pre><!--{{code "/doc/progs/go1.go" `/append.*world/`}}
-->    greeting = append(greeting, &#34;world&#34;...)</pre>

<p>
<em>Updating</em>:
This is a new feature, so existing code needs no changes.
</p>

<h3 id="close">Close</h3>

<p>
The <code>close</code> predeclared function provides a mechanism
for a sender to signal that no more values will be sent.
It is important to the implementation of <code>for</code> <code>range</code>
loops over channels and is helpful in other situations.
Partly by design and partly because of race conditions that can occur otherwise,
it is intended for use only by the goroutine sending on the channel,
not by the goroutine receiving data.
However, before Go 1 there was no compile-time checking that <code>close</code>
was being used correctly.
</p>

<p>
To close this gap, at least in part, Go 1 disallows <code>close</code> on receive-only channels.
Attempting to close such a channel is a compile-time error.
</p>

<pre>
    var c chan int
    var csend chan&lt;- int = c
    var crecv &lt;-chan int = c
    close(c)     // legal
    close(csend) // legal
    close(crecv) // illegal
</pre>

<p>
<em>Updating</em>:
Existing code that attempts to close a receive-only channel was
erroneous even before Go 1 and should be fixed.  The compiler will
now reject such code.
</p>

<h3 id="literals">Composite literals</h3>

<p>
In Go 1, a composite literal of array, slice, or map type can elide the
type specification for the elements' initializers if they are of pointer type.
All four of the initializations in this example are legal; the last one was illegal before Go 1.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/type Date struct/` `/STOP/`}}
-->    type Date struct {
        month string
        day   int
    }
    <span class="comment">// Struct values, fully qualified; always legal.</span>
    holiday1 := []Date{
        Date{&#34;Feb&#34;, 14},
        Date{&#34;Nov&#34;, 11},
        Date{&#34;Dec&#34;, 25},
    }
    <span class="comment">// Struct values, type name elided; always legal.</span>
    holiday2 := []Date{
        {&#34;Feb&#34;, 14},
        {&#34;Nov&#34;, 11},
        {&#34;Dec&#34;, 25},
    }
    <span class="comment">// Pointers, fully qualified, always legal.</span>
    holiday3 := []*Date{
        &amp;Date{&#34;Feb&#34;, 14},
        &amp;Date{&#34;Nov&#34;, 11},
        &amp;Date{&#34;Dec&#34;, 25},
    }
    <span class="comment">// Pointers, type name elided; legal in Go 1.</span>
    holiday4 := []*Date{
        {&#34;Feb&#34;, 14},
        {&#34;Nov&#34;, 11},
        {&#34;Dec&#34;, 25},
    }</pre>

<p>
<em>Updating</em>:
This change has no effect on existing code, but the command
<code>gofmt</code> <code>-s</code> applied to existing source
will, among other things, elide explicit element types wherever permitted.
</p>


<h3 id="init">Goroutines during init</h3>

<p>
The old language defined that <code>go</code> statements executed during initialization created goroutines but that they did not begin to run until initialization of the entire program was complete.
This introduced clumsiness in many places and, in effect, limited the utility
of the <code>init</code> construct:
if it was possible for another package to use the library during initialization, the library
was forced to avoid goroutines.
This design was done for reasons of simplicity and safety but,
as our confidence in the language grew, it seemed unnecessary.
Running goroutines during initialization is no more complex or unsafe than running them during normal execution.
</p>

<p>
In Go 1, code that uses goroutines can be called from
<code>init</code> routines and global initialization expressions
without introducing a deadlock.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/PackageGlobal/` `/^}/`}}
-->var PackageGlobal int

func init() {
    c := make(chan int)
    go initializationFunction(c)
    PackageGlobal = &lt;-c
}</pre>

<p>
<em>Updating</em>:
This is a new feature, so existing code needs no changes,
although it's possible that code that depends on goroutines not starting before <code>main</code> will break.
There was no such code in the standard repository.
</p>

<h3 id="rune">The rune type</h3>

<p>
The language spec allows the <code>int</code> type to be 32 or 64 bits wide, but current implementations set <code>int</code> to 32 bits even on 64-bit platforms.
It would be preferable to have <code>int</code> be 64 bits on 64-bit platforms.
(There are important consequences for indexing large slices.)
However, this change would waste space when processing Unicode characters with
the old language because the <code>int</code> type was also used to hold Unicode code points: each code point would waste an extra 32 bits of storage if <code>int</code> grew from 32 bits to 64.
</p>

<p>
To make changing to 64-bit <code>int</code> feasible,
Go 1 introduces a new basic type, <code>rune</code>, to represent
individual Unicode code points.
It is an alias for <code>int32</code>, analogous to <code>byte</code>
as an alias for <code>uint8</code>.
</p>

<p>
Character literals such as <code>'a'</code>, <code>'語'</code>, and <code>'\u0345'</code>
now have default type <code>rune</code>,
analogous to <code>1.0</code> having default type <code>float64</code>.
A variable initialized to a character constant will therefore
have type <code>rune</code> unless otherwise specified.
</p>

<p>
Libraries have been updated to use <code>rune</code> rather than <code>int</code>
when appropriate. For instance, the functions <code>unicode.ToLower</code> and
relatives now take and return a <code>rune</code>.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/STARTRUNE/` `/ENDRUNE/`}}
-->    delta := &#39;δ&#39; <span class="comment">// delta has type rune.</span>
    var DELTA rune
    DELTA = unicode.ToUpper(delta)
    epsilon := unicode.ToLower(DELTA + 1)
    if epsilon != &#39;δ&#39;+1 {
        log.Fatal(&#34;inconsistent casing for Greek&#34;)
    }</pre>

<p>
<em>Updating</em>:
Most source code will be unaffected by this because the type inference from
<code>:=</code> initializers introduces the new type silently, and it propagates
from there.
Some code may get type errors that a trivial conversion will resolve.
</p>

<h3 id="error">The error type</h3>

<p>
Go 1 introduces a new built-in type, <code>error</code>, which has the following definition:
</p>

<pre>
    type error interface {
        Error() string
    }
</pre>

<p>
Since the consequences of this type are all in the package library,
it is discussed <a href="#errors">below</a>.
</p>

<h3 id="delete">Deleting from maps</h3>

<p>
In the old language, to delete the entry with key <code>k</code> from map <code>m</code>, one wrote the statement,
</p>

<pre>
    m[k] = value, false
</pre>

<p>
This syntax was a peculiar special case, the only two-to-one assignment.
It required passing a value (usually ignored) that is evaluated but discarded,
plus a boolean that was nearly always the constant <code>false</code>.
It did the job but was odd and a point of contention.
</p>

<p>
In Go 1, that syntax has gone; instead there is a new built-in
function, <code>delete</code>.  The call
</p>

<pre><!--{{code "/doc/progs/go1.go" `/delete\(m, k\)/`}}
-->    delete(m, k)</pre>

<p>
will delete the map entry retrieved by the expression <code>m[k]</code>.
There is no return value. Deleting a non-existent entry is a no-op.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will convert expressions of the form <code>m[k] = value,
false</code> into <code>delete(m, k)</code> when it is clear that
the ignored value can be safely discarded from the program and
<code>false</code> refers to the predefined boolean constant.
The fix tool
will flag other uses of the syntax for inspection by the programmer.
</p>

<h3 id="iteration">Iterating in maps</h3>

<p>
The old language specification did not define the order of iteration for maps,
and in practice it differed across hardware platforms.
This caused tests that iterated over maps to be fragile and non-portable, with the
unpleasant property that a test might always pass on one machine but break on another.
</p>

<p>
In Go 1, the order in which elements are visited when iterating
over a map using a <code>for</code> <code>range</code> statement
is defined to be unpredictable, even if the same loop is run multiple
times with the same map.
Code should not assume that the elements are visited in any particular order.
</p>

<p>
This change means that code that depends on iteration order is very likely to break early and be fixed long before it becomes a problem.
Just as important, it allows the map implementation to ensure better map balancing even when programs are using range loops to select an element from a map.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/Sunday/` `/^	}/`}}
-->    m := map[string]int{&#34;Sunday&#34;: 0, &#34;Monday&#34;: 1}
    for name, value := range m {
        <span class="comment">// This loop should not assume Sunday will be visited first.</span>
        f(name, value)
    }</pre>

<p>
<em>Updating</em>:
This is one change where tools cannot help.  Most existing code
will be unaffected, but some programs may break or misbehave; we
recommend manual checking of all range statements over maps to
verify they do not depend on iteration order. There were a few such
examples in the standard repository; they have been fixed.
Note that it was already incorrect to depend on the iteration order, which
was unspecified. This change codifies the unpredictability.
</p>

<h3 id="multiple_assignment">Multiple assignment</h3>

<p>
The language specification has long guaranteed that in assignments
the right-hand-side expressions are all evaluated before any left-hand-side expressions are assigned.
To guarantee predictable behavior,
Go 1 refines the specification further.
</p>

<p>
If the left-hand side of the assignment
statement contains expressions that require evaluation, such as
function calls or array indexing operations, these will all be done
using the usual left-to-right rule before any variables are assigned
their value.  Once everything is evaluated, the actual assignments
proceed in left-to-right order.
</p>

<p>
These examples illustrate the behavior.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/sa :=/` `/then sc.0. = 2/`}}
-->    sa := []int{1, 2, 3}
    i := 0
    i, sa[i] = 1, 2 <span class="comment">// sets i = 1, sa[0] = 2</span>

    sb := []int{1, 2, 3}
    j := 0
    sb[j], j = 2, 1 <span class="comment">// sets sb[0] = 2, j = 1</span>

    sc := []int{1, 2, 3}
    sc[0], sc[0] = 1, 2 <span class="comment">// sets sc[0] = 1, then sc[0] = 2 (so sc[0] = 2 at end)</span></pre>

<p>
<em>Updating</em>:
This is one change where tools cannot help, but breakage is unlikely.
No code in the standard repository was broken by this change, and code
that depended on the previous unspecified behavior was already incorrect.
</p>

<h3 id="shadowing">Returns and shadowed variables</h3>

<p>
A common mistake is to use <code>return</code> (without arguments) after an assignment to a variable that has the same name as a result variable but is not the same variable.
This situation is called <em>shadowing</em>: the result variable has been shadowed by another variable with the same name declared in an inner scope.
</p>

<p>
In functions with named return values,
the Go 1 compilers disallow return statements without arguments if any of the named return values is shadowed at the point of the return statement.
(It isn't part of the specification, because this is one area we are still exploring;
the situation is analogous to the compilers rejecting functions that do not end with an explicit return statement.)
</p>

<p>
This function implicitly returns a shadowed return value and will be rejected by the compiler:
</p>

<pre>
    func Bug() (i, j, k int) {
        for i = 0; i &lt; 5; i++ {
            for j := 0; j &lt; 5; j++ { // Redeclares j.
                k += i*j
                if k > 100 {
                    return // Rejected: j is shadowed here.
                }
            }
        }
        return // OK: j is not shadowed here.
    }
</pre>

<p>
<em>Updating</em>:
Code that shadows return values in this way will be rejected by the compiler and will need to be fixed by hand.
The few cases that arose in the standard repository were mostly bugs.
</p>

<h3 id="unexported">Copying structs with unexported fields</h3>

<p>
The old language did not allow a package to make a copy of a struct value containing unexported fields belonging to a different package.
There was, however, a required exception for a method receiver;
also, the implementations of <code>copy</code> and <code>append</code> have never honored the restriction.
</p>

<p>
Go 1 will allow packages to copy struct values containing unexported fields from other packages.
Besides resolving the inconsistency,
this change admits a new kind of API: a package can return an opaque value without resorting to a pointer or interface.
The new implementations of <code>time.Time</code> and
<code>reflect.Value</code> are examples of types taking advantage of this new property.
</p>

<p>
As an example, if package <code>p</code> includes the definitions,
</p>

<pre>
    type Struct struct {
        Public int
        secret int
    }
    func NewStruct(a int) Struct {  // Note: not a pointer.
        return Struct{a, f(a)}
    }
    func (s Struct) String() string {
        return fmt.Sprintf("{%d (secret %d)}", s.Public, s.secret)
    }
</pre>

<p>
a package that imports <code>p</code> can assign and copy values of type
<code>p.Struct</code> at will.
Behind the scenes the unexported fields will be assigned and copied just
as if they were exported,
but the client code will never be aware of them. The code
</p>

<pre>
    import "p"

    myStruct := p.NewStruct(23)
    copyOfMyStruct := myStruct
    fmt.Println(myStruct, copyOfMyStruct)
</pre>

<p>
will show that the secret field of the struct has been copied to the new value.
</p>

<p>
<em>Updating</em>:
This is a new feature, so existing code needs no changes.
</p>

<h3 id="equality">Equality</h3>

<p>
Before Go 1, the language did not define equality on struct and array values.
This meant,
among other things, that structs and arrays could not be used as map keys.
On the other hand, Go did define equality on function and map values.
Function equality was problematic in the presence of closures
(when are two closures equal?)
while map equality compared pointers, not the maps' content, which was usually
not what the user would want.
</p>

<p>
Go 1 addressed these issues.
First, structs and arrays can be compared for equality and inequality
(<code>==</code> and <code>!=</code>),
and therefore be used as map keys,
provided they are composed from elements for which equality is also defined,
using element-wise comparison.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/type Day struct/` `/Printf/`}}
-->    type Day struct {
        long  string
        short string
    }
    Christmas := Day{&#34;Christmas&#34;, &#34;XMas&#34;}
    Thanksgiving := Day{&#34;Thanksgiving&#34;, &#34;Turkey&#34;}
    holiday := map[Day]bool{
        Christmas:    true,
        Thanksgiving: true,
    }
    fmt.Printf(&#34;Christmas is a holiday: %t\n&#34;, holiday[Christmas])</pre>

<p>
Second, Go 1 removes the definition of equality for function values,
except for comparison with <code>nil</code>.
Finally, map equality is gone too, also except for comparison with <code>nil</code>.
</p>

<p>
Note that equality is still undefined for slices, for which the
calculation is in general infeasible.  Also note that the ordered
comparison operators (<code>&lt;</code> <code>&lt;=</code>
<code>&gt;</code> <code>&gt;=</code>) are still undefined for
structs and arrays.

<p>
<em>Updating</em>:
Struct and array equality is a new feature, so existing code needs no changes.
Existing code that depends on function or map equality will be
rejected by the compiler and will need to be fixed by hand.
Few programs will be affected, but the fix may require some
redesign.
</p>

<h2 id="packages">The package hierarchy</h2>

<p>
Go 1 addresses many deficiencies in the old standard library and
cleans up a number of packages, making them more internally consistent
and portable.
</p>

<p>
This section describes how the packages have been rearranged in Go 1.
Some have moved, some have been renamed, some have been deleted.
New packages are described in later sections.
</p>

<h3 id="hierarchy">The package hierarchy</h3>

<p>
Go 1 has a rearranged package hierarchy that groups related items
into subdirectories. For instance, <code>utf8</code> and
<code>utf16</code> now occupy subdirectories of <code>unicode</code>.
Also, <a href="#subrepo">some packages</a> have moved into
subrepositories of
<a href="http://code.google.com/p/go"><code>code.google.com/p/go</code></a>
while <a href="#deleted">others</a> have been deleted outright.
</p>

<table class="codetable" frame="border" summary="Moved packages">
<colgroup align="left" width="60%"></colgroup>
<colgroup align="left" width="40%"></colgroup>
<tr>
<th align="left">Old path</th>
<th align="left">New path</th>
</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>asn1</td> <td>encoding/asn1</td></tr>
<tr><td>csv</td> <td>encoding/csv</td></tr>
<tr><td>gob</td> <td>encoding/gob</td></tr>
<tr><td>json</td> <td>encoding/json</td></tr>
<tr><td>xml</td> <td>encoding/xml</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>exp/template/html</td> <td>html/template</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>big</td> <td>math/big</td></tr>
<tr><td>cmath</td> <td>math/cmplx</td></tr>
<tr><td>rand</td> <td>math/rand</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>http</td> <td>net/http</td></tr>
<tr><td>http/cgi</td> <td>net/http/cgi</td></tr>
<tr><td>http/fcgi</td> <td>net/http/fcgi</td></tr>
<tr><td>http/httptest</td> <td>net/http/httptest</td></tr>
<tr><td>http/pprof</td> <td>net/http/pprof</td></tr>
<tr><td>mail</td> <td>net/mail</td></tr>
<tr><td>rpc</td> <td>net/rpc</td></tr>
<tr><td>rpc/jsonrpc</td> <td>net/rpc/jsonrpc</td></tr>
<tr><td>smtp</td> <td>net/smtp</td></tr>
<tr><td>url</td> <td>net/url</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>exec</td> <td>os/exec</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>scanner</td> <td>text/scanner</td></tr>
<tr><td>tabwriter</td> <td>text/tabwriter</td></tr>
<tr><td>template</td> <td>text/template</td></tr>
<tr><td>template/parse</td> <td>text/template/parse</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>utf8</td> <td>unicode/utf8</td></tr>
<tr><td>utf16</td> <td>unicode/utf16</td></tr>
</table>

<p>
Note that the package names for the old <code>cmath</code> and
<code>exp/template/html</code> packages have changed to <code>cmplx</code>
and <code>template</code>.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update all imports and package renames for packages that
remain inside the standard repository.  Programs that import packages
that are no longer in the standard repository will need to be edited
by hand.
</p>

<h3 id="exp">The package tree exp</h3>

<p>
Because they are not standardized, the packages under the <code>exp</code> directory will not be available in the
standard Go 1 release distributions, although they will be available in source code form
in <a href="http://code.google.com/p/go/">the repository</a> for
developers who wish to use them.
</p>

<p>
Several packages have moved under <code>exp</code> at the time of Go 1's release:
</p>

<ul>
<li><code>ebnf</code></li>
<li><code>html</code><sup>&#8224;</sup></li>
<li><code>go/types</code></li>
</ul>

<p>
(<sup>&#8224;</sup>The <code>EscapeString</code> and <code>UnescapeString</code> types remain
in package <code>html</code>.)
</p>

<p>
All these packages are available under the same names, with the prefix <code>exp/</code>: <code>exp/ebnf</code> etc.
</p>

<p>
Also, the <code>utf8.String</code> type has been moved to its own package, <code>exp/utf8string</code>.
</p>

<p>
Finally, the <code>gotype</code> command now resides in <code>exp/gotype</code>, while
<code>ebnflint</code> is now in <code>exp/ebnflint</code>.
If they are installed, they now reside in <code>$GOROOT/bin/tool</code>.
</p>

<p>
<em>Updating</em>:
Code that uses packages in <code>exp</code> will need to be updated by hand,
or else compiled from an installation that has <code>exp</code> available.
The <code>go</code> <code>fix</code> tool or the compiler will complain about such uses.
</p>

<h3 id="old">The package tree old</h3>

<p>
Because they are deprecated, the packages under the <code>old</code> directory will not be available in the
standard Go 1 release distributions, although they will be available in source code form for
developers who wish to use them.
</p>

<p>
The packages in their new locations are:
</p>

<ul>
<li><code>old/netchan</code></li>
<li><code>old/regexp</code></li>
<li><code>old/template</code></li>
</ul>

<p>
<em>Updating</em>:
Code that uses packages now in <code>old</code> will need to be updated by hand,
or else compiled from an installation that has <code>old</code> available.
The <code>go</code> <code>fix</code> tool will warn about such uses.
</p>

<h3 id="deleted">Deleted packages</h3>

<p>
Go 1 deletes several packages outright:
</p>

<ul>
<li><code>container/vector</code></li>
<li><code>exp/datafmt</code></li>
<li><code>go/typechecker</code></li>
<li><code>try</code></li>
</ul>

<p>
and also the command <code>gotry</code>.
</p>

<p>
<em>Updating</em>:
Code that uses <code>container/vector</code> should be updated to use
slices directly.  See
<a href="http://code.google.com/p/go-wiki/wiki/SliceTricks">the Go
Language Community Wiki</a> for some suggestions.
Code that uses the other packages (there should be almost zero) will need to be rethought.
</p>

<h3 id="subrepo">Packages moving to subrepositories</h3>

<p>
Go 1 has moved a number of packages into other repositories, usually sub-repositories of
<a href="http://code.google.com/p/go/">the main Go repository</a>.
This table lists the old and new import paths:

<table class="codetable" frame="border" summary="Sub-repositories">
<colgroup align="left" width="40%"></colgroup>
<colgroup align="left" width="60%"></colgroup>
<tr>
<th align="left">Old</th>
<th align="left">New</th>
</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>crypto/bcrypt</td> <td>code.google.com/p/go.crypto/bcrypt</tr>
<tr><td>crypto/blowfish</td> <td>code.google.com/p/go.crypto/blowfish</tr>
<tr><td>crypto/cast5</td> <td>code.google.com/p/go.crypto/cast5</tr>
<tr><td>crypto/md4</td> <td>code.google.com/p/go.crypto/md4</tr>
<tr><td>crypto/ocsp</td> <td>code.google.com/p/go.crypto/ocsp</tr>
<tr><td>crypto/openpgp</td> <td>code.google.com/p/go.crypto/openpgp</tr>
<tr><td>crypto/openpgp/armor</td> <td>code.google.com/p/go.crypto/openpgp/armor</tr>
<tr><td>crypto/openpgp/elgamal</td> <td>code.google.com/p/go.crypto/openpgp/elgamal</tr>
<tr><td>crypto/openpgp/errors</td> <td>code.google.com/p/go.crypto/openpgp/errors</tr>
<tr><td>crypto/openpgp/packet</td> <td>code.google.com/p/go.crypto/openpgp/packet</tr>
<tr><td>crypto/openpgp/s2k</td> <td>code.google.com/p/go.crypto/openpgp/s2k</tr>
<tr><td>crypto/ripemd160</td> <td>code.google.com/p/go.crypto/ripemd160</tr>
<tr><td>crypto/twofish</td> <td>code.google.com/p/go.crypto/twofish</tr>
<tr><td>crypto/xtea</td> <td>code.google.com/p/go.crypto/xtea</tr>
<tr><td>exp/ssh</td> <td>code.google.com/p/go.crypto/ssh</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>image/bmp</td> <td>code.google.com/p/go.image/bmp</tr>
<tr><td>image/tiff</td> <td>code.google.com/p/go.image/tiff</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>net/dict</td> <td>code.google.com/p/go.net/dict</tr>
<tr><td>net/websocket</td> <td>code.google.com/p/go.net/websocket</tr>
<tr><td>exp/spdy</td> <td>code.google.com/p/go.net/spdy</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>encoding/git85</td> <td>code.google.com/p/go.codereview/git85</tr>
<tr><td>patch</td> <td>code.google.com/p/go.codereview/patch</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>exp/wingui</td> <td>code.google.com/p/gowingui</tr>
</table>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update imports of these packages to use the new import paths.
Installations that depend on these packages will need to install them using
a <code>go get</code> command.
</p>

<h2 id="major">Major changes to the library</h2>

<p>
This section describes significant changes to the core libraries, the ones that
affect the most programs.
</p>

<h3 id="errors">The error type and errors package</h3>

<p>
The placement of <code>os.Error</code> in package <code>os</code> is mostly historical: errors first came up when implementing package <code>os</code>, and they seemed system-related at the time.
Since then it has become clear that errors are more fundamental than the operating system.  For example, it would be nice to use <code>Errors</code> in packages that <code>os</code> depends on, like <code>syscall</code>.
Also, having <code>Error</code> in <code>os</code> introduces many dependencies on <code>os</code> that would otherwise not exist.
</p>

<p>
Go 1 solves these problems by introducing a built-in <code>error</code> interface type and a separate <code>errors</code> package (analogous to <code>bytes</code> and <code>strings</code>) that contains utility functions.
It replaces <code>os.NewError</code> with
<a href="/pkg/errors/#New"><code>errors.New</code></a>,
giving errors a more central place in the environment.
</p>

<p>
So the widely-used <code>String</code> method does not cause accidental satisfaction
of the <code>error</code> interface, the <code>error</code> interface uses instead
the name <code>Error</code> for that method:
</p>

<pre>
    type error interface {
        Error() string
    }
</pre>

<p>
The <code>fmt</code> library automatically invokes <code>Error</code>, as it already
does for <code>String</code>, for easy printing of error values.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/START ERROR EXAMPLE/` `/END ERROR EXAMPLE/`}}
-->type SyntaxError struct {
    File    string
    Line    int
    Message string
}

func (se *SyntaxError) Error() string {
    return fmt.Sprintf(&#34;%s:%d: %s&#34;, se.File, se.Line, se.Message)
}</pre>

<p>
All standard packages have been updated to use the new interface; the old <code>os.Error</code> is gone.
</p>

<p>
A new package, <a href="/pkg/errors/"><code>errors</code></a>, contains the function
</p>

<pre>
func New(text string) error
</pre>

<p>
to turn a string into an error. It replaces the old <code>os.NewError</code>.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/ErrSyntax/`}}
-->    var ErrSyntax = errors.New(&#34;syntax error&#34;)</pre>
		
<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update almost all code affected by the change.
Code that defines error types with a <code>String</code> method will need to be updated
by hand to rename the methods to <code>Error</code>.
</p>

<h3 id="errno">System call errors</h3>

<p>
The old <code>syscall</code> package, which predated <code>os.Error</code>
(and just about everything else),
returned errors as <code>int</code> values.
In turn, the <code>os</code> package forwarded many of these errors, such
as <code>EINVAL</code>, but using a different set of errors on each platform.
This behavior was unpleasant and unportable.
</p>

<p>
In Go 1, the
<a href="/pkg/syscall/"><code>syscall</code></a>
package instead returns an <code>error</code> for system call errors.
On Unix, the implementation is done by a
<a href="/pkg/syscall/#Errno"><code>syscall.Errno</code></a> type
that satisfies <code>error</code> and replaces the old <code>os.Errno</code>.
</p>

<p>
The changes affecting <code>os.EINVAL</code> and relatives are
described <a href="#os">elsewhere</a>.

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update almost all code affected by the change.
Regardless, most code should use the <code>os</code> package
rather than <code>syscall</code> and so will be unaffected.
</p>

<h3 id="time">Time</h3>

<p>
Time is always a challenge to support well in a programming language.
The old Go <code>time</code> package had <code>int64</code> units, no
real type safety,
and no distinction between absolute times and durations.
</p>

<p>
One of the most sweeping changes in the Go 1 library is therefore a
complete redesign of the
<a href="/pkg/time/"><code>time</code></a> package.
Instead of an integer number of nanoseconds as an <code>int64</code>,
and a separate <code>*time.Time</code> type to deal with human
units such as hours and years,
there are now two fundamental types:
<a href="/pkg/time/#Time"><code>time.Time</code></a>
(a value, so the <code>*</code> is gone), which represents a moment in time;
and <a href="/pkg/time/#Duration"><code>time.Duration</code></a>,
which represents an interval.
Both have nanosecond resolution.
A <code>Time</code> can represent any time into the ancient
past and remote future, while a <code>Duration</code> can
span plus or minus only about 290 years.
There are methods on these types, plus a number of helpful
predefined constant durations such as <code>time.Second</code>.
</p>

<p>
Among the new methods are things like
<a href="/pkg/time/#Time.Add"><code>Time.Add</code></a>,
which adds a <code>Duration</code> to a <code>Time</code>, and
<a href="/pkg/time/#Time.Sub"><code>Time.Sub</code></a>,
which subtracts two <code>Times</code> to yield a <code>Duration</code>.
</p>

<p>
The most important semantic change is that the Unix epoch (Jan 1, 1970) is now
relevant only for those functions and methods that mention Unix:
<a href="/pkg/time/#Unix"><code>time.Unix</code></a>
and the <a href="/pkg/time/#Time.Unix"><code>Unix</code></a>
and <a href="/pkg/time/#Time.UnixNano"><code>UnixNano</code></a> methods
of the <code>Time</code> type.
In particular,
<a href="/pkg/time/#Now"><code>time.Now</code></a>
returns a <code>time.Time</code> value rather than, in the old
API, an integer nanosecond count since the Unix epoch.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/sleepUntil/` `/^}/`}}
--><span class="comment">// sleepUntil sleeps until the specified time. It returns immediately if it&#39;s too late.</span>
func sleepUntil(wakeup time.Time) {
    now := time.Now() <span class="comment">// A Time.</span>
    if !wakeup.After(now) {
        return
    }
    delta := wakeup.Sub(now) <span class="comment">// A Duration.</span>
    fmt.Printf(&#34;Sleeping for %.3fs\n&#34;, delta.Seconds())
    time.Sleep(delta)
}</pre>

<p>
The new types, methods, and constants have been propagated through
all the standard packages that use time, such as <code>os</code> and
its representation of file time stamps.
</p>

<p>
<em>Updating</em>:
The <code>go</code> <code>fix</code> tool will update many uses of the old <code>time</code> package to use the new
types and methods, although it does not replace values such as <code>1e9</code>
representing nanoseconds per second.
Also, because of type changes in some of the values that arise,
some of the expressions rewritten by the fix tool may require
further hand editing; in such cases the rewrite will include
the correct function or method for the old functionality, but
may have the wrong type or require further analysis.
</p>

<h2 id="minor">Minor changes to the library</h2>

<p>
This section describes smaller changes, such as those to less commonly
used packages or that affect
few programs beyond the need to run <code>go</code> <code>fix</code>.
This category includes packages that are new in Go 1.
Collectively they improve portability, regularize behavior, and
make the interfaces more modern and Go-like.
</p>

<h3 id="archive_zip">The archive/zip package</h3>

<p>
In Go 1, <a href="/pkg/archive/zip/#Writer"><code>*zip.Writer</code></a> no
longer has a <code>Write</code> method. Its presence was a mistake.
</p>

<p>
<em>Updating</em>:
What little code is affected will be caught by the compiler and must be updated by hand.
</p>

<h3 id="bufio">The bufio package</h3>

<p>
In Go 1, <a href="/pkg/bufio/#NewReaderSize"><code>bufio.NewReaderSize</code></a>
and
<a href="/pkg/bufio/#NewWriterSize"><code>bufio.NewWriterSize</code></a>
functions no longer return an error for invalid sizes.
If the argument size is too small or invalid, it is adjusted.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update calls that assign the error to _.
Calls that aren't fixed will be caught by the compiler and must be updated by hand.
</p>

<h3 id="compress">The compress/flate, compress/gzip and compress/zlib packages</h3>

<p>
In Go 1, the <code>NewWriterXxx</code> functions in
<a href="/pkg/compress/flate"><code>compress/flate</code></a>,
<a href="/pkg/compress/gzip"><code>compress/gzip</code></a> and
<a href="/pkg/compress/zlib"><code>compress/zlib</code></a>
all return <code>(*Writer, error)</code> if they take a compression level,
and <code>*Writer</code> otherwise. Package <code>gzip</code>'s
<code>Compressor</code> and <code>Decompressor</code> types have been renamed
to <code>Writer</code> and <code>Reader</code>. Package <code>flate</code>'s
<code>WrongValueError</code> type has been removed.
</p>

<p>
<em>Updating</em>
Running <code>go</code> <code>fix</code> will update old names and calls that assign the error to _.
Calls that aren't fixed will be caught by the compiler and must be updated by hand.
</p>

<h3 id="crypto_aes_des">The crypto/aes and crypto/des packages</h3>

<p>
In Go 1, the <code>Reset</code> method has been removed. Go does not guarantee
that memory is not copied and therefore this method was misleading.
</p>

<p>
The cipher-specific types <code>*aes.Cipher</code>, <code>*des.Cipher</code>,
and <code>*des.TripleDESCipher</code> have been removed in favor of
<code>cipher.Block</code>.
</p>

<p>
<em>Updating</em>:
Remove the calls to Reset. Replace uses of the specific cipher types with
cipher.Block.
</p>

<h3 id="crypto_elliptic">The crypto/elliptic package</h3>

<p>
In Go 1, <a href="/pkg/crypto/elliptic/#Curve"><code>elliptic.Curve</code></a>
has been made an interface to permit alternative implementations. The curve
parameters have been moved to the
<a href="/pkg/crypto/elliptic/#CurveParams"><code>elliptic.CurveParams</code></a>
structure.
</p>

<p>
<em>Updating</em>:
Existing users of <code>*elliptic.Curve</code> will need to change to
simply <code>elliptic.Curve</code>. Calls to <code>Marshal</code>,
<code>Unmarshal</code> and <code>GenerateKey</code> are now functions
in <code>crypto/elliptic</code> that take an <code>elliptic.Curve</code>
as their first argument.
</p>

<h3 id="crypto_hmac">The crypto/hmac package</h3>

<p>
In Go 1, the hash-specific functions, such as <code>hmac.NewMD5</code>, have
been removed from <code>crypto/hmac</code>. Instead, <code>hmac.New</code> takes
a function that returns a <code>hash.Hash</code>, such as <code>md5.New</code>.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will perform the needed changes.
</p>

<h3 id="crypto_x509">The crypto/x509 package</h3>

<p>
In Go 1, the
<a href="/pkg/crypto/x509/#CreateCertificate"><code>CreateCertificate</code></a>
and
<a href="/pkg/crypto/x509/#CreateCRL"><code>CreateCRL</code></a>
functions in <code>crypto/x509</code> have been altered to take an
<code>interface{}</code> where they previously took a <code>*rsa.PublicKey</code>
or <code>*rsa.PrivateKey</code>. This will allow other public key algorithms
to be implemented in the future.
</p>

<p>
<em>Updating</em>:
No changes will be needed.
</p>

<h3 id="encoding_binary">The encoding/binary package</h3>

<p>
In Go 1, the <code>binary.TotalSize</code> function has been replaced by
<a href="/pkg/encoding/binary/#Size"><code>Size</code></a>,
which takes an <code>interface{}</code> argument rather than
a <code>reflect.Value</code>.
</p>

<p>
<em>Updating</em>:
What little code is affected will be caught by the compiler and must be updated by hand.
</p>

<h3 id="encoding_xml">The encoding/xml package</h3>

<p>
In Go 1, the <a href="/pkg/encoding/xml/"><code>xml</code></a> package
has been brought closer in design to the other marshaling packages such
as <a href="/pkg/encoding/gob/"><code>encoding/gob</code></a>.
</p>

<p>
The old <code>Parser</code> type is renamed
<a href="/pkg/encoding/xml/#Decoder"><code>Decoder</code></a> and has a new
<a href="/pkg/encoding/xml/#Decoder.Decode"><code>Decode</code></a> method. An
<a href="/pkg/encoding/xml/#Encoder"><code>Encoder</code></a> type was also introduced.
</p>

<p>
The functions <a href="/pkg/encoding/xml/#Marshal"><code>Marshal</code></a>
and <a href="/pkg/encoding/xml/#Unmarshal"><code>Unmarshal</code></a>
work with <code>[]byte</code> values now. To work with streams,
use the new <a href="/pkg/encoding/xml/#Encoder"><code>Encoder</code></a>
and <a href="/pkg/encoding/xml/#Decoder"><code>Decoder</code></a> types.
</p>

<p>
When marshaling or unmarshaling values, the format of supported flags in
field tags has changed to be closer to the
<a href="/pkg/encoding/json"><code>json</code></a> package
(<code>`xml:"name,flag"`</code>). The matching done between field tags, field
names, and the XML attribute and element names is now case-sensitive.
The <code>XMLName</code> field tag, if present, must also match the name
of the XML element being marshaled.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update most uses of the package except for some calls to
<code>Unmarshal</code>. Special care must be taken with field tags,
since the fix tool will not update them and if not fixed by hand they will
misbehave silently in some cases. For example, the old
<code>"attr"</code> is now written <code>",attr"</code> while plain
<code>"attr"</code> remains valid but with a different meaning.
</p>

<h3 id="expvar">The expvar package</h3>

<p>
In Go 1, the <code>RemoveAll</code> function has been removed.
The <code>Iter</code> function and Iter method on <code>*Map</code> have
been replaced by
<a href="/pkg/expvar/#Do"><code>Do</code></a>
and
<a href="/pkg/expvar/#Map.Do"><code>(*Map).Do</code></a>.
</p>

<p>
<em>Updating</em>:
Most code using <code>expvar</code> will not need changing. The rare code that used
<code>Iter</code> can be updated to pass a closure to <code>Do</code> to achieve the same effect.
</p>

<h3 id="flag">The flag package</h3>

<p>
In Go 1, the interface <a href="/pkg/flag/#Value"><code>flag.Value</code></a> has changed slightly.
The <code>Set</code> method now returns an <code>error</code> instead of
a <code>bool</code> to indicate success or failure.
</p>

<p>
There is also a new kind of flag, <code>Duration</code>, to support argument
values specifying time intervals.
Values for such flags must be given units, just as <code>time.Duration</code>
formats them: <code>10s</code>, <code>1h30m</code>, etc.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/timeout/`}}
-->var timeout = flag.Duration(&#34;timeout&#34;, 30*time.Second, &#34;how long to wait for completion&#34;)</pre>

<p>
<em>Updating</em>:
Programs that implement their own flags will need minor manual fixes to update their
<code>Set</code> methods.
The <code>Duration</code> flag is new and affects no existing code.
</p>


<h3 id="go">The go/* packages</h3>

<p>
Several packages under <code>go</code> have slightly revised APIs.
</p>

<p>
A concrete <code>Mode</code> type was introduced for configuration mode flags
in the packages
<a href="/pkg/go/scanner/"><code>go/scanner</code></a>,
<a href="/pkg/go/parser/"><code>go/parser</code></a>,
<a href="/pkg/go/printer/"><code>go/printer</code></a>, and
<a href="/pkg/go/doc/"><code>go/doc</code></a>.
</p>

<p>
The modes <code>AllowIllegalChars</code> and <code>InsertSemis</code> have been removed
from the <a href="/pkg/go/scanner/"><code>go/scanner</code></a> package. They were mostly
useful for scanning text other then Go source files. Instead, the
<a href="/pkg/text/scanner/"><code>text/scanner</code></a> package should be used
for that purpose.
</p>

<p>
The <a href="/pkg/go/scanner/#ErrorHandler"><code>ErrorHandler</code></a> provided
to the scanner's <a href="/pkg/go/scanner/#Scanner.Init"><code>Init</code></a> method is
now simply a function rather than an interface. The <code>ErrorVector</code> type has
been removed in favor of the (existing) <a href="/pkg/go/scanner/#ErrorList"><code>ErrorList</code></a>
type, and the <code>ErrorVector</code> methods have been migrated. Instead of embedding
an <code>ErrorVector</code> in a client of the scanner, now a client should maintain
an <code>ErrorList</code>.
</p>

<p>
The set of parse functions provided by the <a href="/pkg/go/parser/"><code>go/parser</code></a>
package has been reduced to the primary parse function
<a href="/pkg/go/parser/#ParseFile"><code>ParseFile</code></a>, and a couple of
convenience functions <a href="/pkg/go/parser/#ParseDir"><code>ParseDir</code></a>
and <a href="/pkg/go/parser/#ParseExpr"><code>ParseExpr</code></a>.
</p>

<p>
The <a href="/pkg/go/printer/"><code>go/printer</code></a> package supports an additional
configuration mode <a href="/pkg/go/printer/#Mode"><code>SourcePos</code></a>;
if set, the printer will emit <code>//line</code> comments such that the generated
output contains the original source code position information. The new type
<a href="/pkg/go/printer/#CommentedNode"><code>CommentedNode</code></a> can be
used to provide comments associated with an arbitrary
<a href="/pkg/go/ast/#Node"><code>ast.Node</code></a> (until now only
<a href="/pkg/go/ast/#File"><code>ast.File</code></a> carried comment information).
</p>

<p>
The type names of the <a href="/pkg/go/doc/"><code>go/doc</code></a> package have been
streamlined by removing the <code>Doc</code> suffix: <code>PackageDoc</code>
is now <code>Package</code>, <code>ValueDoc</code> is <code>Value</code>, etc.
Also, all types now consistently have a <code>Name</code> field (or <code>Names</code>,
in the case of type <code>Value</code>) and <code>Type.Factories</code> has become
<code>Type.Funcs</code>.
Instead of calling <code>doc.NewPackageDoc(pkg, importpath)</code>,
documentation for a package is created with:
</p>

<pre>
    doc.New(pkg, importpath, mode)
</pre>

<p>
where the new <code>mode</code> parameter specifies the operation mode:
if set to <a href="/pkg/go/doc/#AllDecls"><code>AllDecls</code></a>, all declarations
(not just exported ones) are considered.
The function <code>NewFileDoc</code> was removed, and the function
<code>CommentText</code> has become the method
<a href="/pkg/go/ast/#Text"><code>Text</code></a> of
<a href="/pkg/go/ast/#CommentGroup"><code>ast.CommentGroup</code></a>.
</p>

<p>
In package <a href="/pkg/go/token/"><code>go/token</code></a>, the
<a href="/pkg/go/token/#FileSet"><code>token.FileSet</code></a> method <code>Files</code>
(which originally returned a channel of <code>*token.File</code>s) has been replaced
with the iterator <a href="/pkg/go/token/#FileSet.Iterate"><code>Iterate</code></a> that
accepts a function argument instead.
</p>

<p>
In package <a href="/pkg/go/build/"><code>go/build</code></a>, the API
has been nearly completely replaced.
The package still computes Go package information
but it does not run the build: the <code>Cmd</code> and <code>Script</code>
types are gone.
(To build code, use the new
<a href="/cmd/go/"><code>go</code></a> command instead.)
The <code>DirInfo</code> type is now named
<a href="/pkg/go/build/#Package"><code>Package</code></a>.
<code>FindTree</code> and <code>ScanDir</code> are replaced by
<a href="/pkg/go/build/#Import"><code>Import</code></a>
and
<a href="/pkg/go/build/#ImportDir"><code>ImportDir</code></a>.
</p>

<p>
<em>Updating</em>:
Code that uses packages in <code>go</code> will have to be updated by hand; the
compiler will reject incorrect uses. Templates used in conjunction with any of the
<code>go/doc</code> types may need manual fixes; the renamed fields will lead
to run-time errors.
</p>

<h3 id="hash">The hash package</h3>

<p>
In Go 1, the definition of <a href="/pkg/hash/#Hash"><code>hash.Hash</code></a> includes
a new method, <code>BlockSize</code>.  This new method is used primarily in the
cryptographic libraries.
</p>

<p>
The <code>Sum</code> method of the
<a href="/pkg/hash/#Hash"><code>hash.Hash</code></a> interface now takes a
<code>[]byte</code> argument, to which the hash value will be appended.
The previous behavior can be recreated by adding a <code>nil</code> argument to the call.
</p>

<p>
<em>Updating</em>:
Existing implementations of <code>hash.Hash</code> will need to add a
<code>BlockSize</code> method.  Hashes that process the input one byte at
a time can implement <code>BlockSize</code> to return 1.
Running <code>go</code> <code>fix</code> will update calls to the <code>Sum</code> methods of the various
implementations of <code>hash.Hash</code>.
</p>

<p>
<em>Updating</em>:
Since the package's functionality is new, no updating is necessary.
</p>

<h3 id="http">The http package</h3>

<p>
In Go 1 the <a href="/pkg/net/http/"><code>http</code></a> package is refactored,
putting some of the utilities into a
<a href="/pkg/net/http/httputil/"><code>httputil</code></a> subdirectory.
These pieces are only rarely needed by HTTP clients.
The affected items are:
</p>

<ul>
<li>ClientConn</li>
<li>DumpRequest</li>
<li>DumpRequestOut</li>
<li>DumpResponse</li>
<li>NewChunkedReader</li>
<li>NewChunkedWriter</li>
<li>NewClientConn</li>
<li>NewProxyClientConn</li>
<li>NewServerConn</li>
<li>NewSingleHostReverseProxy</li>
<li>ReverseProxy</li>
<li>ServerConn</li>
</ul>

<p>
The <code>Request.RawURL</code> field has been removed; it was a
historical artifact.
</p>

<p>
The <code>Handle</code> and <code>HandleFunc</code>
functions, and the similarly-named methods of <code>ServeMux</code>,
now panic if an attempt is made to register the same pattern twice.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update the few programs that are affected except for
uses of <code>RawURL</code>, which must be fixed by hand.
</p>

<h3 id="image">The image package</h3>

<p>
The <a href="/pkg/image/"><code>image</code></a> package has had a number of
minor changes, rearrangements and renamings.
</p>

<p>
Most of the color handling code has been moved into its own package,
<a href="/pkg/image/color/"><code>image/color</code></a>.
For the elements that moved, a symmetry arises; for instance,
each pixel of an
<a href="/pkg/image/#RGBA"><code>image.RGBA</code></a>
is a
<a href="/pkg/image/color/#RGBA"><code>color.RGBA</code></a>.
</p>

<p>
The old <code>image/ycbcr</code> package has been folded, with some
renamings, into the
<a href="/pkg/image/"><code>image</code></a>
and
<a href="/pkg/image/color/"><code>image/color</code></a>
packages.
</p>

<p>
The old <code>image.ColorImage</code> type is still in the <code>image</code>
package but has been renamed
<a href="/pkg/image/#Uniform"><code>image.Uniform</code></a>,
while <code>image.Tiled</code> has been removed.
</p>

<p>
This table lists the renamings.
</p>

<table class="codetable" frame="border" summary="image renames">
<colgroup align="left" width="50%"></colgroup>
<colgroup align="left" width="50%"></colgroup>
<tr>
<th align="left">Old</th>
<th align="left">New</th>
</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>image.Color</td> <td>color.Color</td></tr>
<tr><td>image.ColorModel</td> <td>color.Model</td></tr>
<tr><td>image.ColorModelFunc</td> <td>color.ModelFunc</td></tr>
<tr><td>image.PalettedColorModel</td> <td>color.Palette</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>image.RGBAColor</td> <td>color.RGBA</td></tr>
<tr><td>image.RGBA64Color</td> <td>color.RGBA64</td></tr>
<tr><td>image.NRGBAColor</td> <td>color.NRGBA</td></tr>
<tr><td>image.NRGBA64Color</td> <td>color.NRGBA64</td></tr>
<tr><td>image.AlphaColor</td> <td>color.Alpha</td></tr>
<tr><td>image.Alpha16Color</td> <td>color.Alpha16</td></tr>
<tr><td>image.GrayColor</td> <td>color.Gray</td></tr>
<tr><td>image.Gray16Color</td> <td>color.Gray16</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>image.RGBAColorModel</td> <td>color.RGBAModel</td></tr>
<tr><td>image.RGBA64ColorModel</td> <td>color.RGBA64Model</td></tr>
<tr><td>image.NRGBAColorModel</td> <td>color.NRGBAModel</td></tr>
<tr><td>image.NRGBA64ColorModel</td> <td>color.NRGBA64Model</td></tr>
<tr><td>image.AlphaColorModel</td> <td>color.AlphaModel</td></tr>
<tr><td>image.Alpha16ColorModel</td> <td>color.Alpha16Model</td></tr>
<tr><td>image.GrayColorModel</td> <td>color.GrayModel</td></tr>
<tr><td>image.Gray16ColorModel</td> <td>color.Gray16Model</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>ycbcr.RGBToYCbCr</td> <td>color.RGBToYCbCr</td></tr>
<tr><td>ycbcr.YCbCrToRGB</td> <td>color.YCbCrToRGB</td></tr>
<tr><td>ycbcr.YCbCrColorModel</td> <td>color.YCbCrModel</td></tr>
<tr><td>ycbcr.YCbCrColor</td> <td>color.YCbCr</td></tr>
<tr><td>ycbcr.YCbCr</td> <td>image.YCbCr</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>ycbcr.SubsampleRatio444</td> <td>image.YCbCrSubsampleRatio444</td></tr>
<tr><td>ycbcr.SubsampleRatio422</td> <td>image.YCbCrSubsampleRatio422</td></tr>
<tr><td>ycbcr.SubsampleRatio420</td> <td>image.YCbCrSubsampleRatio420</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>image.ColorImage</td> <td>image.Uniform</td></tr>
</table>

<p>
The image package's <code>New</code> functions
(<a href="/pkg/image/#NewRGBA"><code>NewRGBA</code></a>,
<a href="/pkg/image/#NewRGBA64"><code>NewRGBA64</code></a>, etc.)
take an <a href="/pkg/image/#Rectangle"><code>image.Rectangle</code></a> as an argument
instead of four integers.
</p>

<p>
Finally, there are new predefined <code>color.Color</code> variables
<a href="/pkg/image/color/#Black"><code>color.Black</code></a>,
<a href="/pkg/image/color/#White"><code>color.White</code></a>,
<a href="/pkg/image/color/#Opaque"><code>color.Opaque</code></a>
and
<a href="/pkg/image/color/#Transparent"><code>color.Transparent</code></a>.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update almost all code affected by the change.
</p>

<h3 id="log_syslog">The log/syslog package</h3>

<p>
In Go 1, the <a href="/pkg/log/syslog/#NewLogger"><code>syslog.NewLogger</code></a>
function returns an error as well as a <code>log.Logger</code>.
</p>

<p>
<em>Updating</em>:
What little code is affected will be caught by the compiler and must be updated by hand.
</p>

<h3 id="mime">The mime package</h3>

<p>
In Go 1, the <a href="/pkg/mime/#FormatMediaType"><code>FormatMediaType</code></a> function
of the <code>mime</code> package has  been simplified to make it
consistent with
<a href="/pkg/mime/#ParseMediaType"><code>ParseMediaType</code></a>.
It now takes <code>"text/html"</code> rather than <code>"text"</code> and <code>"html"</code>.
</p>

<p>
<em>Updating</em>:
What little code is affected will be caught by the compiler and must be updated by hand.
</p>

<h3 id="net">The net package</h3>

<p>
In Go 1, the various <code>SetTimeout</code>,
<code>SetReadTimeout</code>, and <code>SetWriteTimeout</code> methods
have been replaced with
<a href="/pkg/net/#IPConn.SetDeadline"><code>SetDeadline</code></a>,
<a href="/pkg/net/#IPConn.SetReadDeadline"><code>SetReadDeadline</code></a>, and
<a href="/pkg/net/#IPConn.SetWriteDeadline"><code>SetWriteDeadline</code></a>,
respectively.  Rather than taking a timeout value in nanoseconds that
apply to any activity on the connection, the new methods set an
absolute deadline (as a <code>time.Time</code> value) after which
reads and writes will time out and no longer block.
</p>

<p>
There are also new functions
<a href="/pkg/net/#DialTimeout"><code>net.DialTimeout</code></a>
to simplify timing out dialing a network address and
<a href="/pkg/net/#ListenMulticastUDP"><code>net.ListenMulticastUDP</code></a>
to allow multicast UDP to listen concurrently across multiple listeners.
The <code>net.ListenMulticastUDP</code> function replaces the old
<code>JoinGroup</code> and <code>LeaveGroup</code> methods.
</p>

<p>
<em>Updating</em>:
Code that uses the old methods will fail to compile and must be updated by hand.
The semantic change makes it difficult for the fix tool to update automatically.
</p>

<h3 id="os">The os package</h3>

<p>
The <code>Time</code> function has been removed; callers should use
the <a href="/pkg/time/#Time"><code>Time</code></a> type from the
<code>time</code> package.
</p>

<p>
The <code>Exec</code> function has been removed; callers should use
<code>Exec</code> from the <code>syscall</code> package, where available.
</p>

<p>
The <code>ShellExpand</code> function has been renamed to <a
href="/pkg/os/#ExpandEnv"><code>ExpandEnv</code></a>.
</p>

<p>
The <a href="/pkg/os/#NewFile"><code>NewFile</code></a> function
now takes a <code>uintptr</code> fd, instead of an <code>int</code>.
The <a href="/pkg/os/#File.Fd"><code>Fd</code></a> method on files now
also returns a <code>uintptr</code>.
</p>

<p>
There are no longer error constants such as <code>EINVAL</code>
in the <code>os</code> package, since the set of values varied with
the underlying operating system. There are new portable functions like
<a href="/pkg/os/#IsPermission"><code>IsPermission</code></a>
to test common error properties, plus a few new error values
with more Go-like names, such as
<a href="/pkg/os/#ErrPermission"><code>ErrPermission</code></a>
and
<a href="/pkg/os/#ErrNoEnv"><code>ErrNoEnv</code></a>.
</p>

<p>
The <code>Getenverror</code> function has been removed. To distinguish
between a non-existent environment variable and an empty string,
use <a href="/pkg/os/#Environ"><code>os.Environ</code></a> or
<a href="/pkg/syscall/#Getenv"><code>syscall.Getenv</code></a>.
</p>


<p>
The <a href="/pkg/os/#Process.Wait"><code>Process.Wait</code></a> method has
dropped its option argument and the associated constants are gone
from the package.
Also, the function <code>Wait</code> is gone; only the method of
the <code>Process</code> type persists.
</p>

<p>
The <code>Waitmsg</code> type returned by
<a href="/pkg/os/#Process.Wait"><code>Process.Wait</code></a>
has been replaced with a more portable
<a href="/pkg/os/#ProcessState"><code>ProcessState</code></a>
type with accessor methods to recover information about the
process.
Because of changes to <code>Wait</code>, the <code>ProcessState</code>
value always describes an exited process.
Portability concerns simplified the interface in other ways, but the values returned by the
<a href="/pkg/os/#ProcessState.Sys"><code>ProcessState.Sys</code></a> and
<a href="/pkg/os/#ProcessState.SysUsage"><code>ProcessState.SysUsage</code></a>
methods can be type-asserted to underlying system-specific data structures such as
<a href="/pkg/syscall/#WaitStatus"><code>syscall.WaitStatus</code></a> and
<a href="/pkg/syscall/#Rusage"><code>syscall.Rusage</code></a> on Unix.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will drop a zero argument to <code>Process.Wait</code>.
All other changes will be caught by the compiler and must be updated by hand.
</p>

<h4 id="os_fileinfo">The os.FileInfo type</h4>

<p>
Go 1 redefines the <a href="/pkg/os/#FileInfo"><code>os.FileInfo</code></a> type,
changing it from a struct to an interface:
</p>

<pre>
    type FileInfo interface {
        Name() string       // base name of the file
        Size() int64        // length in bytes
        Mode() FileMode     // file mode bits
        ModTime() time.Time // modification time
        IsDir() bool        // abbreviation for Mode().IsDir()
        Sys() interface{}   // underlying data source (can return nil)
    }
</pre>

<p>
The file mode information has been moved into a subtype called
<a href="/pkg/os/#FileMode"><code>os.FileMode</code></a>,
a simple integer type with <code>IsDir</code>, <code>Perm</code>, and <code>String</code>
methods.
</p>

<p>
The system-specific details of file modes and properties such as (on Unix)
i-number have been removed from <code>FileInfo</code> altogether.
Instead, each operating system's <code>os</code> package provides an
implementation of the <code>FileInfo</code> interface, which
has a <code>Sys</code> method that returns the
system-specific representation of file metadata.
For instance, to discover the i-number of a file on a Unix system, unpack
the <code>FileInfo</code> like this:
</p>

<pre>
    fi, err := os.Stat("hello.go")
    if err != nil {
        log.Fatal(err)
    }
    // Check that it's a Unix file.
    unixStat, ok := fi.Sys().(*syscall.Stat_t)
    if !ok {
        log.Fatal("hello.go: not a Unix file")
    }
    fmt.Printf("file i-number: %d\n", unixStat.Ino)
</pre>

<p>
Assuming (which is unwise) that <code>"hello.go"</code> is a Unix file,
the i-number expression could be contracted to
</p>

<pre>
    fi.Sys().(*syscall.Stat_t).Ino
</pre>

<p>
The vast majority of uses of <code>FileInfo</code> need only the methods
of the standard interface.
</p>

<p>
The <code>os</code> package no longer contains wrappers for the POSIX errors
such as <code>ENOENT</code>.
For the few programs that need to verify particular error conditions, there are
now the boolean functions
<a href="/pkg/os/#IsExist"><code>IsExist</code></a>,
<a href="/pkg/os/#IsNotExist"><code>IsNotExist</code></a>
and
<a href="/pkg/os/#IsPermission"><code>IsPermission</code></a>.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/os\.Open/` `/}/`}}
-->    f, err := os.OpenFile(name, os.O_RDWR|os.O_CREATE|os.O_EXCL, 0600)
    if os.IsExist(err) {
        log.Printf(&#34;%s already exists&#34;, name)
    }</pre>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update code that uses the old equivalent of the current <code>os.FileInfo</code>
and <code>os.FileMode</code> API.
Code that needs system-specific file details will need to be updated by hand.
Code that uses the old POSIX error values from the <code>os</code> package
will fail to compile and will also need to be updated by hand.
</p>

<h3 id="os_signal">The os/signal package</h3>

<p>
The <code>os/signal</code> package in Go 1 replaces the
<code>Incoming</code> function, which returned a channel
that received all incoming signals,
with the selective <code>Notify</code> function, which asks
for delivery of specific signals on an existing channel.
</p>

<p>
<em>Updating</em>:
Code must be updated by hand.
A literal translation of
</p>
<pre>
c := signal.Incoming()
</pre>
<p>
is
</p>
<pre>
c := make(chan os.Signal)
signal.Notify(c) // ask for all signals
</pre>
<p>
but most code should list the specific signals it wants to handle instead:
</p>
<pre>
c := make(chan os.Signal)
signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT)
</pre>

<h3 id="path_filepath">The path/filepath package</h3>

<p>
In Go 1, the <a href="/pkg/path/filepath/#Walk"><code>Walk</code></a> function of the
<code>path/filepath</code> package
has been changed to take a function value of type
<a href="/pkg/path/filepath/#WalkFunc"><code>WalkFunc</code></a>
instead of a <code>Visitor</code> interface value.
<code>WalkFunc</code> unifies the handling of both files and directories.
</p>

<pre>
    type WalkFunc func(path string, info os.FileInfo, err error) error
</pre>

<p>
The <code>WalkFunc</code> function will be called even for files or directories that could not be opened;
in such cases the error argument will describe the failure.
If a directory's contents are to be skipped,
the function should return the value <a href="/pkg/path/filepath/#variables"><code>filepath.SkipDir</code></a>
</p>

<pre><!--{{code "/doc/progs/go1.go" `/STARTWALK/` `/ENDWALK/`}}
-->    markFn := func(path string, info os.FileInfo, err error) error {
        if path == &#34;pictures&#34; { <span class="comment">// Will skip walking of directory pictures and its contents.</span>
            return filepath.SkipDir
        }
        if err != nil {
            return err
        }
        log.Println(path)
        return nil
    }
    err := filepath.Walk(&#34;.&#34;, markFn)
    if err != nil {
        log.Fatal(err)
    }</pre>

<p>
<em>Updating</em>:
The change simplifies most code but has subtle consequences, so affected programs
will need to be updated by hand.
The compiler will catch code using the old interface.
</p>

<h3 id="regexp">The regexp package</h3>

<p>
The <a href="/pkg/regexp/"><code>regexp</code></a> package has been rewritten.
It has the same interface but the specification of the regular expressions
it supports has changed from the old "egrep" form to that of
<a href="http://code.google.com/p/re2/">RE2</a>.
</p>

<p>
<em>Updating</em>:
Code that uses the package should have its regular expressions checked by hand.
</p>

<h3 id="runtime">The runtime package</h3>

<p>
In Go 1, much of the API exported by package
<code>runtime</code> has been removed in favor of
functionality provided by other packages.
Code using the <code>runtime.Type</code> interface
or its specific concrete type implementations should
now use package <a href="/pkg/reflect/"><code>reflect</code></a>.
Code using <code>runtime.Semacquire</code> or <code>runtime.Semrelease</code>
should use channels or the abstractions in package <a href="/pkg/sync/"><code>sync</code></a>.
The <code>runtime.Alloc</code>, <code>runtime.Free</code>,
and <code>runtime.Lookup</code> functions, an unsafe API created for
debugging the memory allocator, have no replacement.
</p>

<p>
Before, <code>runtime.MemStats</code> was a global variable holding
statistics about memory allocation, and calls to <code>runtime.UpdateMemStats</code>
ensured that it was up to date.
In Go 1, <code>runtime.MemStats</code> is a struct type, and code should use
<a href="/pkg/runtime/#ReadMemStats"><code>runtime.ReadMemStats</code></a>
to obtain the current statistics.
</p>

<p>
The package adds a new function,
<a href="/pkg/runtime/#NumCPU"><code>runtime.NumCPU</code></a>, that returns the number of CPUs available
for parallel execution, as reported by the operating system kernel.
Its value can inform the setting of <code>GOMAXPROCS</code>.
The <code>runtime.Cgocalls</code> and <code>runtime.Goroutines</code> functions
have been renamed to <code>runtime.NumCgoCall</code> and <code>runtime.NumGoroutine</code>.
</p>

<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update code for the function renamings.
Other code will need to be updated by hand.
</p>

<h3 id="strconv">The strconv package</h3>

<p>
In Go 1, the
<a href="/pkg/strconv/"><code>strconv</code></a>
package has been significantly reworked to make it more Go-like and less C-like,
although <code>Atoi</code> lives on (it's similar to
<code>int(ParseInt(x, 10, 0))</code>, as does
<code>Itoa(x)</code> (<code>FormatInt(int64(x), 10)</code>).
There are also new variants of some of the functions that append to byte slices rather than
return strings, to allow control over allocation.
</p>

<p>
This table summarizes the renamings; see the
<a href="/pkg/strconv/">package documentation</a>
for full details.
</p>

<table class="codetable" frame="border" summary="strconv renames">
<colgroup align="left" width="50%"></colgroup>
<colgroup align="left" width="50%"></colgroup>
<tr>
<th align="left">Old call</th>
<th align="left">New call</th>
</tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Atob(x)</td> <td>ParseBool(x)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Atof32(x)</td> <td>ParseFloat(x, 32)§</td></tr>
<tr><td>Atof64(x)</td> <td>ParseFloat(x, 64)</td></tr>
<tr><td>AtofN(x, n)</td> <td>ParseFloat(x, n)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Atoi(x)</td> <td>Atoi(x)</td></tr>
<tr><td>Atoi(x)</td> <td>ParseInt(x, 10, 0)§</td></tr>
<tr><td>Atoi64(x)</td> <td>ParseInt(x, 10, 64)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Atoui(x)</td> <td>ParseUint(x, 10, 0)§</td></tr>
<tr><td>Atoui64(x)</td> <td>ParseUint(x, 10, 64)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Btoi64(x, b)</td> <td>ParseInt(x, b, 64)</td></tr>
<tr><td>Btoui64(x, b)</td> <td>ParseUint(x, b, 64)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Btoa(x)</td> <td>FormatBool(x)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Ftoa32(x, f, p)</td> <td>FormatFloat(float64(x), f, p, 32)</td></tr>
<tr><td>Ftoa64(x, f, p)</td> <td>FormatFloat(x, f, p, 64)</td></tr>
<tr><td>FtoaN(x, f, p, n)</td> <td>FormatFloat(x, f, p, n)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Itoa(x)</td> <td>Itoa(x)</td></tr>
<tr><td>Itoa(x)</td> <td>FormatInt(int64(x), 10)</td></tr>
<tr><td>Itoa64(x)</td> <td>FormatInt(x, 10)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Itob(x, b)</td> <td>FormatInt(int64(x), b)</td></tr>
<tr><td>Itob64(x, b)</td> <td>FormatInt(x, b)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Uitoa(x)</td> <td>FormatUint(uint64(x), 10)</td></tr>
<tr><td>Uitoa64(x)</td> <td>FormatUint(x, 10)</td></tr>
<tr>
<td colspan="2"><hr></td>
</tr>
<tr><td>Uitob(x, b)</td> <td>FormatUint(uint64(x), b)</td></tr>
<tr><td>Uitob64(x, b)</td> <td>FormatUint(x, b)</td></tr>
</table>
		
<p>
<em>Updating</em>:
Running <code>go</code> <code>fix</code> will update almost all code affected by the change.
<br>
§ <code>Atoi</code> persists but <code>Atoui</code> and <code>Atof32</code> do not, so
they may require
a cast that must be added by hand; the <code>go</code> <code>fix</code> tool will warn about it.
</p>


<h3 id="templates">The template packages</h3>

<p>
The <code>template</code> and <code>exp/template/html</code> packages have moved to 
<a href="/pkg/text/template/"><code>text/template</code></a> and
<a href="/pkg/html/template/"><code>html/template</code></a>.
More significant, the interface to these packages has been simplified.
The template language is the same, but the concept of "template set" is gone
and the functions and methods of the packages have changed accordingly,
often by elimination.
</p>

<p>
Instead of sets, a <code>Template</code> object
may contain multiple named template definitions,
in effect constructing
name spaces for template invocation.
A template can invoke any other template associated with it, but only those
templates associated with it.
The simplest way to associate templates is to parse them together, something
made easier with the new structure of the packages.
</p>

<p>
<em>Updating</em>:
The imports will be updated by fix tool.
Single-template uses will be otherwise be largely unaffected.
Code that uses multiple templates in concert will need to be updated by hand.
The <a href="/pkg/text/template/#examples">examples</a> in
the documentation for <code>text/template</code> can provide guidance.
</p>

<h3 id="testing">The testing package</h3>

<p>
The testing package has a type, <code>B</code>, passed as an argument to benchmark functions.
In Go 1, <code>B</code> has new methods, analogous to those of <code>T</code>, enabling
logging and failure reporting.
</p>

<pre><!--{{code "/doc/progs/go1.go" `/func.*Benchmark/` `/^}/`}}
-->func BenchmarkSprintf(b *testing.B) {
    <span class="comment">// Verify correctness before running benchmark.</span>
    b.StopTimer()
    got := fmt.Sprintf(&#34;%x&#34;, 23)
    const expect = &#34;17&#34;
    if expect != got {
        b.Fatalf(&#34;expected %q; got %q&#34;, expect, got)
    }
    b.StartTimer()
    for i := 0; i &lt; b.N; i++ {
        fmt.Sprintf(&#34;%x&#34;, 23)
    }
}</pre>

<p>
<em>Updating</em>:
Existing code is unaffected, although benchmarks that use <code>println</code>
or <code>panic</code> should be updated to use the new methods.
</p>

<h3 id="testing_script">The testing/script package</h3>

<p>
The testing/script package has been deleted. It was a dreg.
</p>

<p>
<em>Updating</em>:
No code is likely to be affected.
</p>

<h3 id="unsafe">The unsafe package</h3>

<p>
In Go 1, the functions
<code>unsafe.Typeof</code>, <code>unsafe.Reflect</code>,
<code>unsafe.Unreflect</code>, <code>unsafe.New</code>, and
<code>unsafe.NewArray</code> have been removed;
they duplicated safer functionality provided by
package <a href="/pkg/reflect/"><code>reflect</code></a>.
</p>

<p>
<em>Updating</em>:
Code using these functions must be rewritten to use
package <a href="/pkg/reflect/"><code>reflect</code></a>.
The changes to <a href="http://code.google.com/p/go/source/detail?r=2646dc956207">encoding/gob</a> and the <a href="http://code.google.com/p/goprotobuf/source/detail?r=5340ad310031">protocol buffer library</a>
may be helpful as examples.
</p>

<h3 id="url">The url package</h3>

<p>
In Go 1 several fields from the <a href="/pkg/net/url/#URL"><code>url.URL</code></a> type
were removed or replaced.
</p>

<p>
The <a href="/pkg/net/url/#URL.String"><code>String</code></a> method now
predictably rebuilds an encoded URL string using all of <code>URL</code>'s
fields as necessary. The resulting string will also no longer have
passwords escaped.
</p>

<p>
The <code>Raw</code> field has been removed. In most cases the <code>String</code>
method may be used in its place.
</p>

<p>
The old <code>RawUserinfo</code> field is replaced by the <code>User</code>
field, of type <a href="/pkg/net/url/#Userinfo"><code>*net.Userinfo</code></a>.
Values of this type may be created using the new <a href="/pkg/net/url/#User"><code>net.User</code></a>
and <a href="/pkg/net/url/#UserPassword"><code>net.UserPassword</code></a>
functions. The <code>EscapeUserinfo</code> and <code>UnescapeUserinfo</code>
functions are also gone.
</p>

<p>
The <code>RawAuthority</code> field has been removed. The same information is
available in the <code>Host</code> and <code>User</code> fields.
</p>

<p>
The <code>RawPath</code> field and the <code>EncodedPath</code> method have
been removed. The path information in rooted URLs (with a slash following the
schema) is now available only in decoded form in the <code>Path</code> field.
Occasionally, the encoded data may be required to obtain information that
was lost in the decoding process. These cases must be handled by accessing
the data the URL was built from.
</p>

<p>
URLs with non-rooted paths, such as <code>"mailto:<EMAIL>?subject=Hi"</code>,
are also handled differently. The <code>OpaquePath</code> boolean field has been
removed and a new <code>Opaque</code> string field introduced to hold the encoded
path for such URLs. In Go 1, the cited URL parses as:
</p>

<pre>
    URL{
        Scheme: "mailto",
        Opaque: "<EMAIL>",
        RawQuery: "subject=Hi",
    }
</pre>

<p>
A new <a href="/pkg/net/url/#URL.RequestURI"><code>RequestURI</code></a> method was
added to <code>URL</code>.
</p>

<p>
The <code>ParseWithReference</code> function has been renamed to <code>ParseWithFragment</code>.
</p>

<p>
<em>Updating</em>:
Code that uses the old fields will fail to compile and must be updated by hand.
The semantic changes make it difficult for the fix tool to update automatically.
</p>

<h2 id="cmd_go">The go command</h2>

<p>
Go 1 introduces the <a href="/cmd/go/">go command</a>, a tool for fetching,
building, and installing Go packages and commands. The <code>go</code> command
does away with makefiles, instead using Go source code to find dependencies and
determine build conditions. Most existing Go programs will no longer require
makefiles to be built.
</p>

<p>
See <a href="/doc/code.html">How to Write Go Code</a> for a primer on the
<code>go</code> command and the <a href="/cmd/go/">go command documentation</a>
for the full details.
</p>

<p>
<em>Updating</em>:
Projects that depend on the Go project's old makefile-based build
infrastructure (<code>Make.pkg</code>, <code>Make.cmd</code>, and so on) should
switch to using the <code>go</code> command for building Go code and, if
necessary, rewrite their makefiles to perform any auxiliary build tasks.
</p>

<h2 id="cmd_cgo">The cgo command</h2>

<p>
In Go 1, the <a href="/cmd/cgo">cgo command</a>
uses a different <code>_cgo_export.h</code>
file, which is generated for packages containing <code>//export</code> lines.
The <code>_cgo_export.h</code> file now begins with the C preamble comment,
so that exported function definitions can use types defined there.
This has the effect of compiling the preamble multiple times, so a
package using <code>//export</code> must not put function definitions
or variable initializations in the C preamble.
</p>

<h2 id="releases">Packaged releases</h2>

<p>
One of the most significant changes associated with Go 1 is the availability
of prepackaged, downloadable distributions.
They are available for many combinations of architecture and operating system
(including Windows) and the list will grow.
Installation details are described on the
<a href="/doc/install">Getting Started</a> page, while
the distributions themselves are listed on the
<a href="http://code.google.com/p/go/downloads/list">downloads page</a>.


</div>

<div id="footer">
Build version go1.0.1.<br>
Except as <a href="http://code.google.com/policies.html#restrictions">noted</a>,
the content of this page is licensed under the
Creative Commons Attribution 3.0 License,
and code is licensed under a <a href="/LICENSE">BSD license</a>.<br>
<a href="/doc/tos.html">Terms of Service</a> | 
<a href="http://www.google.com/intl/en/privacy/privacy-policy.html">Privacy Policy</a>
</div>

<script type="text/javascript">
(function() {
  var ga = document.createElement("script"); ga.type = "text/javascript"; ga.async = true;
  ga.src = ("https:" == document.location.protocol ? "https://ssl" : "http://www") + ".google-analytics.com/ga.js";
  var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(ga, s);
})();
</script>
</body>
<script type="text/javascript">
  (function() {
    var po = document.createElement('script'); po.type = 'text/javascript'; po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(po, s);
  })();
</script>
</html>

