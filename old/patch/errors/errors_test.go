// Copyright 2011 The Go Authors.  All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package errors_test

import (
	. "errors"
	"testing"
)

func TestNewEqual(t *testing.T) {
	// Different allocations should not be equal.
	if New("abc") == New("abc") {
		t.<PERSON>(`New("abc") == New("abc")`)
	}
	if New("abc") == New("xyz") {
		t.<PERSON>(`New("abc") == New("xyz")`)
	}

	// Same allocation should be equal to itself (not crash).
	err := New("jkl")
	if err != err {
		t.<PERSON>(`err != err`)
	}
}

func TestErrorMethod(t *testing.T) {
	err := New("abc")
	if err.<PERSON>rror() != "abc" {
		t.<PERSON><PERSON>(`New("abc").Error() = %q, want %q`, err.<PERSON>(), "abc")
	}
}
