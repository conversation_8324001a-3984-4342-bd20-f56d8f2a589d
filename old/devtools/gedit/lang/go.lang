<?xml version="1.0" encoding="UTF-8"?>
<!--

 Authors: <AUTHORS>

 This library is free software; you can redistribute it and/or
 modify it under the terms of the GNU Library General Public
 License as published by the Free Software Foundation; either
 version 2 of the License, or (at your option) any later version.

 This library is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 Library General Public License for more details.

 You should have received a copy of the GNU Library General Public
 License along with this library; if not, write to the
 Free Software Foundation, Inc., 59 Temple Place - Suite 330,
 Boston, MA 02111-1307, USA.

-->
<language id="go" _name="Go" version="2.0" _section="Sources">
    <metadata>
      <property name="mimetypes">text/x-go</property>
      <property name="globs">*.go;*.tgo</property>
      <property name="line-comment-start">//</property>
      <property name="block-comment-start">/*</property>
      <property name="block-comment-end">*/</property>
    </metadata>

    <styles>
        <style id="comment"           _name="Comment"             map-to="def:comment"/>
        <style id="error"             _name="Error"               map-to="def:error"/>
        <style id="string"            _name="String"              map-to="def:string"/>
        <style id="preprocessor"      _name="Preprocessor"        map-to="def:preprocessor"/>
        <style id="common-defines"    _name="Common Defines"      map-to="def:special-constant"/>
        <style id="included-file"     _name="Included File"       map-to="def:string"/>
        <style id="char"              _name="Character"           map-to="def:character"/>
        <style id="keyword"           _name="Keyword"             map-to="def:keyword"/>
        <style id="type"              _name="Data Type"           map-to="def:type"/>
        <style id="storage-class"     _name="Storage Class"       map-to="def:type"/>
        <style id="escaped-character" _name="Escaped Character"   map-to="def:special-char"/>
        <style id="floating-point"    _name="Floating point number" map-to="def:floating-point"/>
        <style id="decimal"           _name="Decimal number"      map-to="def:decimal"/>
        <style id="octal"             _name="Octal number"        map-to="def:base-n-integer"/>
        <style id="hexadecimal"       _name="Hexadecimal number"  map-to="def:base-n-integer"/>
        <style id="boolean"           _name="Boolean value"       map-to="def:boolean"/>
    </styles>

    <definitions>
        <define-regex id="escaped-character" extended="true">
            \\(                   # leading backslash
            [\\\"\'nrbtfav\?] |   # escaped character
            [0-7]{1,3} |          # one, two, or three octal digits
            x[0-9A-Fa-f]+         # 'x' followed by hex digits
            )
        </define-regex>

        <context id="go-proper">
            <include>
                <context id="comment" style-ref="comment" end-at-line-end="true">
                    <start>//</start>
                    <include>
                      <context ref="def:in-line-comment"/>
                    </include>
                </context>

                <context id="comment-multiline" style-ref="comment">
                    <start>/\*</start>
                    <end>\*/</end>
                    <include>
                        <context ref="def:in-comment"/>
                    </include>
                </context>

                <context id="close-comment-outside-comment" style-ref="error">
                    <match>\*/(?!\*)</match>
                </context>

                <context id="string" style-ref="string" end-at-line-end="true">
                    <start>"</start>
                    <end>"</end>
                    <include>
                        <context id="escaped-character" style-ref="escaped-character">
                            <match>\%{escaped-character}</match>
                        </context>
                        <context ref="def:line-continue"/>
                    </include>
                </context>

                <context id="string1" style-ref="string" end-at-line-end="false">
                    <start>`</start>
                    <end>`</end>
                    <include>
                        <context ref="escaped-character"/>
                        <context ref="def:line-continue"/>
                    </include>
                </context>

                <context id="char" style-ref="char">
                    <match>'(\%{escaped-character}|.)'</match>
                </context>

                <context id="float" style-ref="floating-point">
                    <match extended="true">
                        (?&lt;![\w\.])
                        ((\.[0-9]+ | [0-9]+\.[0-9]*) ([Ee][+-]?[0-9]*)? |
                         ([0-9]+[Ee][+-]?[0-9]*))
                        [fFlL]?
                        (?![\w\.])
                    </match>
                </context>

                <context id="hexadecimal" style-ref="hexadecimal">
                    <match extended="true">
                        (?&lt;![\w\.])
                        0[xX][a-fA-F0-9]+
                        (?![\w\.])
                    </match>
                </context>

                <context id="octal" style-ref="octal">
                    <match extended="true">
                        (?&lt;![\w\.])
                        0[0-7]+
                        (?![\w\.])
                    </match>
                </context>

                <context id="decimal" style-ref="decimal">
                    <match extended="true">
                        (?&lt;![\w\.])
                        [0-9]+
                        (?![\w\.])
                    </match>
                </context>

                <context id="boolean" style-ref="boolean">
                    <match extended="true">
                        (true|false)
                    </match>
                </context>

                <context id="keywords" style-ref="keyword">
                    <keyword>type</keyword>
                    <keyword>break</keyword>
                    <keyword>default</keyword>
                    <keyword>func</keyword>
                    <keyword>interface</keyword>
                    <keyword>select</keyword>
                    <keyword>case</keyword>
                    <keyword>defer</keyword>
                    <keyword>go</keyword>
                    <keyword>map</keyword>
                    <keyword>struct</keyword>
                    <keyword>chan</keyword>
                    <keyword>else</keyword>
                    <keyword>goto</keyword>
                    <keyword>package</keyword>
                    <keyword>switch</keyword>
                    <keyword>const</keyword>
                    <keyword>fallthrough</keyword>
                    <keyword>if</keyword>
                    <keyword>range</keyword>
                    <keyword>type</keyword>
                    <keyword>continue</keyword>
                    <keyword>for</keyword>
                    <keyword>import</keyword>
                    <keyword>return</keyword>
                    <keyword>var</keyword>
                    <keyword>this</keyword>
                    <keyword>len</keyword>
                    <keyword>cap</keyword>
                    <keyword>panic</keyword>
                    <keyword>recover</keyword>
                    <keyword>copy</keyword>
                    <keyword>append</keyword>
                </context>

                <context id="types" style-ref="type">
                    <keyword>uint8</keyword>
                    <keyword>uint16</keyword>
                    <keyword>uint32</keyword>
                    <keyword>uint64</keyword>
                    <keyword>int8</keyword>
                    <keyword>int16</keyword>
                    <keyword>int32</keyword>
                    <keyword>int64</keyword>
                    <keyword>float32</keyword>
                    <keyword>float64</keyword>
                    <keyword>byte</keyword>
                    <keyword>uint</keyword>
                    <keyword>int</keyword>
                    <keyword>float</keyword>
                    <keyword>uintptr</keyword>
                    <keyword>string</keyword>
                    <keyword>nil</keyword>
                    <keyword>bool</keyword>
                    <keyword>error</keyword>
                    <keyword>os.Error</keyword>
                    <keyword>os.File</keyword>
                    <keyword>io.Reader</keyword>
                    <keyword>io.ReadCloser</keyword>
                    <keyword>io.Writer</keyword>
                    <keyword>io.ReadWriter</keyword>
                    <keyword>io.ReaderAt</keyword>
                    <keyword>io.WriterAt</keyword>
                    <keyword>http.Request</keyword>
                    <keyword>http.ResponseWriter</keyword>
                    <keyword>rpc.Request</keyword>
                    <keyword>rpc.ResponseWriter</keyword>
                    <keyword>cc.ReadWriterAt</keyword>
                    <keyword>cc.SimpleCache</keyword>
                    <keyword>cc.SimpleIntCache</keyword>
                    <keyword>cc.Runner</keyword>
                    <keyword>cc.ChunkPool</keyword>
                    <keyword>cc.ChunkedFile</keyword>
                    <keyword>cc.LogFile</keyword>
                    <keyword>cc.LogReader</keyword>
                    <keyword>store.Putter</keyword>
                    <keyword>store.Getter</keyword>
                    <keyword>store.Interface</keyword>
                    <keyword>store.CachedStorage</keyword>
                    <keyword>ipc.ResponseWriter</keyword>
                    <keyword>ipc.Request</keyword>
                    <keyword>ipc.ServeMux</keyword>
                    <keyword>sync.Mutex</keyword>
                    <keyword>sync.RWMutex</keyword>
                    <keyword>testing.T</keyword>
                </context>
            </include>
        </context>

        <context id="go">
            <include>
                <context ref="go-proper"/>
            </include>
        </context>
    </definitions>
</language>
