# 银行转账待确认通知 - 拼接逻辑样式总结

## 🎯 实际组装的消息样式

### 示例1: 普通销售 (3个客户)
```
**您有 `3` 个客户的银行转账待确认，请及时确认**
>

>
>[12345](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=12345)

>
>[67890](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=67890)

>
>[11111](https://portal.qiniu.io/bo/financial/bank-transfer/confirmation?status=2&uid=11111)
```

### 示例2: 销售运营 (1个客户)
```
**您有 `1` 个客户的银行转账待确认，请及时确认**
>

>
>[44444](https://portal.qiniu.io/bo/financial/bank-transfer/admin?status=2&uid=44444)
```

## 🔧 代码逻辑

```go
// 模板定义
const workwxTemplate = `**您有 ` + "`%d`" + ` 个客户的银行转账待确认，请及时确认**
>
`

// 拼接逻辑
content := fmt.Sprintf(workwxTemplate, len(users))
for _, uid := range users {
    content += fmt.Sprintf(`
>
>[%d](%s&uid=%d)
`, uid, link, uid)
}
```

## 📐 格式结构

1. **标题行**: `**您有 `N` 个客户的银行转账待确认，请及时确认**`
2. **引用块开始**: `>`
3. **空行**
4. **每个用户**:
   - 空引用行: `>`
   - 用户链接: `>[用户ID](链接&uid=用户ID)`

## 🔗 链接规则

- **普通销售**: `/bo/financial/bank-transfer/confirmation?status=2&uid=用户ID`
- **销售运营**: `/bo/financial/bank-transfer/admin?status=2&uid=用户ID`

## 📱 企业微信效果

- ✅ 粗体标题
- ✅ 代码样式的数字
- ✅ 引用块样式
- ✅ 可点击的用户链接
- ✅ 整洁的垂直布局
