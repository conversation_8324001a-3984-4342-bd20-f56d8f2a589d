package mockacc

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestEnhancedInfoById(t *testing.T) {
	builtInUserIDS := []string{"root", "qboxtest", "qboxbucket", "fsq", "qboxquata"}
	sa := SimpleAccount{}
	for _, v := range builtInUserIDS {
		_, _, err := sa.EnhancedInfoById(v)
		assert.Nil(t, err)
	}

	invalidID := "invalidUserID"
	_, _, err := sa.EnhancedInfoById(invalidID)
	assert.NotNil(t, err)
}

func TestEnhancedInfoByUid(t *testing.T) {
	builtInUserUIDS := []uint32{********, *********, *********, **********, *********}
	sa := SimpleAccount{}
	for _, v := range builtInUserUIDS {
		_, _, err := sa.EnhancedInfoByUid(v)
		assert.Nil(t, err)
	}

	invalidUID := uint32(0)
	_, _, err := sa.EnhancedInfoByUid(invalidUID)
	assert.NotNil(t, err)
}

func TestCreateUserByPassword(t *testing.T) {
	sa := SimpleAccount{}
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
	ui, err := sa.CreateUserByPassword(email, "test")
	assert.Nil(t, err)
	assert.NotEqual(t, uint32(0), ui.Uid)
	assert.Equal(t, uint32(0), ui.ParentUid)
}

func TestCountChildrenByParentUid(t *testing.T) {
	parentUID := uint32(1)
	tempUser := UserInfo{
		ParentUid: parentUID,
	}
	SaInstance = append(SaInstance, &tempUser)
	sa := SimpleAccount{}
	cnt, err := sa.CountChildrenByParentUid(parentUID)
	assert.Nil(t, err)
	assert.Equal(t, 1, cnt.Count)
}

func TestChildrenByParentUid(t *testing.T) {
	parentUID := uint32(10)

	childEmail := "<EMAIL>"
	for i := 10; i < 20; i++ {
		tempUser := UserInfo{
			Uid:       uint32(i),
			Email:     childEmail,
			ParentUid: parentUID,
		}
		SaInstance = append(SaInstance, &tempUser)
	}
	sa := SimpleAccount{}
	children, err := sa.ChildrenByParentUid(parentUID)
	assert.Nil(t, err)
	assert.Equal(t, 10, len(children))

	for _, v := range children {
		assert.Equal(t, childEmail, v.Email)
	}
}

func TestSetUserType(t *testing.T) {

	newUtype := uint32(1024)

	for i := 10; i < 20; i++ {
		tempUser := UserInfo{
			Uid:   uint32(i),
			Email: fmt.Sprintf("<EMAIL>", i),
		}
		SaInstance = append(SaInstance, &tempUser)
	}
	sa := SimpleAccount{}
	ui, err := sa.SetUserType("root", newUtype)
	assert.Nil(t, err)
	assert.Equal(t, "root", ui.Id)

	info, _, _ := sa.EnhancedInfoById("root")
	assert.Equal(t, newUtype, info.Utype)
}

func TestUserUpdate(t *testing.T) {
	uid := uint32(**********)
	tempUser := UserInfo{
		Uid:   uid,
		Email: fmt.Sprintf("<EMAIL>", uid),
	}
	SaInstance = append(SaInstance, &tempUser)
	sa := SimpleAccount{}
	updateParam := UserUpdateParam{
		Uid:              uid,
		Utype:            uint32(4),
		ChildEmailDomain: "mockacc.test.io",
		CanGetChildKey:   true,
		Ctime:            time.Now().Unix(),
	}
	ui, err := sa.UserUpdate(updateParam)
	assert.Nil(t, err)
	assert.Equal(t, uint32(4), ui.Utype)
	assert.Equal(t, "mockacc.test.io", ui.ChildEmailDomain)
	assert.Equal(t, true, ui.CanGetChildKey)
	assert.NotEqual(t, 0, ui.Ctime)

	info, _, err := sa.EnhancedInfoByUid(uid)
	assert.Nil(t, err)
	assert.Equal(t, uint32(4), info.Utype)
	assert.Equal(t, "mockacc.test.io", info.ChildEmailDomain)
	assert.Equal(t, true, info.CanGetChildKey)
	assert.NotEqual(t, 0, info.Ctime)
}

func TestUserDisable(t *testing.T) {
	uid := uint32(**********)
	tempUser := UserInfo{
		Uid:   uid,
		Utype: uint32(4),
		Email: fmt.Sprintf("<EMAIL>", uid),
	}
	SaInstance = append(SaInstance, &tempUser)

	sa := SimpleAccount{}
	ui, err := sa.UserDisable(uid, "test", 1)
	assert.Nil(t, err)
	assert.Equal(t, 32772, ui.Utype) // 4 | 0x8000 = 32772
	assert.Equal(t, "test", ui.DisabledReason)
	assert.Equal(t, 1, ui.DisabledType)
}

func TestUserSetEmail(t *testing.T) {
	uid := uint32(**********)
	tempUser := UserInfo{
		Uid:   uid,
		Utype: uint32(4),
		Email: fmt.Sprintf("<EMAIL>", uid),
	}
	SaInstance = append(SaInstance, &tempUser)

	sa := SimpleAccount{}
	ui, err := sa.UserSetEmail(uid, "<EMAIL>")
	assert.Nil(t, err)
	assert.Equal(t, "<EMAIL>", ui.Email)
}
