#!/usr/bin/env bash

err() {
  echo -e "[$(date +'%Y-%m-%dT%H:%M:%S.%N%z')] FAIL: $@" >&2
  exit 1
}

info() {
  echo -e "[$(date +'%Y-%m-%dT%H:%M:%S.%N%z')] INFO: $@" >&1
}

warn() {
  echo -e "[$(date +'%Y-%m-%dT%H:%M:%S.%N%z')] WARN: $@" >&2
}

mv-auditlog () {
    mkdir -p ${ARTIFACTS}/run/auditlog
    if [[ -d ${QBOXROOT}/run/auditlog ]]; then
        mv ${QBOXROOT}/run/auditlog/* ${ARTIFACTS}/run/auditlog
    fi
    if [[ -d {QBOXROOT}/kodo/src/qiniu.com/kodo/s3apiv2/app/qboxs3apiv2/run/auditlog ]]; then
        mv {QBOXROOT}/kodo/src/qiniu.com/kodo/s3apiv2/app/qboxs3apiv2/run/auditlog/* ${ARTIFACTS}/run/auditlog
    fi
}

multi-wait () {
    PIDs=$1
    for pid in ${PIDs[@]}
    do
        wait ${pid}
    done
}

rename-test-result-file (){
    cd /logs/artifacts/
    files=$(ls /logs/artifacts/)
    echo "All test result files : "${files}
    if [[ "$files" = "" ]];then
        echo "Can not found any test result files"
        exit 1
    fi
    for f in $(ls /logs/artifacts/);do
        mv $f 'junit_'$f
    done
}

# start-common-service start service via the command, and will check the status as needed
# $1 service name
# $2 command to start this service
# $3 whether to wait for port listened, if specified, this function will
#    wait until the port is listened by this process within 10 seconds
# $4 app log path, if not specified, it is /run/$1.log by default
start-common-service () {
    local SERVICE=$1
    local START_CMD=$2
    local LISTEN_PORT=$3
    local APP_LOG=$4

    if [[ -z ${APP_LOG} ]]; then
       if ! mkdir -p ${ARTIFACTS}/run ; then
           warn "unable to mkdir: ${ARTIFACTS}/run"
       fi
       APP_LOG="${ARTIFACTS}/run/${SERVICE}.log"
    fi

    info "start ${SERVICE} via command: ${START_CMD} >> ${APP_LOG}"
    ${START_CMD} >> ${APP_LOG} 2>&1 &
    sleep 1

    local PID=$!
    #if [[ ${PID} -ne 0 ]]; then
      # echo "${PID}","${SERVICE}","${START_CMD}",${APP_LOG} >> ${SERVICE_INFO}
    #else
    #   err "failed to start ${SERVICE}, app log: \n `cat ${APP_LOG}`"
    #fi

    if [[ -n ${LISTEN_PORT} ]]; then
       local n=0
       local output=""
       local timeout=20
       until [[ ${n} -ge ${timeout} ]]
       do
           # check whether the target port is listened by specific process
           if [[ $(lsof -aPi -p ${PID} | grep "LISTEN" | grep ":${LISTEN_PORT}") ]]; then
              # By CarlJi: don't know how to put this sentence outside the "if" circle...
              output=$(lsof -aPi -p ${PID} | grep "LISTEN" | grep ":${LISTEN_PORT}")
              break
           fi

           n=$[${n}+1]
           sleep 1
       done

       if [[ ${n} -ge ${timeout} ]]; then
          err "failed to wait the port ${LISTEN_PORT} to be listened for service ${SERVICE} after more than ${timeout} seconds. Here is for your information:\n command to start this service: ${START_CMD}, service PID got: ${PID}, command to check service port: lsof -aPi -p ${PID} | grep LISTEN | grep :${LISTEN_PORT}, service app log: \n `cat ${APP_LOG}`"
       else
          info "service ${SERVICE} is running now, PID:${PID}, listen Port: \n ${output}"
       fi
    fi
}

run_memcached () {
    cd /home/<USER>
    rm -rf test-data
    start-common-service memcached "memcached -u nobody -v -p 11211 -l localhost" "/dev/null"
}

run_mongo () {
    cd /home/<USER>
    # remove test-data dir
    rm -rf test-data
    mkdir -p test-data/cfg0
    start-common-service mongoconfigsvr "mongod --configsvr --replSet cfg0 --dbpath test-data/cfg0 --logpath test-data/config1 --bind_ip localhost --port 27019" 27019
    mongo --port 27019 --eval 'rs.initiate({_id:"cfg0", configsvr: true, members: [{"_id":1, "host":"localhost:27019"}]})'

    # create replica set for shard 0
    mkdir -p test-data/db01
    mkdir -p test-data/db02
    mkdir -p test-data/db03

    start-common-service rs0db01 "mongod --shardsvr --replSet rs0 --dbpath test-data/db01 --logpath test-data/shard0 --bind_ip localhost --port 27018" 27018
    start-common-service rs0db02 "mongod --shardsvr --replSet rs0 --dbpath test-data/db02 --logpath test-data/shard1 --bind_ip localhost --port 27028" 27028
    start-common-service rs0db03 "mongod --shardsvr --replSet rs0 --dbpath test-data/db03 --logpath test-data/shard2 --bind_ip localhost --port 27038" 27038

    mongo --port 27018 --eval 'rs.initiate({_id : "rs0", members: [{ _id : 0, host : "localhost:27018" }, { _id : 1, host : "localhost:27028" }, { _id : 2, host : "localhost:27038" }]})'

    # create replica set for shard 1
    mkdir -p test-data/db11
    mkdir -p test-data/db12
    mkdir -p test-data/db13

    start-common-service rs1db11 "mongod --shardsvr --replSet rs1 --dbpath test-data/db11 --logpath test-data/shard3 --bind_ip localhost --port 27118" 27118
    start-common-service rs1db12 "mongod --shardsvr --replSet rs1 --dbpath test-data/db12 --logpath test-data/shard4 --bind_ip localhost --port 27128" 27128
    start-common-service rs1db13 "mongod --shardsvr --replSet rs1 --dbpath test-data/db13 --logpath test-data/shard5 --bind_ip localhost --port 27138" 27138

    mongo --port 27118 --eval 'rs.initiate({_id : "rs1", members: [{ _id : 0, host : "localhost:27118" }, { _id : 1, host : "localhost:27128" }, { _id : 2, host : "localhost:27138" }]})'

    # start mongos on default port 27017
    start-common-service mongos "mongos --configdb cfg0/localhost:27019 --logpath test-data/mongos1 --bind_ip localhost --port 26007" 26007

    # add shard
    mongo --host localhost --port 26007 --eval 'sh.addShard("rs0/localhost:27018")'
    mongo --host localhost --port 26007 --eval 'sh.addShard("rs1/localhost:27118")'
}