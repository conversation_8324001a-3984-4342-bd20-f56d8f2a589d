<div class="modal-header">
  <p ng-show="isCreate">套餐创建预览</p>
  <p ng-show="!isCreate">套餐更新预览</p>
</div>
<div class="modal-body">
    <div class="page-header">
      <h1 ng-show="isCreate">将创建下面的套餐</h1>
      <h1 ng-show="!isCreate">将更新下面的套餐</h1>
      <table class="table table-bordered table-hover table-striped seller-table">
          <thead>
            <tr>
              <td>分类</td>
              <td>名称</td>
              <td>描述</td>
              <td>附加描述</td>
              <td>状态</td>
              <td>原价</td>
              <td>现价</td>
              <td>代客下单</td>
              <td>上线时间</td>
              <td>下线时间</td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{{package.category}}</td>
              <td>{{package.name}}</td>
              <td>{{package.description}}</td>
              <td>{{package.sub_description}}</td>
              <td>{{PACKAGE_STATUS_TEXT_MAP[package.status]}}</td>
              <td>
                <div ng-show="package.supported_currency != SUPPORTED_CURRENCY.USD">¥{{package.fee}}</div>
                <div ng-show="package.supported_currency == SUPPORTED_CURRENCY.CNYUSD">或</div>
                <div ng-show="package.supported_currency != SUPPORTED_CURRENCY.CNY">${{package.dollar_fee}}</div>
              </td>
              <td>
                <div ng-show="package.supported_currency != SUPPORTED_CURRENCY.USD">¥{{package.c_fee}}</div>
                <div ng-show="package.supported_currency == SUPPORTED_CURRENCY.CNYUSD">或</div>
                <div ng-show="package.supported_currency != SUPPORTED_CURRENCY.CNY">${{package.dollar_c_fee}}</div>
              </td>
              <td>
                <span ng-if="package.ban_valet">禁止</span>
                <span ng-if="!package.ban_valet">允许</span>
              </td>
              <td>{{package.start_time}}</td>
              <td>{{package.end_time}}</td>
            </tr>
          </tbody>
      </table>
    </div>

    <div class="page-header">
      <h1>将包含以下商品</h1>
      <table class="table table-bordered table-hover table-striped seller-table">
        <thead>
            <tr>
              <td>商家ID</td>
              <td>商品ID</td>
              <td>商品名称</td>
              <td>商品数量</td>
              <td>商品使用时长</td>
              <td>原价</td>
              <td>现价</td>
            </tr>
        </thead>
        <tbody>
          <tr ng-repeat="product in packageProducts">
            <td>{{product.seller_id}}</td>
            <td>{{product.product_id}}</td>
            <td>{{product.product_name}}</td>
            <td>{{product.quantity}}</td>
            <td>
              <span ng-if="product.unit!=99"> {{product.duration}} </span>
              <span> {{product.unit|tradeProductUnit}} </span>
            </td>
            <td>
              <div ng-show="product.supported_currency != SUPPORTED_CURRENCY.USD">¥{{product.original_price}}</div>
              <div ng-show="product.supported_currency == SUPPORTED_CURRENCY.CNYUSD">或</div>
              <div ng-show="product.supported_currency != SUPPORTED_CURRENCY.CNY">${{product.dollar_original_price}}</div>
            </td>
            <td>
              <div ng-show="product.supported_currency != SUPPORTED_CURRENCY.USD">¥{{product.price}}</div>
              <div ng-show="product.supported_currency == SUPPORTED_CURRENCY.CNYUSD">或</div>
              <div ng-show="product.supported_currency != SUPPORTED_CURRENCY.CNY">${{product.dollar_price}}</div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

  <p style="color: red;" class="text-center">{{errMsg}}</p>
</div>
<div class="modal-footer text-center">
  <a href="" class="btn btn-default" ng-click="cancel()">返回继续修改</a>
  <a href="" class="btn btn-primary" ng-click="save()">已确认,可以提交</a>
</div>
