(->
  angular.module 'app.service'

  .factory 'EvmBillItem', ($http, $q, EvmPriceItem) ->
    _extractRangeDisplay = (priceRangeDef, value, digits = 0) ->
      billUnit = priceRangeDef.billUnit
      step = priceRangeDef.step

      value /= billUnit

      i = 0
      while value >= step && i < priceRangeDef.display.length - 1
        value /= step
        i += 1

      return value.toFixed(digits) + ' ' + priceRangeDef.display[i]

    class EvmDetailBase
      constructor: (@rawDetail, @price) ->
        priceRangeDef = @price.rangeDef()

        @allValue = @rawDetail.all_value
        if @price.priceType == 'MONTH_FEE' || @price.priceType == 'YEAR_FEE'
          @allValueDisplay = "#{@allValue} 个月"
        else
          @allValueDisplay = _extractRangeDisplay priceRangeDef, @allValue, 4
        @allMoney = @rawDetail.all_money

    class EvmPackageItem
      constructor: (@rawItem, priceRangeDef, priceUnit) ->
        @value = @rawItem.value
        @valueDisplay = _extractRangeDisplay priceRangeDef, priceUnit, @rawItem.value, 4
        @reduce = @rawItem.reduce
        @name = @rawItem.price.name


    class EvmRebateItem
      constructor: (@rawItem) ->
        @reduce = -@rawItem.change
        @name = @rawItem.price.name


    class EvmDiscountItem
      constructor: (@rawItem) ->
        @reduce = -@rawItem.change
        @name = @rawItem.price.name


    class EvmBillItem
      constructor: (@rawItem) ->
        @itemName = @rawItem.item
        @createAt = @rawItem.create_at
        @start = @rawItem.from
        @end = @rawItem.to
        @startDisplay = moment.unix(@start).format('YYYY-MM-DD HH:mm:ss')
        @endDisplay = moment.unix(@end).format('YYYY-MM-DD HH:mm:ss')
        @money = @rawItem.money
        @zone = @rawItem.zone
        @priceItem = new EvmPriceItem @itemName, @rawItem.detail.price.item.base
        @key = @rawItem.key
        @keyDisplay = @_parseKeyForDisplay @key

        self = @

        @baseDetail = new EvmDetailBase @rawItem.detail.item.base, self.priceItem

        @packages = _.map @rawItem.detail.item.packages, (rawPackage) ->
          new EvmPackageItem rawPackage, self.priceItem.rangeDef(), self.priceItem.unit

        @itemRebates = _.map @rawItem.detail.item.rebates, (rawRebate) ->
          new EvmRebateItem rawRebate

        @groupRebates = _.map @rawItem.detail.group.rebates, (rawRebate) ->
          new EvmRebateItem rawRebate

        @productRebates = _.map @rawItem.detail.all.rebates, (rawRebate) ->
          new EvmRebateItem rawRebate

        @itemDiscounts = _.map @rawItem.detail.item.discounts, (rawDiscount) ->
          new EvmDiscountItem rawDiscount

        @groupDiscounts = _.map @rawItem.detail.group.discounts, (rawDiscount) ->
          new EvmDiscountItem rawDiscount

      _parseKeyForDisplay: (key) ->
        switch @priceItem.itemDef().group
          when 'evm_compute' then return '主机'
          when 'evm_network' then return "#{key} Mbps"
          when 'evm_volume'
            parts = key.split(':')
            type = parts[0]
            size = parts[1]
            if type == 'ssd'
              return "性能型 #{size} GB"
            else if type == 'sata'
              return "容量型 #{size} GB"
            else
              return key
          when 'evm_snapshot' then return "#{key} GB"
          else return key

    return EvmBillItem
)()
