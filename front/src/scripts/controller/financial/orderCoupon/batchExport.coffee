angular.module 'app.controller'
  .controller 'FinancialExportOrderCouponBatchController', (
    $scope, $state, $stateParams, $q, newCouponMap, newCouponService,
    $modalInstance, notificationService, tradeService, $filter,
  ) ->
    $scope.isExporting  = false
    $scope.isDisabled = true
    $scope.couponBatchStatus = newCouponMap.couponBatchStatus
    $scope.couponBatchStatusMap = newCouponMap.couponBatchStatusMap
    $scope.couponBindMethodMap = newCouponMap.couponBindMethodMap
    $scope.couponBindMethod = newCouponMap.couponBindMethod
    $scope.couponPayModeScope = newCouponMap.couponPayModeScope
    $scope.couponPayModeScopeMap = newCouponMap.couponPayModeScopeMap
    $scope.couponTimePeriodType = newCouponMap.couponTimePeriodType
    $scope.couponBatchStateList = Object.keys($scope.couponBatchStatusMap).map (item) ->
      id: parseInt(item)
      label: $scope.couponBatchStatusMap[item]
    $scope.couponBindMethodList = Object.keys($scope.couponBindMethodMap).map (item) ->
      label = $scope.couponBindMethodMap[item]
      if item == '0'
        label = '全部'
      id: parseInt(item)
      label: label
    $scope.reasonTypeList = Object.keys(newCouponMap.reasonType).map (item) ->
      id: newCouponMap.reasonType[item].value
      label: newCouponMap.reasonType[item].display

    $scope.cancel = ->
      $modalInstance.dismiss 'cancel'

    $scope.initExport = ->
      if !$scope.id \
        && !$scope.name \
        && !$scope.status \
        && !$scope.start \
        && !$scope.end \
        && !$scope.reasonType \
        && !$scope.bindMethod
          $scope.isDisabled = true
      else
        $scope.stateText = $scope.couponBatchStatusMap[$scope.status]
        if $scope.start
          $scope.startText = moment($scope.start).format('YYYY-MM-DD')
        if $scope.end
          $scope.endText = moment($scope.end).format('YYYY-MM-DD')
        $scope.isDisabled = false

    $scope.initExport()

    $scope.exportBatch = ->
      if $scope.isExporting
        return
      $scope.isExporting = true

      if $scope.status == 0
        states = Object.keys(newCouponMap.couponBatchStatusMap).filter (item) ->
          item != '0'
      else
        states = [$scope.status]
      query =
        state: states
        page: 1
        page_size: 1000
      if $scope.id
        query['ids'] = [$scope.id]
      if $scope.name
        query.name = $scope.name
      if $scope.start
        query.start_time = moment($scope.start).format()
      if $scope.end
        query.end_time = moment($scope.end).format()
      if $scope.reasonType
        query.reason = [$scope.reasonType]
      if $scope.bindMethod
        query.bind_method = [$scope.bindMethod]

      newCouponService.listCouponBatchByCondsUnpaginated(query)
      .then (res) ->
        if res.coupon_batches
          transformData(res.coupon_batches)
          exportDatas = []
          res.coupon_batches.forEach (batch) ->
            if $scope.couponTimePeriodType[batch.time_period_type] ==
            $scope.couponTimePeriodType.COUPON_TIME_PERIOD_TYPE_CONST_DURATION
              effectStr = "自领取之日有效 #{batch.effect_days || 0} 天"
            else
              effectStr = batch.effectStart + '~' + batch.effectEnd

            exportDatas.push {
              '编号': batch.id
              '名称': batch.name
              '事由分类': batch.reason
              '申请方': batch.applicant
              '发放形式': batch.bindMethod
              '生效时间': effectStr
              '满减类型': batch.useCondition
              '抵用券面额': batch.coupon_money
              '发放数量': batch.batch_size
              '领取数量': batch.num_activated
              '使用数量': batch.num_used
              '适用范围': batch.coupon_scope_desc
            }

          ws = XLSX.utils.json_to_sheet(exportDatas)
          wb = XLSX.utils.book_new()
          XLSX.utils.book_append_sheet(wb, ws)
          wopts =
            bookType: 'csv'
            bookSST: false
            type: 'binary'

          wbout = XLSX.write(wb, wopts)

          toBuffer = (s) ->
            buf = new ArrayBuffer(s.length)
            view = new Uint8Array(buf)
            for item, i in s
              view[i] = s.charCodeAt(i) & 0xFF
            return buf

          saveAs(
            new Blob([toBuffer(wbout)], {type: ''}),
            "包年包月（订单）券批次列表-#{moment().format('YYYY-MM-DD')}.csv"
          )
      .catch (err) ->
        notificationService.error "获取列表失败！#{err.error}"

    transformData = (data) ->
      data.forEach (item) ->
        item.startTime = moment(item.start_time).format('YYYY-MM-DD HH:mm:ss')
        item.endTime = moment(item.end_time).format('YYYY-MM-DD HH:mm:ss')
        item.stateText = $scope.couponBatchStatusMap[$scope.couponBatchStatus[item.state]]
        item.bindMethod = $scope.couponBindMethodMap[$scope.couponBindMethod[item.bind_method]]
        item.applicant = newCouponMap.applicantType[item.applicant].display
        item.reason = newCouponMap.reasonType[item.reason].display
        item.payModeScope =
          $scope.couponPayModeScopeMap[$scope.couponPayModeScope[item.pay_mode_scope]]
        if item.is_multiple_use
          item.isMultipleUsed = '可多次使用'
        else
          item.isMultipleUsed = '仅一次使用'
        if item.threshold_money == '0' || !item.threshold_money
          item.useCondition = '无门槛'
        else
          item.useCondition =
            "满 #{(item.threshold_money / 10000).toFixed(2)} 元可用"

        if $scope.couponTimePeriodType[item.time_period_type] ==
        $scope.couponTimePeriodType.COUPON_TIME_PERIOD_TYPE_CONST_DURATION
          item.effectDays = "#{item.effect_days || 0} 天"
        else
          item.effectStart =
            moment(item.coupon_effect_time).format('YYYY-MM-DD HH:mm:ss')
          item.effectEnd =
            moment(item.coupon_dead_time).format('YYYY-MM-DD HH:mm:ss')
