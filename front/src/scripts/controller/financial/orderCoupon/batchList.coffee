angular.module 'app.controller'
  .controller 'financialOrderCouponBatchListController', (
    $scope, newCouponMap, $state, newCouponService, $modal,
    notificationService, modalService
  ) ->
    $scope.couponBatchStatus = newCouponMap.couponBatchStatus
    $scope.couponBatchStatusMap = newCouponMap.couponBatchStatusMap
    $scope.couponBindMethodMap = newCouponMap.couponBindMethodMap
    $scope.couponBindMethod = newCouponMap.couponBindMethod
    $scope.couponPayModeScope = newCouponMap.couponPayModeScope
    $scope.couponPayModeScopeMap = newCouponMap.couponPayModeScopeMap
    $scope.couponTimePeriodType = newCouponMap.couponTimePeriodType
    $scope.couponBatchStateList = Object.keys($scope.couponBatchStatusMap).map (item) ->
      id: parseInt(item)
      label: $scope.couponBatchStatusMap[item]
    $scope.couponBindMethodList = Object.keys($scope.couponBindMethodMap).map (item) ->
      label = $scope.couponBindMethodMap[item]
      if item == '0'
        label = '全部'
      id: parseInt(item)
      label: label
    $scope.reasonTypeList = Object.keys(newCouponMap.reasonType).map (item) ->
      id: newCouponMap.reasonType[item].value
      label: newCouponMap.reasonType[item].display
    $scope.name = ''
    $scope.page = 1
    $scope.status = 0
    $scope.start = ''
    $scope.end = ''
    $scope.noMore = false

    $scope.couponList = []

    $scope.resetSearch = () ->
      $scope.id = ''
      $scope.name = ''
      $scope.status = 0
      $scope.start = ''
      $scope.end = ''
      $scope.reasonType = ''
      $scope.bindMethod = ''

    $scope.beforeRender = ($view, $dates) ->
      timezoneOffset = 0
      # @if isSufy
      nowDate = new Date()
      timezoneOffset = nowDate.getTimezoneOffset()
      $dates.forEach (date) ->
        date.utcDateValue = date.utcDateValue - timezoneOffset * 60000
      # @endif

    $scope.exportBatch = () ->
      model = $modal.open
        templateUrl: 'templates/financial/orderCoupon/batchExport.html'
        controller: 'FinancialExportOrderCouponBatchController'
        size: 'lg'
        scope: $scope
      model.result.then ->
        return

    $scope.search = (page) ->
      if $scope.status == 0
        states = Object.keys(newCouponMap.couponBatchStatusMap).filter (item) ->
          item != '0'
      else
        states = [$scope.status]
      query =
        state: states
        page: page
        page_size: 20
      if $scope.id
        query['ids'] = [$scope.id]
      if $scope.name
        query['name'] = $scope.name
      if $scope.start
        query['start_time'] = moment($scope.start).format()
      if $scope.end
        query['end_time'] = moment($scope.end).format()
      if $scope.reasonType
        query['reason'] = [$scope.reasonType]
      if $scope.bindMethod
        query['bind_method'] = [$scope.bindMethod]

      $scope.couponList = []
      $scope.isLoading = true
      newCouponService.getList(query)
      .then (res) ->
        if res.coupon_batches
          $scope.couponList = res.coupon_batches
          if $scope.couponList.length < 20
            $scope.noMore = true
          else
            $scope.noMore = false
          $scope.page = page
          transformData($scope.couponList)
        else
          $scope.couponList = []
      .catch (err) ->
        notificationService.error "获取列表失败！#{err.error}"
      .finally () ->
        $scope.isLoading = false

    transformData = (data) ->
      data.forEach (item) ->
        item.startTime = moment(item.start_time).format('YYYY-MM-DD HH:mm:ss')
        item.endTime = moment(item.end_time).format('YYYY-MM-DD HH:mm:ss')
        item.stateText = $scope.couponBatchStatusMap[$scope.couponBatchStatus[item.state]]
        item.bindMethod = $scope.couponBindMethodMap[$scope.couponBindMethod[item.bind_method]]
        item.payModeScope =
        $scope.couponPayModeScopeMap[$scope.couponPayModeScope[item.pay_mode_scope]]
        if item.is_multiple_use
          item.isMultipleUsed = '可多次使用'
        else
          item.isMultipleUsed = '仅一次使用'
        if item.threshold_money == '0' || !item.threshold_money
          item.useCondition = '无门槛'
        else
          item.useCondition =
          "满 #{(item.threshold_money / 10000).toFixed(2)} 元可用"

        if $scope.couponTimePeriodType[item.time_period_type] ==
        $scope.couponTimePeriodType.COUPON_TIME_PERIOD_TYPE_CONST_DURATION
          item.effectDays = "#{item.effect_days || 0} 天"
        else
          item.effectStart =
          moment(item.coupon_effect_time).format('YYYY-MM-DD HH:mm:ss')
          item.effectEnd =
          moment(item.coupon_dead_time).format('YYYY-MM-DD HH:mm:ss')

    $scope.prevPage = () ->
      if $scope.page > 1
        $scope.search($scope.page - 1)

    $scope.nextPage = () ->
      if !$scope.noMore
        $scope.search($scope.page + 1)

    $scope.search($scope.page)

    transformData($scope.couponList)

    $scope.cancellation = (itemId) ->
      modalService.confirm(
        "<p class='coupon-batch-confirm-content'>
          <i class='fa fa-exclamation-triangle icon-warning'></i>
          &nbsp;&nbsp;确定作废抵用券批次？
        </p>
        <p class='coupon-batch-confirm-content'>
          作废后已发放的抵用券
          <span class='red'>将被冻结无法使用</span>，并且该批抵用券无法再发放，你确定作废吗？
        </p>
        "
      ).then () ->
        newCouponService.cancelBatchById(itemId)
        .then (res) ->
          notificationService.success '作废成功'
        .catch (error) ->
          notificationService.error '操作失败'
        .finally () ->
          $scope.search($scope.page)
