angular.module 'app.controller'
  .controller 'CreateOrEditCreditController', (
    $scope, $state, $stateParams, $modalInstance,
    notificationService, creditService, developerMgrService,
    isCreate, creditNeedEdit,
    CREDIT_PERIOD_UNIT, CREDIT_PERIOD_UNIT_MAP, CREDIT_PERIOD_MAX_VALUE_MAP, CREDIT_STATUS
  ) ->
    $scope.isCreate = isCreate
    $scope.creditPeriodUnitMap = CREDIT_PERIOD_UNIT_MAP
    $scope.titlePrefix = if !isCreate then '编辑' else '新增'

    $scope.credit = {
      id: '',
      uid: '',
      credit_line: '',
      credit_period: '',
      period_unit: CREDIT_PERIOD_UNIT.DAY
    }
    $scope.credit = if !isCreate then _.cloneDeep(creditNeedEdit) else $scope.credit
    originCreditInfo = if !isCreate then _.extend({}, creditNeedEdit) else _.extend({}, $scope.credit)

    $scope.shouldEditForbidSubmit = true
    $scope.creditPeriodMaxValue = CREDIT_PERIOD_MAX_VALUE_MAP[$scope.credit.period_unit]

    hasChange = (preObj, currentObj) ->
      if !preObj then return false
      for key of preObj
        if preObj[key] != currentObj[key]
          return true
      return false

    handleEditForbidSubmit = ->
      isCreditChange = hasChange(originCreditInfo, $scope.credit)
      if isCreditChange then return false else return true

    getCreditPeriodMaxValue = (periodUnit) ->
      if !periodUnit
        periodUnit = CREDIT_PERIOD_UNIT.DAY
      return CREDIT_PERIOD_MAX_VALUE_MAP[periodUnit]

    getCreateCreditOptions = (credit, fullName) ->
      options =
        uid: credit.uid
        customer_name: fullName
        credit_line: credit.credit_line
        credit_period: credit.credit_period
        period_unit: credit.period_unit
        status: CREDIT_STATUS.STATUS_UNACTIVATED
        remark: 'create from portal io credit page',
        auth_time: moment().valueOf() * 10000
      return options

    getDeveloperInfo = (uid) ->
      options =
        uid: uid
      return developerMgrService.getDeveloperInfo options

    $scope.periodUnitDropdownChange = ->
      $scope.creditPeriodMaxValue = getCreditPeriodMaxValue $scope.credit.period_unit

    $scope.$watch 'credit', ->
      $scope.shouldEditForbidSubmit = handleEditForbidSubmit()
    , true

    $scope.submit = ->
      # 信用额度保留两位小数并 * 10000
      credit_line = parseInt($scope.credit.credit_line * 100) * 100
      # 账期向下取整
      credit_period = Math.floor($scope.credit.credit_period)
      credit = _.extend({}, $scope.credit, {credit_line: credit_line, credit_period: credit_period})
      # 新建 && 编辑都调用 create 接口
      $scope.shouldEditForbidSubmit = true
      getDeveloperInfo(credit.uid).then (developer) ->
        fullName = developer.companyName || developer.fullName || developer.email
        options = getCreateCreditOptions credit, fullName
        creditService.createCredit(options).then (res) ->
          $scope.shouldEditForbidSubmit = false
          if res
            notificationService.success "#{$scope.titlePrefix}授信客户成功"
            $modalInstance.close 'ok'
          else
            notificationService.error "#{$scope.titlePrefix}授信客户失败"
        , (err) ->
          $scope.shouldEditForbidSubmit = false
          notificationService.error "#{$scope.titlePrefix}授信客户失败：" + err

    $scope.cancel = ->
      $modalInstance.dismiss 'cancel'
