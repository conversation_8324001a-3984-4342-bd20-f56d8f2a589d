angular.module 'app.controller'
  .controller 'FinancialRelationOverviewController', (
    $scope, $state, $stateParams
    userService, gaeaService, notificationService,
  ) ->

    $scope.relation = {}
    $scope.isPrimaryAccount = false
    $scope.creatorInfo = {}

    $scope.tabs =
      info: $state.current.name.indexOf('layout.financial.relation.relationShow') != -1
      children: $state.current.name.indexOf('layout.financial.relation.children') != -1
      auditRecord: $state.current.name.indexOf('layout.financial.relation.auditRecord') != -1

    $scope.initOverview = () ->
      gaeaService.frRelationGet $stateParams.uid
        .then (relation) ->
          $scope.relation = relation
          $scope.isPrimaryAccount = relation.parent_uid == 0

          userService.userInfoByUid relation.creator_id
            .then (creatorInfo) ->
              $scope.creatorInfo = creatorInfo
        , (err) ->
          notificationService.error "载入财务关系失败：#{err}"

    $scope.initOverview()
