// Code generated by scripts/gensdk; DO NOT EDIT.

package v3

import (
	"net/url"

	"github.com/qiniu/rpc.v1"
	"qbox.us/api/pay/pay"
)

type HandleBase struct {
	Host   string
	Client *rpc.Client
}

func NewHandleBase(host string, client *rpc.Client) *HandleBase {
	return &HandleBase{host, client}
}

type ModelBase struct {
	KindInfo
	Items map[pay.Item]ModelItemBasePrice `json:"items"`

	XXX_NoUnkeyedLiteral struct{} `json:"-"`
}

type ModelBaseWithZone struct {
	ModelBase
	Zone int `json:"zone"`

	XXX_NoUnkeyedLiteral struct{} `json:"-"`
}

type ModelItemBasePrice struct {
	Type            ItemBasePriceType `json:"type"`
	DataType        ItemDataType      `json:"data"`
	CountType       ItemCountType     `json:"count"`
	Unit            int64             `json:"unit"`
	Price           ModelRangePrice   `json:"price"`
	CumulativeCycle CumulativeType    `json:"cumulative_cycle"`
	BillPeriodType  BillPeriodType    `json:"bill_period_type"`
	// CurrencyType 该报价的币种（CNY/USD/etc.）
	CurrencyType string `json:"currency_type"`

	XXX_NoUnkeyedLiteral struct{} `json:"-"`
}

type ModelRangePrice struct {
	Type   RangePriceType    `json:"type"`
	Ranges []ModelPriceRange `json:"ranges"`

	XXX_NoUnkeyedLiteral struct{} `json:"-"`
}

type ModelPriceRange struct {
	Range int64  `json:"range"`
	Price string `json:"price"`

	XXX_NoUnkeyedLiteral struct{} `json:"-"`
}

func (r HandleBase) Add(logger rpc.Logger, req ModelBaseWithZone) (err error) {
	err = r.Client.CallWithJson(logger, nil, r.Host+"/v3/base/add", req)
	return
}

type ReqUpdatePublicPrice struct {
	// SplitTime 公开报价从何时开始分裂
	SplitTime pay.HNS `json:"split_time"`
	// NewBase 新公开报价
	NewBase ModelBaseWithZone `json:"new_base"`

	XXX_NoUnkeyedLiteral struct{} `json:"-"`
}

// UpdatePublicPrice 按指定时间点分裂并更新公开报价
//
// v3 不支持，仅 priceshim 支持
func (r HandleBase) UpdatePublicPrice(logger rpc.Logger, req ReqUpdatePublicPrice) (err error) {
	err = r.Client.CallWithJson(logger, nil, r.Host+"/v3/base/update/public/price", req)
	return
}

func (r HandleBase) Update(logger rpc.Logger, req ModelBaseWithZone) (err error) {
	err = r.Client.CallWithJson(logger, nil, r.Host+"/v3/base/update", req)
	return
}

// Get 获取基础价格
func (r HandleBase) Get(logger rpc.Logger, req ReqID) (resp ModelBaseWithZone, err error) {
	value := url.Values{}
	value.Add("id", req.ID)
	err = r.Client.Call(logger, &resp, r.Host+"/v3/base/get?"+value.Encode())
	return
}

func (r HandleBase) ListPrice(logger rpc.Logger, req ReqWithZones) (resp []BaseWithZone, err error) {
	value := url.Values{}
	value.Add("products", req.Products)
	value.Add("zones", req.Zones)
	if req.When != nil {
		value.Add("when", req.When.String())
	}
	err = r.Client.Call(logger, &resp, r.Host+"/v3/base/list/price?"+value.Encode())
	return
}
