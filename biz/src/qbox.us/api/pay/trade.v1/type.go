package trade

import (
	"net/url"
	"strconv"
	"strings"
	"time"

	"qbox.us/api/pay/pay"
	"qbox.us/api/pay/trade.v1/enums"
	"qbox.us/api/pay/trade.v1/models"
)

type CustomTime struct {
	time.Time
}

func (ct *CustomTime) ToString() string {
	return ct.Time.Format(time.RFC3339)
}

func (ct *CustomTime) UnmarshalJSON(b []byte) (err error) {
	if b[0] == '"' && b[len(b)-1] == '"' {
		b = b[1 : len(b)-1]
	}
	ct.Time, err = time.Parse(time.RFC3339, string(b))
	return
}

func (ct *CustomTime) MarshalJSON() ([]byte, error) {
	return []byte(ct.ToString()), nil
}

func (ct *CustomTime) ParseValue(str string) (err error) {
	ct.Time, err = time.Parse(time.RFC3339, str)
	return err
}

func (ct *CustomTime) Value() string {
	return ct.ToString()
}

type ReqSellerNew struct {
	Email    string `json:"email"`
	Title    string `json:"title"`
	Name     string `json:"name"`
	Callback string `json:"callback"`
}

type ReqSellerGet struct {
	Id    int64  `json:"id"`
	Email string `json:"email"`
}

type ReqSellerList struct {
	Page       int                  `json:"page"`
	PageSize   int                  `json:"page_size"`
	Status     *models.SellerStatus `json:"status"`
	UpdateFrom *CustomTime          `json:"update_from"`
	UpdateTo   *CustomTime          `json:"update_to"`
	ID         *int64               `json:"id"`
	Email      *string              `json:"email"`
}

type ReqSellerUpdate struct {
	Id       int64                `json:"id"`
	Title    *string              `json:"title"`
	Name     *string              `json:"name"`
	Email    *string              `json:"email"`
	Callback *string              `json:"callback"`
	Status   *models.SellerStatus `json:"status"`
}

type ReqProductNew struct {
	Email               string                  `json:"email"`
	Name                string                  `json:"name"`
	Model               string                  `json:"model"`
	SPU                 string                  `json:"spu"`
	Unit                models.ProductUnit      `json:"unit"`
	OriginalPrice       float64                 `json:"original_price"`
	Price               *float64                `json:"price"`
	DollarOriginalPrice int64                   `json:"dollar_original_price"`
	DollarPrice         int64                   `json:"dollar_price"`
	SupportedCurrency   enums.SupportedCurrency `json:"supported_currency"`
	Property            string                  `json:"property"`
	Description         string                  `json:"description"`
	ExpiresIn           uint64                  `json:"expires_in"`
	CategoryId          int64                   `json:"category_id"`
	SettlementMode      int                     `json:"settlement_mode"`
	Duration            uint                    `json:"duration"`         // 基础售卖的时长(unit*duration)
	IsLumpsum           bool                    `json:"is_lumpsum"`       // 是否一次性付款,即费用计算不受时长周期的影响
	BanValet            bool                    `json:"ban_valet"`        // 是否禁止代客下单(默认不禁止)
	ValetCategory       string                  `json:"valet_category"`   // 代客下单分类
	ApprovalEnabled     bool                    `json:"approval_enabled"` // 是否需要先审核再下单
	// AllowManualAccomplish 是否允许手动发货
	//
	// see https://jira.qiniu.io/browse/BO-18528
	AllowManualAccomplish bool        `json:"allow_manual_accomplish"`
	StartTime             *CustomTime `json:"start_time"`
	EndTime               *CustomTime `json:"end_time"`
}

type ReqProductGet struct {
	Id           int64  `json:"id"`
	ProductModel string `json:"model"`
}

type ReqSellerProduct struct {
	SellerId int64  `json:"seller_id"`
	Email    string `json:"email"`
	Status   int    `json:"status"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

type ReqProductList struct {
	ID                *int64                  `json:"id"`
	SellerId          *int64                  `json:"seller_id"`
	Model             *string                 `json:"model"`
	SPU               *string                 `json:"spu"`
	Unit              *models.ProductUnit     `json:"unit"`
	Status            *models.ProductStatus   `json:"status"`
	UpdateFrom        *CustomTime             `json:"update_from"`
	UpdateTo          *CustomTime             `json:"update_to"`
	Page              int                     `json:"page"`
	PageSize          int                     `json:"page_size"`
	FuzzyName         string                  `json:"fuzzy_name"`
	ValetCategory     *string                 `json:"valet_category"`
	SupportedCurrency enums.SupportedCurrency `json:"supported_currency"`
}

type ReqProductIdsGet struct {
	Ids []int64 `json:"ids"`
}

type ReqProductUpdate struct {
	Id                  int64                   `json:"id"` // required
	Name                *string                 `json:"name"`
	CategoryID          *int64                  `json:"category_id"`
	OriginalPrice       *float64                `json:"original_price"`
	Price               *float64                `json:"price"`
	DollarOriginalPrice *int64                  `json:"dollar_original_price"`
	DollarPrice         *int64                  `json:"dollar_price"`
	SupportedCurrency   enums.SupportedCurrency `json:"supported_currency"`
	Model               *string                 `json:"model"`
	SPU                 *string                 `json:"spu"`
	Property            *string                 `json:"property"`
	Unit                *models.ProductUnit     `json:"unit"`
	Description         *string                 `json:"description"`
	ExpiresIn           *uint64                 `json:"expires_in"`
	Status              *models.ProductStatus   `json:"status"`
	SettlementMode      int                     `json:"settlement_mode"`
	StartTime           *CustomTime             `json:"start_time"`
	EndTime             *CustomTime             `json:"end_time"`
	CancelOrder         bool                    `json:"cancel_order"`
	Duration            *uint                   `json:"duration"`         // 基础售卖的时长(unit*duration)
	IsLumpsum           *bool                   `json:"is_lumpsum"`       // 是否一次性付款,即费用计算不受时长周期的影响
	BanValet            *bool                   `json:"ban_valet"`        // 是否禁止代客下单(默认不禁止)
	ValetCategory       *string                 `json:"valet_category"`   // 代客下单分类
	ApprovalEnabled     *bool                   `json:"approval_enabled"` // 是否需要先审核再下单
	// AllowManualAccomplish 是否允许手动发货
	//
	// see https://jira.qiniu.io/browse/BO-18528
	AllowManualAccomplish *bool `json:"allow_manual_accomplish"`
}

type ReqProductRelease struct {
	Id int64 `json:"id"`
}

type ReqProductHistory struct {
	Id       int64 `json:"id"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
}

type ReqProductOrderNew struct {
	ProductId     int64    `json:"product_id"`
	Duration      uint     `json:"duration"`
	TimeDuration  uint64   `json:"time_duration"`
	Quantity      uint     `json:"quantity"`
	Property      *string  `json:"property"`
	Fee           *float64 `json:"fee"`
	Remark        string   `json:"remark"`         // po 备注,目前最大存储2000字符
	RemarkVisible bool     `json:"remark_visible"` // po.remark 是否对客户可见
}

type ReqOrderNew struct {
	Orders         []ReqProductOrderNew `json:"orders"`
	BuyerId        uint32               `json:"uid"`
	Memo           string               `json:"memo"`
	UnionOrderHash string               `json:"union_order_hash"`
}

type ReqOrderPay struct {
	OrderHash     string `json:"order_hash"`
	IgnoreExpired bool   `json:"ignore_expired"`
}

type ReqOrderGet struct {
	Id         int64  `json:"id"`
	OrderHash  string `json:"order_hash"`
	WithDetail bool   `json:"with_detail"`
}

type ReqOrderUpdate struct {
	Id          int64              `json:"id"`
	OrderHash   string             `json:"order_hash"`
	ActuallyFee *float64           `json:"actually_fee"`
	Status      models.OrderStatus `json:"status"`
}

// ReqOrderUpdatePrice update order price rquest
type ReqOrderUpdatePrice struct {
	OrderHash     string          `json:"order_hash"`
	ActuallyFee   float64         `json:"actually_fee"`
	POActuallyFee []POActuallyFee `json:"po_actually_fee"`
	IgnoreExpired bool            `json:"ignore_expired"`
	// NeedApprovedStats 是否需要历史审批数据(最低价、最近价、审批成本价)
	NeedApprovedStats bool `json:"need_approved_stats"`
}

// POActuallyFee 指定 po 级别改价
type POActuallyFee struct {
	POID        int64   `json:"po_id"`
	ActuallyFee float64 `json:"actually_fee"`
}

// ReqOrderList order list request
type ReqOrderList struct {
	WithDetail     bool                 `json:"with_detail"`
	ID             *int64               `json:"id"`
	PackageOrder   string               `json:"package_order"`
	OrderHash      *string              `json:"order_hash"`
	OrderHashList  []string             `json:"order_hash_list"`
	UnionOrderHash string               `json:"union_order_hash"`
	SellerId       *int64               `json:"seller_id"`
	BuyerId        *uint32              `json:"uid"`
	Status         []models.OrderStatus `json:"status"`
	CreateFrom     *CustomTime          `json:"create_from"`
	CreateTo       *CustomTime          `json:"create_to"`
	UpdateFrom     *CustomTime          `json:"update_from"`
	UpdateTo       *CustomTime          `json:"update_to"`
	PayFrom        *CustomTime          `json:"pay_from"`
	PayTo          *CustomTime          `json:"pay_to"`
	// ExpiredFilter 过滤掉所有 未完成订单里已过期 的订单
	ExpiredFilter   bool                      `json:"expired_filter"`
	Page            int                       `json:"page"`
	PageSize        int                       `json:"page_size"`
	ProductOrderIDs []int64                   `json:"product_order_ids"`
	ProductIDs      []int64                   `json:"product_ids"`
	PackageIDs      *string                   `json:"package_ids"`
	CurrencyType    string                    `json:"currency_type"`
	POStatus        models.ProductOrderStatus `json:"po_status"`
	FuzzyName       string                    `json:"fuzzy_name"`
	Hidden          *bool                     `json:"hidden"`
}

func (params *ReqOrderList) ToValues() url.Values {
	value := url.Values{}
	value.Add("with_detail", strconv.FormatBool(params.WithDetail))
	value.Add("page", strconv.FormatInt(int64(params.Page), 10))
	value.Add("page_size", strconv.FormatInt(int64(params.PageSize), 10))
	if params.ID != nil {
		value.Add("id", strconv.FormatInt(*params.ID, 10))
	}
	if params.PackageOrder != "" {
		value.Add("package_order", params.PackageOrder)
	}
	if params.UnionOrderHash != "" {
		value.Add("union_order_hash", params.UnionOrderHash)
	}
	if params.OrderHash != nil {
		value.Add("order_hash", *params.OrderHash)
	}
	if len(params.OrderHashList) > 0 {
		value.Add("order_hash_list", strings.Join(params.OrderHashList, ","))
	}
	if params.SellerId != nil {
		value.Add("seller_id", strconv.FormatInt(*params.SellerId, 10))
	}
	if params.BuyerId != nil {
		value.Add("buyer_id", strconv.FormatUint(uint64(*params.BuyerId), 10))
	}
	if l := len(params.Status); l > 0 {
		ss := make([]string, l)
		for i, s := range params.Status {
			ss[i] = strconv.Itoa(int(s))
		}
		value.Add("status", strings.Join(ss, ","))
	}

	if l := len(params.ProductOrderIDs); l > 0 {
		ps := make([]string, l)
		for i, s := range params.ProductOrderIDs {
			ps[i] = strconv.FormatInt(s, 10)
		}
		value.Add("product_order_ids", strings.Join(ps, ","))
	}

	if l := len(params.ProductIDs); l > 0 {
		ps := make([]string, l)
		for i, s := range params.ProductIDs {
			ps[i] = strconv.FormatInt(s, 10)
		}
		value.Add("product_ids", strings.Join(ps, ","))
	}

	if params.CreateFrom != nil {
		value.Add("create_from", params.CreateFrom.ToString())
	}
	if params.CreateTo != nil {
		value.Add("create_to", params.CreateTo.ToString())
	}
	if params.PayFrom != nil {
		value.Add("pay_from", params.PayFrom.ToString())
	}
	if params.PayTo != nil {
		value.Add("pay_to", params.PayTo.ToString())
	}
	if params.UpdateFrom != nil {
		value.Add("update_from", params.UpdateFrom.ToString())
	}
	if params.UpdateTo != nil {
		value.Add("update_to", params.UpdateTo.ToString())
	}

	if params.ExpiredFilter {
		value.Add("expired_filter", "true")
	}
	if params.PackageIDs != nil {
		value.Add("package_ids", *params.PackageIDs)
	}

	if params.CurrencyType != "" {
		value.Add("currency_type", params.CurrencyType)
	}
	if params.POStatus.Validate() {
		value.Add("po_status", params.POStatus.ToString())
	}
	if params.FuzzyName != "" {
		value.Add("fuzzy_name", params.FuzzyName)
	}
	if params.Hidden != nil {
		value.Add("hidden", strconv.FormatBool(*params.Hidden))
	}

	return value
}

type ReqUserOrderList struct {
	Uid        uint32 `json:"uid"`
	WithDetail bool   `json:"with_detail"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type ReqSellerOrderList struct {
	Email      string `json:"email"`
	SellerId   int64  `json:"seller_id"`
	WithDetail bool   `json:"with_detail"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

type RespOrderNew struct {
	OrderHash string `json:"order_hash"`
}

type ReqProductOrderAccomplish struct {
	Id        int64       `json:"id"`
	Property  string      `json:"property"`
	StartTime *CustomTime `json:"start_time"`
	Force     bool        `json:"force"`
}

type ReqProductOrderUpgrade struct {
	BuyerId   uint32      `json:"buyer_id"`
	CurrentId int64       `json:"current_id"`
	ProductId int64       `json:"product_id"`
	Quantity  *uint       `json:"quantity"`
	StartTime *CustomTime `json:"start_time"`
	Force     bool        `json:"bool"`
	Memo      string      `json:"memo"`
}

type ReqProductOrderList struct {
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	ID         *int64      `json:"id"`
	ProductId  *int64      `json:"product_id"`
	SellerId   *int64      `json:"seller_id"`
	BuyerId    *uint32     `json:"buyer_id"`
	OrderHash  *string     `json:"order_hash"`
	UpdateFrom *CustomTime `json:"update_from"`
	UpdateTo   *CustomTime `json:"update_to"`
}

type ReqProductOrderRefund struct {
	ProductOrderID int64  `json:"product_order_id"`
	Property       string `json:"property"`
}

type ReqProductOrderFailed struct {
	ProductOrderID int64 `json:"product_order_id"`
}

type ReqProductOrderRefundAck struct {
	OrderHash      string `json:"order_hash"`
	ProductOrderId int64  `json:"product_order_id"`
}

type ReqProductCategoryGet struct {
	CategoryId int64 `json:"category_id"`
}

type ReqProductCategoryListV2 struct {
	CategoryIDs []int64 `json:"category_ids,omitempty"`
	Page        int     `json:"page"`
	PageSize    int     `json:"page_size"`
}

type ReqProductCategoryGetByName struct {
	Name string `json:"name"`
}

type ReqOrderCalcprice struct {
	ProductID int64   `json:"product_id"`
	Price     float64 `json:"price"`
	Duration  uint    `json:"duration"`
	Quantity  uint    `json:"quantity"`
}

type RespOrderCalcprice struct {
	CFee float64 `json:"c_fee"`
	Fee  float64 `json:"fee"`
}

type ReqOrderCancel struct {
	Id        int64  `json:"id"`
	OrderHash string `json:"order_hash"`
}

type ReqCouponScopeNewOrUpdate struct {
	CouponID          string  `json:"coupon_id"`
	SellerIDsInclude  []int64 `json:"seller_ids_include"`
	SellerIDsExclude  []int64 `json:"seller_ids_exclude"`
	ProductIDsInclude []int64 `json:"product_ids_include"`
	ProductIDsExclude []int64 `json:"product_ids_exclude"`
	SkuIDsInclude     []int64 `json:"sku_ids_include"`
	SkuIDsExclude     []int64 `json:"sku_ids_exclude"`
}

type ReqCouponScopeGet struct {
	CouponID  string `json:"coupon_id"`
	IsExclude bool   `json:"is_exclude"`
}

// PackageProductBuy defines package product effect type
type PackageProductBuy struct {
	ProductID     int64  `json:"product_id"`
	EffectType    int    `json:"effect_type"`
	Remark        string `json:"remark"`         // po 备注,目前最大存储2000字符
	RemarkVisible bool   `json:"remark_visible"` // po.remark 是否对客户可见
}

// ReqPackageBuy defines packageBuy request
type ReqPackageBuy struct {
	PackageID int64               `json:"package_id"`
	Quantity  uint                `json:"quantity"`
	BuyerID   uint32              `json:"buyer_id"`
	Memo      string              `json:"memo"`
	Products  []PackageProductBuy `json:"products"`
}

// RespPackageBuy defines response of packageBuy
type RespPackageBuy struct {
	OrderHash   string   `json:"order_hash"` // Deprecated,remove in the future
	OrderHashes []string `json:"order_hashes"`
}

// ReqPackageNew request of create new trade's package
type ReqPackageNew struct {
	Package  ReqPackageInfo          `json:"package"`
	Products []ReqPackageProductInfo `json:"products"`
}

// ReqPackageInfo request of package part
type ReqPackageInfo struct {
	Fee               int64                   `json:"fee"`
	CFee              int64                   `json:"c_fee"`
	DollarFee         int64                   `json:"dollar_fee"`
	DollarCFee        int64                   `json:"dollar_c_fee"`
	SupportedCurrency enums.SupportedCurrency `json:"supported_currency"`
	Category          string                  `json:"category"`
	Name              string                  `json:"name"`
	Description       *string                 `json:"description"`
	SubDescription    *string                 `json:"sub_description"`
	Status            models.PackageStatus    `json:"status"`
	StartTime         time.Time               `json:"start_time"`
	EndTime           time.Time               `json:"end_time"`
	BanValet          bool                    `json:"ban_valet"`
}

// ReqPackageProductInfo request of package product part
type ReqPackageProductInfo struct {
	ProductId           int64  `json:"product_id"`
	Quantity            uint   `json:"quantity"`
	OriginalPrice       int64  `json:"original_price"`
	Price               int64  `json:"price"`
	DollarOriginalPrice int64  `json:"dollar_original_price"`
	DollarPrice         int64  `json:"dollar_price"`
	ProductName         string `json:"product_name"`
	Duration            uint   `json:"duration"`
	SellerID            int64  `json:"seller_id"`
}

// ReqPackageInfoUpdate request of update package's info
type ReqPackageInfoUpdate struct {
	Id                int64                   `json:"id"`
	Fee               *int64                  `json:"fee"`
	CFee              *int64                  `json:"c_fee"`
	DollarFee         *int64                  `json:"dollar_fee"`
	DollarCFee        *int64                  `json:"dollar_c_fee"`
	SupportedCurrency enums.SupportedCurrency `json:"supported_currency"`
	Category          *string                 `json:"category"`
	Name              *string                 `json:"name"`
	Description       *string                 `json:"description"`
	SubDescription    *string                 `json:"sub_description"`
	Status            *models.PackageStatus   `json:"status"`
	StartTime         *time.Time              `json:"start_time"`
	EndTime           *time.Time              `json:"end_time"`
	BanValet          *bool                   `json:"ban_valet"`
}

// ReqPackageList  defines package list request
type ReqPackageList struct {
	// PackageIds 通过套餐id 精确批量查询
	PackageIds []int64 `json:"package_ids"`
	// SellerId 通过商家搜索
	SellerId int64 `json:"seller_id"`
	// Status 通过状态搜索
	Status models.PackageStatus `json:"status"`
	// PackageName 通过套餐名模糊搜索
	PackageName string `json:"package_name"`
	// ProductId 搜索包含特定商品的套餐
	ProductId int64 `json:"product_id"`
	// ProductName 通过商品名称模糊搜索套餐
	ProductName string `json:"product_name"`
	// StartFrom 生效时间查询区间开始点
	StartFrom *CustomTime `json:"start_from"`
	// StartTo 生效时间查询区间结束点
	StartTo *CustomTime `json:"start_to"`
	// EndFrom 失效时间查询区间开始点
	EndFrom *CustomTime `json:"end_from"`
	// EndTo 失效时间查询区间结束点
	EndTo *CustomTime `json:"end_to"`
	// CreateFrom 创建时间查询开始点
	CreateFrom *CustomTime `json:"create_from"`
	// CreateTo 创建时间查询结束点
	CreateTo *CustomTime `json:"create_to"`
	// Page 分页,首页为0
	Page int `json:"page"`
	// PageSize 最大200
	PageSize int `json:"page_size"`
	// SupportedCurrency 支持的币种
	SupportedCurrency enums.SupportedCurrency `json:"supported_currency"`
}

// ReqPackagePrice defines package price request
type ReqPackagePrice struct {
	BuyerId   uint32 `json:"buyer_id"`
	PackageId int64  `json:"package_id"`
	Quantity  uint   `json:"quantity"`
}

// RespPackagePrice defines response of package price
type RespPackagePrice struct {
	CFee float64 `json:"c_fee"`
	Fee  float64 `json:"fee"`
}

// ReqPackageProducts package product request
type ReqPackageProducts struct {
	PackageId int64 `json:"package_id"`
}

// Validate validates package product request
func (r *ReqPackageProducts) Validate() bool {
	return r.PackageId > 0
}

// RespPackageProducts defines package products response
type RespPackageProducts struct {
	Products []PackageProductEntry `json:"products"`
}

// PackageProductEntry package product entry
type PackageProductEntry struct {
	PackageId           int64              `json:"package_id"`
	ProductId           int64              `json:"product_id"`
	Price               int64              `json:"price"`
	OriginalPrice       int64              `json:"original_price"`
	DollarPrice         int64              `json:"dollar_price"`
	DollarOriginalPrice int64              `json:"dollar_original_price"`
	Name                string             `json:"name"`
	Duration            uint               `json:"duration"`
	Unit                models.ProductUnit `json:"unit"`
	Quantity            uint               `json:"quantity"`
	SellerId            int64              `json:"seller_id"`
	SellerName          string             `json:"seller_name"`
	Specification       string             `json:"specification"`
	Zone                int                `json:"zone"`
	RespackId           string             `json:"respack_id"`
}

// ReqProductOrderDetail po detail request
type ReqProductOrderDetail struct {
	OrderHash string `json:"order_hash"`
}

// RespProductOrderDetail po detail resp
type RespProductOrderDetail struct {
	PODetails []ProductOrderDetail `json:"po_details"`
}

// ProductOrderDetail  product order detail
type ProductOrderDetail struct {
	POId    int64                    `json:"po_id"`
	Details []ProductOrderDetailItem `json:"details"`
}

// ProductOrderDetailItem item
type ProductOrderDetailItem struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// ReqCouponAvailableV3 request new-coupon available
type ReqCouponAvailableV3 struct {
	UID       uint32 `json:"uid"`
	OrderHash string `json:"order_hash"`
}

// RespCouponAvailableV3 response new-coupon available
type RespCouponAvailableV3 struct {
	Selected []string    `json:"selected"`
	Coupons  []PayCoupon `json:"coupons"`
}

// ReqCouponAllocate request of allocate coupon
type ReqCouponAllocate struct {
	UID            uint32   `json:"uid"`
	UnionOrderHash string   `json:"union_order_hash"`
	CouponCodes    []string `json:"coupon_codes"`
}

// RespCouponAllocate response of allocate coupon
type RespCouponAllocate struct {
	DeductedFee int64             `json:"deducted_fee"` // 优惠券可以抵扣的金额
	PayableFee  int64             `json:"payable_fee"`  // 除优惠券之外剩余的应付金额
	Allocated   []AllocatedCoupon `json:"allocated"`    // 分配情况
}

// ReqCouponAvailable request new-coupon available
type ReqCouponAvailable struct {
	UID            uint32 `json:"uid"`
	UnionOrderHash string `json:"union_order_hash"`
}

// RespCouponAvailable response available coupon
type RespCouponAvailable struct {
	Allocated []AllocatedCoupon `json:"allocated"`
	Coupons   []PayCoupon       `json:"coupons"`
}

// AllocatedCoupon allocate coupon
type AllocatedCoupon struct {
	UnionOrderHash string `json:"union_order_hash"` // order.union_order_hash
	OrderHash      string `json:"order_hash"`       // order.order_hash
	ProductOrderId int64  `json:"po_id"`            // product_order.id
	CouponCode     string `json:"coupon_code"`      // 券 code
	CouponMoney    int64  `json:"coupon_money"`     // 券额
	BoundMoney     int64  `json:"bound_money"`      // 绑定的金额
	CFee           int64  `json:"c_fee"`            // order.po.c_fee
}

// PayCoupon pay coupon, part of pay-sdk.CouponDetail
type PayCoupon struct {
	CouponCode string `json:"coupon_code"`
	Name       string `json:"name"`
	// 总值，万分之一元
	Money int64 `json:"money"`
	// 余额，万分之一元
	Balance        int64  `json:"balance"`
	EffectTime     int64  `json:"effect_time"`
	DeadTime       int64  `json:"dead_time"`
	ThresholdMoney int64  `json:"threshold_money"`
	Description    string `json:"description"`
	PayModeScope   int32  `json:"pay_mode_scope"`
	ScopeDesc      string `json:"scope_desc"`
	IsMultipleUse  bool   `json:"is_multiple_use"`
}

// ReqOrderCouponUsed coupon used
type ReqOrderCouponUsed struct {
	OrderHash string `json:"order_hash"`
}

// ReqRespackUser request respack user
type ReqRespackUser struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

// RespRespackUser resp respack user
type RespRespackUser struct {
	Uids []uint32 `json:"uids"`
}

// RespPackageDetail resp packageDetail
type RespPackageDetail struct {
	Package         models.Package        `json:"package"`
	PackageProducts []PackageProductEntry `json:"package_products"`
}

// ReqUserSellers request user sellers
type ReqUserSellers struct {
	Uid uint32 `json:"uid"`
}

// ReqUnionOrderCancel cancel union order
type ReqUnionOrderCancel struct {
	UnionOrderHash string `json:"union_order_hash"`
}

// ReqUnionOrderPay request union order pay
type ReqUnionOrderPay struct {
	// pay section
	PaymentType    string   `json:"payment_type"`
	Uid            uint32   `json:"uid"`
	UnionOrderHash string   `json:"union_order_hash"`
	CouponCodes    []string `json:"coupon_codes"`
	IgnoreExpired  bool     `json:"ignore_expired"`

	// recharge section
	RechargeExcode string    `json:"recharge_excode"`
	RechargeType   string    `json:"recharge_type"`
	RechargeUID    uint32    `json:"recharge_uid"`
	RechargeMoney  pay.Money `json:"recharge_money"`
	RechargeDesc   string    `json:"recharge_desc"`
}

// RespUnionOrderPay union order pay response
type RespUnionOrderPay struct {
	Message        string  `json:"message"`
	RemainAmount   float64 `json:"remain_amount"` // 剩余待支付金额
	UnionOrderHash string  `json:"union_order_hash"`
	PayMode        string  `json:"pay_mode"` // 支付方式
}

// RespUnionOrderDeduct union order deduct response
type RespUnionOrderDeduct struct {
	TxnIds        []string `json:"txn_ids"`         // po粒度的扣费流水列表,其excode=po.id
	RechargeTxnID string   `json:"recharge_txn_id"` // 这笔指定支付对应的钱包的充值流水id,其excode=portal-v4.transaction.GenRechargeExcode()
}

// ReqProductExtraInfo request product extra info
type ReqProductExtraInfo struct {
	ProductId int64 `json:"product_id"`
}

// ReqProductInfo request product info
type ReqProductInfo struct {
	ProductIds []int64 `json:"product_ids"`
}

// ReqOrderMixedNew 混合下单接口参数
type ReqOrderMixedNew struct {
	Uid   uint32        `json:"uid"`
	Goods []*MixedGoods `json:"goods"`
}

// MixedGoods 混合下单的 goods 结构体
type MixedGoods struct {
	GoodsType     enums.GoodsType  `json:"goods_type"`     // 商品种类：product、package
	GoodsId       int64            `json:"goods_id"`       // 具体id
	Duration      uint             `json:"duration"`       // 时长,对goods_type=package无效
	Quantity      uint             `json:"quantity"`       // 数量
	EffectType    enums.EffectType `json:"effect_type"`    // 生效时间
	Property      string           `json:"property"`       // 自定义属性
	Memo          string           `json:"memo"`           // 订单备注
	Remark        string           `json:"remark"`         // po 备注，目前最大存储 2000 字符
	RemarkVisible bool             `json:"remark_visible"` // po.remark 是否对客户可见
	CFee          *uint64          `json:"c_fee"`          // 自定义费用
	OriginalFee   *uint64          `json:"original_fee"`   // 自定义的po 的原价
	PromotionCode string           `json:"promotion_code"` // 促销 code
}

// RespOrderMixedNew 混合下单返回体
type RespOrderMixedNew struct {
	UnionOrderHash string   `json:"union_order_hash"`
	OrderHashes    []string `json:"order_hashes"`
}
