// Code generated by scripts/gensdk; DO NOT EDIT.

package wallet

import (
	"net/url"
	"strconv"

	"github.com/qiniu/rpc.v1"
)

type HandleBill struct {
	Host   string
	Client *rpc.Client
}

func NewHandleBill(host string, client *rpc.Client) *HandleBill {
	return &HandleBill{host, client}
}

// ListUndeductMonthBasebills 列出所有已出外部账单但未扣费的 basebills
func (r HandleBill) ListUndeductMonthBasebills(logger rpc.Logger, req ReqUID) (resp []ModelBaseBill, err error) {
	value := url.Values{}
	value.Add("uid", strconv.FormatUint(uint64(req.UID), 10))
	err = r.Client.Call(logger, &resp, r.Host+"/v3/bill/list/undeduct/month/basebills?"+value.Encode())
	return
}

// ListUndeductMonthCustombills 列出所有已出外部账单但未扣费的 custombills
func (r HandleBill) ListUndeductMonthCustombills(logger rpc.Logger, req ReqUID) (resp []ModelCustomBill, err error) {
	value := url.Values{}
	value.Add("uid", strconv.FormatUint(uint64(req.UID), 10))
	err = r.Client.Call(logger, &resp, r.Host+"/v3/bill/list/undeduct/month/custombills?"+value.Encode())
	return
}

// BillOverview 返回当前用户账单概览
func (r HandleBill) BillOverview(logger rpc.Logger, req ReqUID) (resp ModelBillOverview, err error) {
	value := url.Values{}
	value.Add("uid", strconv.FormatUint(uint64(req.UID), 10))
	err = r.Client.Call(logger, &resp, r.Host+"/v3/bill/bill/overview?"+value.Encode())
	return
}

// ListEffectiveBasebills 按条件列出内部账单，考虑重出账（补充账单）的情况
func (r HandleBill) ListEffectiveBasebills(logger rpc.Logger, req ReqUIDAndRangeSecond) (resp []ModelBaseBill, err error) {
	value := url.Values{}
	value.Add("uid", strconv.FormatUint(uint64(req.UID), 10))
	value.Add("product", req.Product.ToString())
	if req.Zone != nil {
		value.Add("zone", strconv.FormatInt(int64(*req.Zone), 10))
	}
	value.Add("from", req.From.String())
	value.Add("to", req.To.String())
	err = r.Client.Call(logger, &resp, r.Host+"/v3/bill/list/effective/basebills?"+value.Encode())
	return
}

// ListEffectiveCustombills 按条件列出自定义账单，考虑重出账（补充账单）的情况
func (r HandleBill) ListEffectiveCustombills(logger rpc.Logger, req ReqUIDAndRangeSecond) (resp []ModelCustomBill, err error) {
	value := url.Values{}
	value.Add("uid", strconv.FormatUint(uint64(req.UID), 10))
	value.Add("product", req.Product.ToString())
	if req.Zone != nil {
		value.Add("zone", strconv.FormatInt(int64(*req.Zone), 10))
	}
	value.Add("from", req.From.String())
	value.Add("to", req.To.String())
	err = r.Client.Call(logger, &resp, r.Host+"/v3/bill/list/effective/custombills?"+value.Encode())
	return
}

// HasUsageForOrderRule 为规则引擎设计的某个用户某个计费项历史上是否有量的接口
//
// NOTE: 因为接口主要设计为适应规则引擎的限制，所以不推荐其他场景使用。
func (r HandleBill) HasUsageForOrderRule(logger rpc.Logger, req ReqHasUsageForOrderRule) (resp RespHasUsageForOrderRule, err error) {
	value := url.Values{}
	value.Add("uid", strconv.FormatUint(uint64(req.UID), 10))
	value.Add("item", req.Item)
	err = r.Client.Call(logger, &resp, r.Host+"/v3/bill/has/usage/for/order/rule?"+value.Encode())
	return
}
