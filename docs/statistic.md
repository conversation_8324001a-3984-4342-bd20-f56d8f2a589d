单用户各产品线计量查询接口
=======
##通用接口
```
POST /api/stat
```
####通用输入:
```
* uid: 用户id
* email: 用户邮箱
* begin: 起始日期字符串，毫秒，闭区间
* end: 结束日期字符串，毫秒，开区间
* g: 时间粒度，支持day
* item: 基于pay.Item定义
```
####通用返回:
```
{
    "code": 200,
    "data": {
        "times": [time1, time2, time3, ...],
        "datas": [serie1, serie2, ...]
    }
}
```
## 空间使用量
请求参数:
* item: space
* extra:{"zone":"z1"}(必填)

  * z0:宁波
  * z1:昌平
  * na0:北美
  * z2:华南

```
{"item":"space","begin":"1451577600000","end":"1451664000000","uid":1338989962,"extra":{"zone":"z1"}}
```
返回:
```
{
  "code": 200,
  "data": {
    "times": [
      1451577600,
      1451664000
    ],
    "datas": [
      1000,
      2000
    ]
  }
}
```
## 源站流量
请求参数:
* item: transfer_origin
* extras: zone 可选(z1北京，z0宁波)

```
{"item":"transfer_origin","begin":"1451577600000","end":"1451664000000","uid":1338989962}
```
返回:
```
{
  "code": 200,
  "data": {
    "times": [
      1451577600,
      1451664000
    ],
    "datas": [
      1073741824,
      1073741824
    ]
  }
}
```
## API GET/PUT
请求参数:
* item: api_get/api_put
* extras: zone 可选(z1北京，z0宁波)

```
{"item":"api_put","begin":"1451577600000","end":"1451664000000","uid":1338989962}
```
返回:
```
{
  "code": 200,
  "data": {
    "times": [
      1451577600,
      1451664000
    ],
    "datas": [
      10000,
      10000
    ]
  }
}
```
## PILI UP/DOWN
请求参数:
* item: pili_up/pili_down
* extras: zone 可选(z1北京，z0宁波)

```
{"item":"pili_up","begin":"1451577600000","end":"1451664000000","uid":1338989962}
```
返回:
```
{
  "code": 200,
  "data": {
    "times": [
      1448899200,
      1448985600
    ],
    "datas": [
      100000000,
      100000000
    ]
  }
}
```
## MPS/MARKET
请求参数:
* item : 
    + mps:sd
    + mps:hd
    + mps:audio
    + mps:imageAve
    + mps:vframe
    + mps:avinfo
    + mps:pm3u8
    + mps:avconcat
    + mps:sd240
    + mps:sd480
    + market:tupu:nrop:certain
    + market:tupu:nrop:depend
    + market:tupu:adv:certain
    + market:tupu:adv:depend
    + market:tupu:video:certain
    + market:tupu:video:depend
    + market:tupu:video:unified
    + market:yifang:convert:word
    + market:yifang:convert:excel
    + market:yifang:convert:ppt
    + market:facepp:facecrop2
    + market:sequickimage:convert
    + market:tusdk:face:detection
    + market:tusdk:face:landmark
    + market:dg:content:audit:v5
* extras: zone 可选(z1北京，z0宁波)

```
{"item":"mps:sd","begin":"1451577600000","end":"1451664000000","uid":1338989962}
```

返回:
```
{
  "code": 200,
  "data": {
    "times": [
      1467302400,
      1467388800
    ],
    "datas": [
      10000,
      10000
    ]
  }
}
```
##FUSION计量接口
请求参数:
* item: transfer_cdn/transfer_ov/https_transfer/https_transfer_ov 

```
{"item":"transfer_cdn","begin":"1451577600000","end":"1451664000000","uid":1338989962, "extras":{"type":"flow"}}
```
返回:
```
{ "code": 200, 
  "data": { 
        "times": [ 1467331500, 1467331800, 1467332100, 1467332400, 1467332700, 1467333000, 1467333300, 1467333600, 1467333900, 1467334200, ...], 
        "datas": [ 1073741824, 1073741824, 1073741824, 1073741824, 1073741824, 1073741824, 1073741824, 1073741824, 1073741824, 1073741824, ...] } 
  }
}
```

##UFOP计量接口
请求参数:
* item:
    + ufop:machine:m0c1
    + ufop:machine:m1c2
    + ufop:machine:m2c4
    + ufop:machine:m4c8
    + ufop:machine:m8c12
    + ufop:machine:g1m16c8

```
{"item":"ufop:machine:m0c1","begin":1469980800000,"end":1470067200000,"email":"<EMAIL>"}
```
返回:
```
{
  "code": 200,
  "data": {
    "times": [
      1469980800
    ],
    "datas": [
      86400000000000
    ]
  }
}
```

##文件数统计

请求参数:

 * item:file_count
 * g:支持day
 * zone:同space
