账单接口
=======

以下接口都需要admin oauth鉴权

## 获取账单列表

```
GET /api/user/:uid/bills?start=201601&end=201605&email=<EMAIL>
```
* start: string，开始月份，可为空
* end: string，结束月份，可为空
* email: string，用于查合账子账号的账单列表

正常返回值:
```json
{
  "code": 200,
  "data": {
    "bills": [
      {
        "id": "575121df43c8ce5742000030",
        "month": "2016-05-01T00:00:00+08:00",
        "money": 799077100,
        "status": 2
      }
    ],
    "summary": true
  }
}
```

* bills: 账单数组
  * id: 账单id
  * month: 账单月份
  * money: 账单金额
  * status: 账单状态
* summary: 是否是合账账单列表

获取账单列表失败:
```json
{
  "code": 7720
}
```


## 普通月账单

```
GET /api/user/:uid/bills/:month
```
* uid: 用户uid
* month: 账单月份，格式201601

正常返回值:
```html
账单渲染好的html
```

账单未找到:
http status code 7721

获取账单详情失败:
http status code 7722

数据库错误:
http status code 598

## 合账月账单

```
GET /api/user/:uid/merge-account-bills/:month
```
* uid: 用户uid
* month: 账单月份，格式201601

正常返回值:
```html
账单渲染好的html
```

账单未找到:
http status code 7721

获取合账账单详情失败:
http status code 7723

数据库错误:
http status code 598
