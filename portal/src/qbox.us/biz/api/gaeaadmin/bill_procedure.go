package gaeaadmin

import (
	"fmt"
	"net/url"
	"time"

	"qbox.us/biz/utils.v2/json"
)

const (
	isMonthClosedPath = "%s/api/bills/procedure/is-closed"
)

type isMonthClosedResp struct {
	json.CommonResponse

	Data bool `json:"data"`
}

func (s *gaeaAdminService) BillingIsMonthClosed(month time.Time) (closed bool, err error) {
	var out isMonthClosedResp

	path := fmt.Sprintf(isMonthClosedPath, s.host)

	params := url.Values{
		"month": []string{month.Format("200601")},
	}

	err = s.client.GetCallWithForm(s.reqLogger, &out, path, params)
	if err != nil {
		return
	}

	err = out.Error()
	if err != nil {
		return false, err
	}

	return out.Data, nil
}
