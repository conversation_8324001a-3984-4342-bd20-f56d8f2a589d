package gaeaadmin

import (
	"net/url"
	"strconv"
	"time"
)

// FinancialRelationType 财务子账号类型
type FinancialRelationType int

const (
	FinancialRelationTypeMeasure FinancialRelationType = 1 // 合量: 合并使用量类型
	FinancialRelationTypeBilling FinancialRelationType = 2 // 合账: 合并账单类型
	FinancialRelationTypeNone    FinancialRelationType = 3 // 未开启合账
)

type FinancialRelation struct {
	Id             string                `json:"id"`
	Uid            uint32                `json:"uid"`
	ParentUid      uint32                `json:"parent_uid"`
	Memo           string                `json:"memo"`
	CreatorId      string                `json:"creator_id"`
	Type           FinancialRelationType `json:"type"`
	CreatedAt      time.Time             `json:"created_at"`
	UpdatedAt      time.Time             `json:"updaetd_at"`
	EffectiveStart time.Time             `json:"effective_start"`
	EffectiveEnd   time.Time             `json:"effective_end"`
}

// IsParent 是否是财务父账号
func (d FinancialRelation) IsParent() bool {
	return d.ParentUid == 0
}

// IsChild 是否是财务子账号
func (d FinancialRelation) IsChild() bool {
	return d.ParentUid > 0
}

// IsMeasureChild 是否是财务 合量 子账号
func (d FinancialRelation) IsMeasureChild() bool {
	return d.Type == FinancialRelationTypeMeasure && d.IsChild()
}

// IsBillingChild 是否是财务 合账 子账号
func (d FinancialRelation) IsBillingChild() bool {
	return d.Type == FinancialRelationTypeBilling && d.IsChild()
}

type FinancialRelationListParams struct {
	Uid       *uint32                `param:"uid"`
	ParentUid *uint32                `param:"parent_uid"`
	Type      *FinancialRelationType `param:"type"`
	CreatorId *string                `param:"creator_id"`
	Prev      *uint32                `param:"prev"`
	Next      *uint32                `param:"next"`
	PageSize  int                    `param:"page_size"`
	Epoch     time.Time              `param:"epoch"`
}

type FinancialRelationListChildrenParams struct {
	Uid      *uint32   `param:"uid"`
	Prev     *uint32   `param:"prev"`
	Next     *uint32   `param:"next"`
	PageSize int       `param:"page_size"`
	Epoch    time.Time `param:"epoch"`
}

type FinancialRelationCreateParams struct {
	Uid       uint32                `json:"uid"`
	ParentUid uint32                `json:"parent_uid"`
	Type      FinancialRelationType `json:"type"`
	Memo      string                `json:"memo"`
}

type FinancialRelationUpdateParams struct {
	ParentUid *uint32                `json:"parent_uid"`
	Type      *FinancialRelationType `json:"type"`
	Memo      *string                `json:"memo"`
}

type FinancialRelationList struct {
	List []FinancialRelation `json:"list"`
	Prev uint32              `json:"prev"`
	Next uint32              `json:"next"`
}

func (p *FinancialRelationListParams) Values() (value url.Values) {
	value = make(url.Values)

	if p.Uid != nil {
		value.Set("uid", strconv.FormatUint(uint64(*p.Uid), 10))
	}

	if p.ParentUid != nil {
		value.Set("parent_uid", strconv.FormatUint(uint64(*p.ParentUid), 10))
	}

	if p.Type != nil {
		value.Set("type", strconv.Itoa(int(*p.Type)))
	}

	if p.CreatorId != nil {
		value.Set("creator_id", *p.CreatorId)
	}

	if p.Prev != nil {
		value.Set("prev", strconv.FormatUint(uint64(*p.Prev), 10))
	}

	if p.Next != nil {
		value.Set("next", strconv.FormatUint(uint64(*p.Next), 10))
	}

	value.Set("page_size", strconv.Itoa(p.PageSize))

	if !p.Epoch.IsZero() {
		value.Set("epoch", p.Epoch.Format(time.RFC3339Nano))
	}

	return
}

func (p *FinancialRelationListChildrenParams) Values() (value url.Values) {
	value = make(url.Values)

	if p.Uid != nil {
		value.Set("uid", strconv.FormatUint(uint64(*p.Uid), 10))
	}

	if p.Prev != nil {
		value.Set("prev", strconv.FormatUint(uint64(*p.Prev), 10))
	}

	if p.Next != nil {
		value.Set("next", strconv.FormatUint(uint64(*p.Next), 10))
	}

	value.Set("page_size", strconv.Itoa(p.PageSize))

	if !p.Epoch.IsZero() {
		value.Set("epoch", p.Epoch.Format(time.RFC3339Nano))
	}
	return
}
