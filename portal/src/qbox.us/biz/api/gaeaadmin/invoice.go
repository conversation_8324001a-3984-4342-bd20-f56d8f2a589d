package gaeaadmin

import (
	"fmt"
	"time"

	"labix.org/v2/mgo/bson"
	"qbox.us/api/pay/pay"
	"qbox.us/biz/api/gaeaadmin/enums"
)

const (
	GetInvoiceDeliveryPath          = "%s/api/finance/invoice/delivery/%d"
	SetInvoiceDeliveryPath          = "%s/api/finance/invoice/delivery"
	GetInvoiceUploadTokenPath       = "%s/api/finance/invoice/upload-token"
	CreateInvoiceSettingPath        = "%s/api/finance/invoice/setting/%d"
	UpdateInvoiceSettingPath        = "%s/api/finance/invoice/setting?id=%s"
	GetInvoiceSettingPath           = "%s/api/finance/invoice/setting?id=%s"
	DeleteInvoiceSettingPath        = "%s/api/finance/invoice/setting?id=%s"
	ListInvoiceSettingByUidPath     = "%s/api/finance/invoice/setting/%d/list?page=%d&page_size=%d"
	SetDefaultInvoiceSettingPath    = "%s/api/finance/invoice/setting/%d/default?id=%s"
	GetDefaultInvoiceSettingPath    = "%s/api/finance/invoice/setting/%d/default"
	GetInvoiceValidTransactionsPath = "%s/api/finance/invoice/%d/valid-transactions?start=%d&end=%d"
	CreateInvoicePath               = "%s/api/finance/invoice/%d"
	GetInvoicePath                  = "%s/api/finance/invoice/detail/%s"
	ListInvoiceByUidPath            = "%s/api/finance/invoice/%d/list?page=%d&page_size=%d"
	UpdateInvoicePath               = "%s/api/finance/invoice/detail/%s"
	GetElectricInvoiceStockPath     = "%s/api/finance/invoice/electric-invoice-stock"
)

type InvoiceDelivery struct {
	Uid      uint32    `json:"uid"`
	Address  string    `json:"address"`
	Phone    string    `json:"phone"`
	Name     string    `json:"name"`
	UpdateAt time.Time `json:"update_at"`
}

func (s *gaeaAdminService) GetInvoiceDelivery(uid uint32) (delivery InvoiceDelivery, err error) {
	var (
		resp struct {
			apiResultBase
			Data InvoiceDelivery `json:"data"`
		}
		api = fmt.Sprintf(GetInvoiceDeliveryPath, s.host, uid)
	)
	delivery.UpdateAt = time.Now()
	err = s.client.GetCall(s.reqLogger, &resp, api)
	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	delivery = resp.Data
	return
}

func (s *gaeaAdminService) SetInvoiceDelivery(delivery InvoiceDelivery) (err error) {
	var (
		resp struct {
			apiResultBase
			Data InvoiceDelivery `json:"data"`
		}
		api = fmt.Sprintf(SetInvoiceDeliveryPath, s.host)
	)

	err = s.client.CallWithJson(s.reqLogger, &resp, api, delivery)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	return
}

type InvoiceUploadToken struct {
	Domain string `json:"domain"`
	Token  string `json:"token"`
}

func (s *gaeaAdminService) GetInvoiceUploadToken() (token InvoiceUploadToken, err error) {
	var (
		resp struct {
			apiResultBase
			Data InvoiceUploadToken `json:"data"`
		}
		api = fmt.Sprintf(GetInvoiceUploadTokenPath, s.host)
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)
	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	token = resp.Data

	return
}

type InvoiceSettingInput struct {
	Title          string                     `json:"title"`
	Type           enums.InvoiceType          `json:"type"`
	Status         enums.InvoiceSettingStatus `json:"status"`
	TaxPayerId     *string                    `json:"tax_payer_id"`
	BankName       *string                    `json:"bank_name"`
	BankAccount    *string                    `json:"bank_account"`
	Company        *string                    `json:"company"`
	CompanyAddress *string                    `json:"company_address"`
	CompanyTel     *string                    `json:"company_tel"`
	CertificateUrl *string                    `json:"certificate_url"`
	Reason         *string                    `json:"reason"`
	Comment        *string                    `json:"comment"`
	CoTaxType      enums.CoTaxType            `json:"co_tax_type"` // 公司纳税类型
	CoTaxId        *string                    `json:"co_tax_id"`   // 公司纳税号码
}

type InvoiceSettingOutput struct {
	Id             bson.ObjectId              `json:"id"`
	Uid            uint32                     `json:"uid"`
	Title          string                     `json:"title"`
	Type           enums.InvoiceType          `json:"type"`
	Status         enums.InvoiceSettingStatus `json:"status"`
	TaxPayerId     string                     `json:"tax_payer_id"`
	BankName       string                     `json:"bank_name"`
	BankAccount    string                     `json:"bank_account"`
	Company        string                     `json:"company"`
	CompanyAddress string                     `json:"company_address"`
	CompanyTel     string                     `json:"company_tel"`
	CertificateUrl string                     `json:"certificate_url"`
	FileName       string                     `json:"file_name"`
	Reason         string                     `json:"reason"`
	Comment        string                     `json:"comment"`
	IsDefault      bool                       `json:"is_default"`
	UpdatedAt      time.Time                  `json:"updated_at"`
	CoTaxType      enums.CoTaxType            `json:"co_tax_type"` // 公司纳税类型
	CoTaxId        string                     `json:"co_tax_id"`   // 公司纳税号码
}

func (s *gaeaAdminService) CreateInvoiceSetting(uid uint32, input InvoiceSettingInput) (id string, err error) {
	var (
		resp struct {
			apiResultBase
			Data string `json:"data"`
		}
		api = fmt.Sprintf(CreateInvoiceSettingPath, s.host, uid)
	)

	err = s.client.CallWithJson(s.reqLogger, &resp, api, input)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	id = resp.Data

	return
}

func (s *gaeaAdminService) UpdateInvoiceSetting(id string, input InvoiceSettingInput) (err error) {
	var (
		resp struct {
			apiResultBase
		}
		api = fmt.Sprintf(UpdateInvoiceSettingPath, s.host, id)
	)

	err = s.client.PutCallWithJson(s.reqLogger, &resp, api, input)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	return
}

func (s *gaeaAdminService) GetInvoiceSetting(id string) (out InvoiceSettingOutput, err error) {
	var (
		resp struct {
			apiResultBase
			Data InvoiceSettingOutput `json:"data"`
		}
		api = fmt.Sprintf(GetInvoiceSettingPath, s.host, id)
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	out = resp.Data
	return
}

func (s *gaeaAdminService) DeleteInvoiceSetting(id string) (err error) {
	var (
		resp apiResultBase
		api  = fmt.Sprintf(DeleteInvoiceSettingPath, s.host, id)
	)

	err = s.client.DeleteCall(s.reqLogger, &resp, api)
	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	return
}

func (s *gaeaAdminService) ListInvoiceSettingBuyUid(uid uint32, page, page_size int) (outs []InvoiceSettingOutput, err error) {
	var (
		resp struct {
			apiResultBase
			Data []InvoiceSettingOutput `json:"data"`
		}
		api = fmt.Sprintf(ListInvoiceSettingByUidPath, s.host, uid, page, page_size)
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	outs = resp.Data
	return
}

func (s *gaeaAdminService) SetDefaultInvoiceSetting(uid uint32, id string) (err error) {
	var (
		resp apiResultBase
		api  = fmt.Sprintf(SetDefaultInvoiceSettingPath, s.host, uid, id)
	)

	err = s.client.PutCallWithJson(s.reqLogger, &resp, api, nil)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	return
}

func (s *gaeaAdminService) GetDefaultInvoiceSetting(uid uint32) (out InvoiceSettingOutput, err error) {
	var (
		resp struct {
			apiResultBase
			Data InvoiceSettingOutput `json:"data"`
		}
		api = fmt.Sprintf(GetDefaultInvoiceSettingPath, s.host, uid)
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)
	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	out = resp.Data

	return
}

type RelatedTransaction struct {
	SerialNum   string    `json:"serial_num"`
	Cash        pay.Money `json:"cash"`
	ValidAmount pay.Money `json:"valid_amount"`
	Time        time.Time `json:"time"`
	Source      string    `json:"source"`
}

type ValidTransactionsOutput struct {
	Amount              pay.Money            `json:"amount"`
	SumRefund           pay.Money            `json:"sum_refund"`   // 提现金额
	SumUsed             pay.Money            `json:"sum_used"`     // 已开票金额
	SumRecharge         pay.Money            `json:"sum_recharge"` // 充值总金额
	SumOverused         pay.Money            `json:"sum_overused"` // 超额金额（考虑历史所有开票记录）
	RelatedTransactions []RelatedTransaction `json:"related_transactions"`
}

func (s *gaeaAdminService) GetInvoiceValidTransactions(uid uint32, start, end time.Time) (out ValidTransactionsOutput, err error) {
	var (
		resp struct {
			apiResultBase
			Data ValidTransactionsOutput `json:"data"`
		}
		api = fmt.Sprintf(GetInvoiceValidTransactionsPath, s.host, uid, start.Unix(), end.Unix())
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	out = resp.Data

	return
}

type Delivery struct {
	Address string `json:"address"`
	Phone   string `json:"phone"`
	Name    string `json:"name"`
	Email   string `json:"email"`
}

type InvoiceInput struct {
	// InvoiceSettingId 开票基本信息 id
	InvoiceSettingId string `json:"invoice_setting_id"`
	// Amount 开票金额
	Amount pay.Money `json:"amount"`
	// RelatedTransactions 关联流水
	RelatedTransactions []string `json:"related_transactions"`
	// TaxRate 税点
	TaxRate int `json:"tax_rate"`
	// FeeNote 费用名目
	FeeNote string `json:"fee_note"`
	// Delivery 物流信息
	Delivery Delivery `json:"delivery"`
	// Note 发票打印内容
	Note string `json:"note" conform:"!js"`
	// Comment 内部备注
	Comment string `json:"comment" conform:"!js"`
	// CustomerComment 用户备注
	CustomerComment string `json:"customer_comment"`
	// BillRequestId 唯一标识需要打印发票的开票 id 信息
	BillRequestId string `json:"bill_request_id"`
	// Start 需要打印发票的账单 开始时间 信息
	Start time.Time `json:"start"`
	// End 需要打印发票的账单 结束时间 信息
	End time.Time `json:"end"`
	// HideExpenseDetails 需要打印发票的账单 是否隐藏支付明细
	HideExpenseDetails bool `json:"hide_expense_details"`
	// InvoiceCategory 发票类型
	InvoiceCategory enums.InvoiceCategory `json:"invoice_category"`
	// InvoiceColor 发票颜色
	InvoiceColor enums.InvoiceColor `json:"invoice_color"`
	// WriteoffNumber 对冲的原发票的number
	WriteoffNumber string `json:"writeoff_number"`
}

func (s *gaeaAdminService) CreateInvoice(uid uint32, input InvoiceInput) (id string, err error) {
	var (
		resp struct {
			apiResultBase
			Data string `json:"data"`
		}
		api = fmt.Sprintf(CreateInvoicePath, s.host, uid)
	)

	err = s.client.CallWithJson(s.reqLogger, &resp, api, input)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	id = resp.Data

	return
}

type RelateTransaction struct {
	Id         string    `json:"id"`
	Cash       pay.Money `json:"cash"`
	AmountUsed pay.Money `json:"amount_used"`
}

type InvoiceSettingRecord struct {
	Title          string            `json:"title"`
	Type           enums.InvoiceType `json:"type"`
	TaxPayerId     string            `json:"tax_payer_id"`
	BankName       string            `json:"bank_name"`
	BankAccount    string            `json:"bank_account"`
	Company        string            `json:"company"`
	CompanyAddress string            `json:"company_address"`
	CompanyTel     string            `json:"company_tel"`
	CertificateUrl string            `json:"certificate_url"`
	CoTaxType      enums.CoTaxType   `json:"co_tax_type"` // 公司纳税类型
	CoTaxId        string            `json:"co_tax_id"`   // 公司纳税号码
}

type DeliveryRecord struct {
	Address        string `json:"address"`
	Phone          string `json:"phone"`
	Name           string `json:"name"`
	Email          string `json:"email"`
	ExpressCompany string `json:"express_company"`
	ExpressNumber  string `json:"express_number"`
}

type InvoiceRecord struct {
	// Id id
	Id bson.ObjectId `json:"id"`
	// Uid uid
	Uid uint32 `json:"uid"`
	// Number 票号
	Number string `json:"number"`
	// Money 开票金额
	Money pay.Money `json:"money"`
	// RelateTransactions 关联流水
	RelateTransactions []RelateTransaction `json:"relate_transactions"`
	// InvoiceSetting 开票基本信息
	InvoiceSetting InvoiceSettingRecord `json:"invoice_setting"`
	// InvoiceSettingUID 开票信息归属 UID
	InvoiceSettingUID uint32 `json:"invoice_setting_uid"`
	// Delivery 物流信息
	Delivery DeliveryRecord `json:"delivery"`
	// Status 状态
	Status enums.InvoiceStatus `json:"status"`
	// Note 发票打印内容
	Note string `json:"note" bson:"note"`
	// TaxRate 税点
	TaxRate int `json:"tax_rate"`
	// FeeNote 费用名目
	FeeNote string `json:"fee_note"`
	// Reason 原因
	Reason string `json:"reason"`
	// Comment 内部备注
	Comment string `json:"comment"`
	// CustomerComment 用户备注
	CustomerComment string `json:"customer_comment"`
	// Applicant 申请人
	Applicant string `json:"applicant"`
	// BillRequestId 唯一标识需要打印发票的开票 id 信息
	BillRequestId string `json:"bill_request_id"`
	// Start 需要打印发票的账单 开始时间 信息
	Start time.Time `json:"start"`
	// End 需要打印发票的账单 结束时间 信息
	End time.Time `json:"end" bson:"end"`
	// HideExpenseDetails 需要打印发票的账单 是否隐藏支付明细
	HideExpenseDetails bool `json:"hide_expense_details"`
	// InvoiceCategory 发票类型 1-电子发票 2-纸质发票
	InvoiceCategory enums.InvoiceCategory `json:"invoice_category"`
	// ElectricInvoiceID 电子发票 ID
	ElectricInvoiceID string `json:"electric_invoice_id"`
	// ElectricInvoiceUrl 电子发票 pdf URL
	// 此处专指向 PDF 格式的电子发票，因要兼容历史数据和前端代码，名字不改为 PDFElectricInvoiceUrl
	ElectricInvoiceUrl string `json:"electric_invoice_url"`
	// OFDElectricInvoiceUrl ofd 格式电子发票
	OFDElectricInvoiceUrl string `json:"ofd_electric_invoice_url"`
	// XMLElectricInvoiceUrl xml 格式电子发票
	XMLElectricInvoiceUrl string `json:"xml_electric_invoice_url"`
	// InvoiceColor 发票颜色 1-蓝色发票 2-红色发票
	InvoiceColor enums.InvoiceColor `json:"invoice_color"`
	// RelateInvoiceID 关联的红票/蓝票 ID
	RelatedInvoiceID string `json:"related_invoice_id" bson:"related_invoice_id"`
	// RelateInvoiceUrl 关联的红票/蓝票 Url
	RelatedInvoiceUrl string `json:"related_invoice_url" bson:"related_invoice_url"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `json:"updated_at"`
}

func (s *gaeaAdminService) GetInvoice(id string) (out InvoiceRecord, err error) {
	var (
		resp struct {
			apiResultBase
			Data InvoiceRecord `json:"data"`
		}
		api = fmt.Sprintf(GetInvoicePath, s.host, id)
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	out = resp.Data
	return
}

func (s *gaeaAdminService) ListInvoiceByUid(uid uint32, page, page_size int) (outs []InvoiceRecord, err error) {
	var (
		resp struct {
			apiResultBase
			Data []InvoiceRecord `json:"data"`
		}
		api = fmt.Sprintf(ListInvoiceByUidPath, s.host, uid, page, page_size)
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	outs = resp.Data
	return
}

type UpdateInvoiceInput struct {
	// Number 票号
	Number *string `json:"number"`
	// Status 状态
	Status *enums.InvoiceStatus `json:"status"`
	// DeliveryExpressCompany 快递公司
	DeliveryExpressCompany *string `json:"delivery_express_company"`
	// DeliveryNumber 快递单号
	DeliveryNumber *string `json:"delivery_number"`
	// Reason 原因
	Reason *string `json:"reason" conform:"!js"`
	// Note 发票打印内容
	Note *string `json:"note" conform:"!js"`
	// Comment 内部备注
	Comment *string `json:"comment" conform:"!js"`
	// CustomerComment 用户备注
	CustomerComment *string `json:"customer_comment" conform:"!js"`
}

func (s gaeaAdminService) UpdateInvoice(id string, input UpdateInvoiceInput) (err error) {
	var (
		resp apiResultBase
		api  = fmt.Sprintf(UpdateInvoicePath, s.host, id)
	)

	err = s.client.PutCallWithJson(s.reqLogger, &resp, api, input)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	return
}

// CancelInvoice 撤回已申请未审核的发票
func (s gaeaAdminService) CancelInvoice(id string, updateStatus enums.InvoiceStatus) (err error) {
	// 撤回发票的本质是将发票的状态从已申请改为已撤回，因此还是调用 gaea admin 的 UpdateInvoice
	input := UpdateInvoiceInput{
		Status: &updateStatus,
	}
	var (
		resp apiResultBase
		api  = fmt.Sprintf(UpdateInvoicePath, s.host, id)
	)
	err = s.client.PutCallWithJson(s.reqLogger, &resp, api, input)

	if err != nil || !resp.OK() {
		err = resp.Error()
		return
	}

	return
}

type ElectricInvoiceStock struct {
	// Stock 当前库存
	Stock int `json:"stock"`
}

// GetElectricInvoiceStock 获取电子发票当前库存数量
func (s gaeaAdminService) GetElectricInvoiceStock() (stock *ElectricInvoiceStock, err error) {
	var (
		resp struct {
			apiResultBase
			Data ElectricInvoiceStock `json:"data"`
		}
		api = fmt.Sprintf(GetElectricInvoiceStockPath, s.host)
	)

	err = s.client.GetCall(s.reqLogger, &resp, api)
	if err != nil || !resp.OK() {
		err = resp.Error()
		return nil, err
	}

	stock = &resp.Data
	return
}
