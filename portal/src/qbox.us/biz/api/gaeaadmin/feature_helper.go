package gaeaadmin

import (
	"net/url"
	"strconv"
)

type Feature struct {
	FeatureId string `json:"featureId"`
	Summary   string `json:"summary"`
	Disable   bool   `json:"disable"`
	Index     int    `json:"index"`
	Refer     string `json:"refer"`
}

type DeveloperSearch struct {
	Total int             `json:"total"`
	Items []DeveloperItem `json:"items"`
}

type DeveloperItem struct {
	Uid          uint32 `json:"uid"`
	Email        string `json:"email"`
	FullName     string `json:"fullname"`
	PhoneNumber  string `json:"phone_number"`
	ImCategory   string `json:"im_category"`
	ImNumber     string `json:"im_number"`
	MobileBinded bool   `json:"mobile_binded"`
	IsActivated  bool   `json:"isactived"`
	CreateAt     int64  `json:"created_at"`
	SalesId      string `json:"sf_sales_id"`
}

type FeatureDeveloperGetReq struct {
	Query     string `json:"query"`
	Limit     int    `json:"limit"`
	Offset    int    `json:"offset"`
	FeatureId string `json:"featureId"`
}

type UpdateFeatureConfigInput struct {
	Email  string   `json:"email"`
	Uid    uint32   `json:"uid"`
	Add    []string `json:"add"`
	Delete []string `json:"delete"`
}

type NavFeatureRet struct {
	Index   int    `json:"index"`
	Summary string `json:"summary"`
	Disable bool   `json:"disable"`
	Refer   string `json:"refer"`
}

type NavEdit struct {
	FeatureId string `json:"featureId"`
	Disable   bool   `json:"disable"`
	Index     int    `json:"index"`
	Refer     string `json:"refer"`
}

type GlbFeatureEditInput struct {
	FeatureId string `json:"featureId"`
	Disable   bool   `json:"disable"`
	Summary   string `json:"summary"`
}

type FeatureConfigUrlParams struct {
	Uid   uint32
	Email string
}

func (p FeatureConfigUrlParams) ToURLValues() url.Values {
	values := url.Values{}
	if p.Uid > 0 {
		values.Add("uid", strconv.FormatUint(uint64(p.Uid), 10))
	}

	if p.Email != "" {
		values.Add("email", p.Email)
	}

	return values
}
