package gaeaadmin

import (
	"time"

	"qbox.us/api/pay/pay"
	"qbox.us/biz/api/gaeaadmin/enums"
)

// ReconciliationBill 对账中心口径，账单
type ReconciliationBill struct {
	// Period 账单所属账期
	Period time.Time `json:"period"`
	// AccountEmail 账号邮箱
	AccountEmail string `json:"accountEmail"`
	// BillID 订单/账单编号
	BillID string `json:"billID"`
	// Type 账单类型
	Type enums.ReconciliationBillType `json:"type"`
	// PayStatus 支付状态
	PayStatus enums.ReconciliationPayStatus `json:"payStatus"`
	// Product 产品线
	Product enums.ReconciliationProduct `json:"product"`
	// ItemDesc 计费项描述
	ItemDesc string `json:"itemDesc"`
	// Fee 消费金额
	Fee pay.Money `json:"fee"`
	// CouponFee 抵用券抵扣金额
	CouponFee pay.Money `json:"couponFee"`
	// NbFee 牛币抵扣金额
	NbFee pay.Money `json:"nbFee"`
	// CashFee 现金抵扣金额
	CashFee pay.Money `json:"cashFee"`
}

// ReconciliationTxnDetail 对账中心口径，流水明细
type ReconciliationTxnDetail struct {
	// ID 流水编号
	ID string `json:"id"`
	// TxnTime 交易时间
	TxnTime time.Time `json:"txnTime"`
	// TxnType 交易类型
	TxnType enums.ReconciliationTxnType `json:"txnType"`
	// Change 收入/支出金额
	Change pay.Money `json:"change"`
	// CashAfter 流水执行后，现金余额
	CashAfter pay.Money `json:"cashAfter"`
	// NbAfter 流水执行后，牛币余额
	NbAfter pay.Money `json:"nbAfter"`
	// CouponAfter 流水执行后，剩余可用抵用券余额之和
	CouponAfter pay.Money `json:"couponAfter"`
	// Description 描述
	Description string `json:"description"`
}

// ReconciliationBillsFee 月结算单费用统计
type ReconciliationBillsFee struct {
	TotalFee       pay.Money `json:"total_fee"`
	TotalNbFee     pay.Money `json:"total_nb_fee"`
	TotalCashFee   pay.Money `json:"total_cash_fee"`
	TotalCouponFee pay.Money `json:"total_coupon_fee"`
	UnPayedFee     pay.Money `json:"un_payed_fee"`
}
