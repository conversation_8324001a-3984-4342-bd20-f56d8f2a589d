// 该包主要提供了解析鉴权的工具，与 `base/qiniu/src/qiniu.com/auth` 类似
// 区别：解析 bearer 鉴权不依赖 account 库，并且返回结果中不含有 `Appid` & `AccessKey` 字段

package auth

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"

	. "code.google.com/p/go.net/context"
	"github.com/qiniu/rpc.v1/lb.v2.1"
	"github.com/qiniu/xlog.v1"
	acc "qbox.us/biz/services.v2/account"
	"qbox.us/servend/account"
	. "qiniu.com/auth/proto.v1"
	"qiniu.com/auth/qboxmac.v1"
	"qiniu.com/auth/qiniumac.v1"
	"qiniu.com/auth/signer.v1"
)

type SimpleAuthParser struct {
	AllowBearer   bool
	AllowQBox     bool
	AllowQiniu    bool
	Acc           Interface
	BearerService *BearerService // 用于解析 bearer 鉴权
}

func (ap *SimpleAuthParser) ParseAuth(req *http.Request) (user account.UserInfo, err error) {

	authl, ok := req.Header["Authorization"]
	if !ok {
		err = signer.ErrBadToken
		return
	}

	var user1 SudoerInfo
	auth := authl[0]
	switch {
	case ap.AllowBearer && strings.HasPrefix(auth, "Bearer "):
		if ap.BearerService == nil {
			err = fmt.Errorf("BearerService is nil")
		} else {
			token := auth[7:]
			user1, err = ap.BearerService.ParseBearerAuth(token)
		}
	case ap.AllowQBox && strings.HasPrefix(auth, "QBox "):
		token := auth[5:]
		user1, err = signer.ParseNormalAuth(qboxmac.DefaultRequestSigner, ap.Acc, Background(), token, req)
	case ap.AllowQBox && strings.HasPrefix(auth, "QBoxAdmin "):
		token := auth[10:]
		user1, err = signer.ParseAdminAuth(qboxmac.DefaultRequestSigner, ap.Acc, Background(), token, req)
	case ap.AllowQiniu && strings.HasPrefix(auth, "Qiniu "):
		token := auth[6:]
		user1, err = signer.ParseNormalAuth(qiniumac.DefaultRequestSigner, ap.Acc, Background(), token, req)
	case ap.AllowQiniu && strings.HasPrefix(auth, "QiniuAdmin "):
		token := auth[11:]
		user1, err = signer.ParseAdminAuth(qiniumac.DefaultRequestSigner, ap.Acc, Background(), token, req)
	default:
		err = signer.ErrBadToken
		return
	}

	user.Uid = user1.Uid
	user.Sudoer = user1.Sudoer
	user.Utype = user1.Utype
	user.UtypeSu = user1.UtypeSu

	return
}

type BearerService struct {
	accClient *lb.Client
	logger    *xlog.Logger
}

func NewBearerService(accHosts string, logger *xlog.Logger) *BearerService {
	hosts := strings.Split(accHosts, ",")
	accCfg := &lb.Config{
		Hosts:    hosts,
		TryTimes: uint32(len(hosts)),
	}
	accClient := lb.New(accCfg, http.DefaultTransport)

	return &BearerService{accClient: accClient, logger: logger}
}

func (a *BearerService) ParseBearerAuth(accessToken string) (user SudoerInfo, err error) {
	var userInfo acc.UserInfo
	data := url.Values{
		"access_token": {accessToken},
	}
	// 考虑同一个 BearerService 会存在 logger 并发问题，此处采用 logger.SpawnWithCtx()
	err = a.accClient.CallWithForm(a.logger.SpawnWithCtx(), &userInfo, "/user/info", data)
	if err != nil {
		err = fmt.Errorf("accClient get user info failed :%v", err)
		return
	}

	user.Utype = uint32(userInfo.UserType)
	user.Uid = userInfo.Uid
	return
}
