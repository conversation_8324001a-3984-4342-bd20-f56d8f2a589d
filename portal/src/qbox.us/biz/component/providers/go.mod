module qbox.us/biz/component/providers

go 1.18

replace code.google.com/p/go.net => ../../../../../../com/src/code.google.com/p/go.net

replace github.com/bradfitz/gomemcache.20160421 => ../../../../../../com/src/github.com/bradfitz/gomemcache.20160421

replace github.com/qiniu/api => ../../../../../../qiniu/src/github.com/qiniu/api

replace github.com/qiniu/rpc.v1 => ../../../../../../qiniu/src/github.com/qiniu/rpc.v1

replace github.com/qiniu/xlog.v1 => ../../../../../../qiniu/src/github.com/qiniu/xlog.v1

replace github.com/teapots/inject => ../../../../github.com/teapots/inject

replace github.com/teapots/teapot => ../../../../github.com/teapots/teapot

replace labix.org/v2/mgo => ../../../../../../com/src/labix.org/v2/mgo

replace qbox.us/biz/component/client => ../client

replace qbox.us/biz/component/sessions => ../sessions

replace qbox.us/biz/services.v2 => ../../services.v2

replace qbox.us/biz/utils.v2 => ../../utils.v2

replace qbox.us/iam => ../../../../../../biz/src/qbox.us/iam

replace qbox.us/oauth => ../../../../../../com/src/qbox.us/oauth

replace qbox.us/servend => ../../../../../../biz/src/qbox.us/servend

replace qiniu.com/auth => ../../../../../../qiniu/src/qiniu.com/auth
