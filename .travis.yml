dist: focal
os: linux
language: go
go_import_path: qiniu.io/gaea
git:
    depth: 3
go:
  - 1.24.x

services:
  - mongodb
  - redis
  - rabbitmq
  - mysql

branches:
  only:
    - master
    - test
    - /feat.*/

cache:
  apt: true
  directories:
    - node_modules
    - /home/<USER>/gopath/src/github.com/golang
    - /home/<USER>/gopath/bin
    - $HOME/.cache/go-build
    - $GOPATH/pkg/mod

before_install:
  - export GOPRIVATE=github.com/qbox,qbox.us,qiniu.io,qiniupkg.com,qiniu.com
  # give access to private repos
  # SSH key is configured in Travis repo settings, now instruct git to fetch code via ssh
  - git config --global url."ssh://**************/qbox/".insteadOf "https://github.com/qbox/"
  # Workaround outdated dep requesting transitive dep via insecure Git protocol:
  # https://github.blog/2021-09-01-improving-git-protocol-security-github/
  - git config --global url."https://github.com/monospaced/".insteadOf "git://github.com/monospaced/"

before_script:
  - eval "$(gimme $TRAVIS_GO_VERSION)"
  - gimme list
  - export TZ=Asia/Shanghai
  - date
  - export QBOXROOT=~/qboxroot && mkdir -p "$QBOXROOT"
  - export PORTAL_ROOT=$QBOXROOT/portal.qiniu.io
  - export PATH="${HOME}/bin:${PATH}"
  # ubuntu focal 竟然内置了一个 ********* localhost 的绑定关系，导致单测试 localhost 访问出问题
  - sudo sed -i 's/*********/127.0.0.1/gi' /etc/hosts

script:
  # - scripts/install_mongodb1.8.3.sh
  - scripts/ci/travis.sh

notifications:
  email:
    - <EMAIL>
after_success:
  - bash <(curl -s https://codecov.io/bash) -F unit -t eae738d5-cfb2-4891-84f5-e4f27b71850e