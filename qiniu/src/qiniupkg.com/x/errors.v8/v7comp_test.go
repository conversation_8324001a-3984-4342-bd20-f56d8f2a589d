package errors

import (
	"syscall"
	"testing"

	gerrors "github.com/qiniu/errors"
	qerrors "qbox.us/errors"
	errors "qiniupkg.com/x/errors.v7"
)

func TestSys(t *testing.T) {

	if !IsBadRequest(syscall.EINVAL) {
		t.<PERSON>al("IsBadRequest?")
	}

	if !IsAlreadyExists(syscall.EEXIST) {
		t.<PERSON>("IsAlreadyExists?")
	}

	if !IsNotFound(syscall.ENOENT) {
		t.<PERSON>al("IsNotFound?")
	}
}

func TestV7(t *testing.T) {

	e := qerrors.Info(syscall.EINVAL, "bad request")
	if !IsBadRequest(e) {
		t.Fatal("IsBadRequest?")
	}

	e2 := gerrors.Info(syscall.EINVAL, "gerror")
	if !IsBadRequest(e2) {
		t.<PERSON>al("IsBadRequest?")
	}

	e3 := errors.Info(syscall.EINVAL, "error")
	if !IsBadRequest(e3) {
		t.<PERSON><PERSON>("IsBadRequest?")
	}
}
