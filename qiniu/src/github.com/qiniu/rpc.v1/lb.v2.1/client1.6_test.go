//go:build go1.7
// +build go1.7

package lb

import (
	"sync/atomic"
	"testing"

	"github.com/qiniu/xlog.v1"
	"github.com/stretchr/testify/assert"
)

func TestRetryWithNilBody(t *testing.T) {
	xl := xlog.NewWith("RetryWithReqBody")

	cfgsA := []*TestServerCfg{
		&TestServerCfg{ExpectedBody: nil, StatusCode: 200, ReturnBody: []byte("Welcome"), ReproxyTimes: 2},
	}
	_, serversA, closer := startTestServers(t, cfgsA)

	proxys, _ := startProxys(4)

	var hostsA []string
	for _, server := range serversA {
		hostsA = append(hostsA, server.URL)
	}
	var proxyHostsB []string
	for _, server := range proxys {
		proxyHostsB = append(proxyHostsB, server.URL)
	}

	tr := NewTransport(&TransportConfig{
		RespTimeoutMS: 2000,
		Proxys:        proxyHostsB,
		TryTimes:      4,
		ShouldReproxy: shouldReproxy,
	})

	cli := New(&Config{
		Hosts:       hostsA,
		ShouldRetry: shouldRetry,
	}, tr)

	trytime = 0
	req, _ := NewRequest("POST", "/", nil)
	pc := atomic.LoadUint64(&proxycount)
	resp, err := cli.Do(xl, req)
	assert.Nil(t, err)
	assert.Equal(t, 200, resp.StatusCode)
	pc2 := atomic.LoadUint64(&proxycount)
	assert.Equal(t, uint64(3), pc2-pc)
	assert.Equal(t, uint64(3), trytime)
	closer()
}
