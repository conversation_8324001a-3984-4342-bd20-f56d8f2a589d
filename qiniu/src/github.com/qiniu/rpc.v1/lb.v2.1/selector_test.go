package lb

import (
	"net/http"
	"net/url"
	"sync/atomic"
	"testing"
	"time"

	"qbox.us/ratelimit"

	"github.com/qiniu/xlog.v1"
	"github.com/stretchr/testify/assert"
)

func TestHostStruct(t *testing.T) {
	xl := xlog.NewDummy()
	rh := "http://localhost:9040"
	u, err := url.Parse(rh)
	assert.NoError(t, err)
	rl := ratelimit.New(0, 0)
	h := &host{raw: rh, URL: u, rl: rl}

	//assert.True(t, h.IsOK(10))
	_, isPunished := h.IsPunished(10)
	assert.True(t, !isPunished)
	h.SetFail(xl)
	//assert.False(t, h.<PERSON>(10))
	_, isPunished = h.IsPunished(10)
	assert.False(t, !isPunished)
	//assert.True(t, h.Is<PERSON>(-1))
	_, isPunished = h.IsPunished(-1)
	assert.True(t, !isPunished)
	h.lastFailedTime = time.Now().Add(-10 * time.Second).Unix()
	//assert.True(t, h.IsOK(10))
	_, isPunished = h.IsPunished(10)
	assert.True(t, !isPunished)
	h.SetFail(xl)
	//assert.False(t, h.IsOK(1))
	_, isPunished = h.IsPunished(1)
	assert.False(t, !isPunished)
	time.Sleep(2 * time.Second)
	//assert.True(t, h.IsOK(1))
	_, isPunished = h.IsPunished(1)
	assert.True(t, !isPunished)
}

func TestRetrySelector(t *testing.T) {
	xl := xlog.NewDummy()
	var rs retrySelector
	rs.idx = 1
	rs.failRetryInterval = 10
	rl := ratelimit.New(0, 0)
	rs.hosts = []*host{
		&host{raw: "host0", rl: rl},
		&host{raw: "host1", rl: rl},
		&host{raw: "host2", rl: rl},
		&host{raw: "host3", rl: rl},
	}
	rs.hosts[3].SetFail(xl)
	stat := make(map[string]int)
	N := 30
	for i := 0; i < (len(rs.hosts)-2)*N; i++ {
		h := rs.Get(xl)
		stat[h.raw]++
	}
	for i, host := range rs.hosts {

		//if i == int(rs.idx) || !host.IsOK(rs.failRetryInterval) {
		_, isPunished := host.IsPunished(rs.failRetryInterval)
		if i == int(rs.idx) || isPunished {
			assert.Equal(t, 0, stat[host.raw], "%+v %v", host, stat)
		} else {
			assert.Equal(t, N, stat[host.raw], "%+v %v", host, stat)
		}
	}

	rs.retryHosts = nil
	assert.NotNil(t, rs.Get(xl))

	rs.retryHosts = nil
	rs.hosts[0].SetFail(xl)
	rs.hosts[2].SetFail(xl)
	assert.Nil(t, rs.Get(xl))
}

func TestSelector(t *testing.T) {
	xl := xlog.NewDummy()
	sel := newSelector([]string{"http://host0", "http://host1", "http://host2"}, 0, 10, false, 0, nil, 0, 0)

	req, err := http.NewRequest("GET", "http://www.qiniu.com", nil)
	assert.NoError(t, err)

	_, ok := sel.GetReqHost(req)
	assert.False(t, ok)
	ehost := &host{raw: "abcd"}
	sel.SetReqHost(req, ehost)
	h, ok := sel.GetReqHost(req)
	assert.True(t, ok)
	assert.Equal(t, ehost, h)
	sel.DelReqHost(req)
	_, ok = sel.GetReqHost(req)
	assert.False(t, ok)

	h, rs := sel.Get(xl)
	assert.Equal(t, "http://host1", h.raw)
	assert.Equal(t, "host1", h.URL.Host)

	h.SetFail(xl)
	for i := 0; i < 10; i++ {
		rhost := rs.Get(xl)
		assert.NotEqual(t, "http://host1", rhost.raw)
	}

	h, rs = sel.Get(xl)
	assert.Equal(t, "http://host2", h.raw)

	for i := 0; i < 10; i++ {
		rhost := rs.Get(xl)
		assert.NotEqual(t, "http://host1", rhost.raw)
		assert.NotEqual(t, "http://host2", rhost.raw)
	}

	h, rs = sel.Get(xl)
	assert.Equal(t, "http://host0", h.raw)

	h, rs = sel.Get(xl)
	assert.Equal(t, "http://host2", h.raw)
}

func TestLogwithPunishReqid(t *testing.T) {
	xl := xlog.NewWith("firstReq")
	sel := newSelector([]string{"http://host0", "http://host1", "http://host2"}, 0, 10, false, 0, nil, 0, 0)
	h, rs := sel.Get(xl)
	punishReqid, isPunished := h.IsPunished(10)
	assert.Empty(t, punishReqid)
	assert.True(t, !isPunished)
	assert.Equal(t, "http://host1", h.raw)
	assert.Equal(t, "host1", h.URL.Host)

	h.SetFail(xl)
	rhost := rs.Get(xl)
	assert.NotEqual(t, "http://host1", rhost.raw)

	rs.hosts[2].SetFail(xl)
	rhost = rs.Get(xl)
	assert.NotEqual(t, "http://host1", rhost.raw)

	xl = xlog.NewWith("secondReq")
	h, rs = sel.Get(xl)
	h.SetFail(xl)
	rhost = rs.Get(xl)
	assert.Nil(t, nil, rhost)

}
func TestSelectorDns(t *testing.T) {
	xl := xlog.NewDummy()
	lookupCount := int32(0)
	lookupHost := func(host string) ([]string, error) {
		return []string{host + "-a", host + "-b"}, nil
	}
	LookupHost := func(host string) (addrs []string, err error) {
		atomic.AddInt32(&lookupCount, 1)
		return lookupHost(host)
	}
	sel := newSelector([]string{"http://host0", "http://host1:80", "https://host2"}, 0, 10, true, 1, LookupHost, 0, 0)

	var h *host
	for i := 0; i < 3; i++ {
		h, _ = sel.Get(xl)
		assert.Equal(t, "http://host0-b", h.raw)
		assert.Equal(t, "host0-b", h.URL.Host)
		if i == 2 {
			h.SetFail(xl)
		}

		h, _ = sel.Get(xl)
		assert.Equal(t, "http://host1-a:80", h.raw)
		assert.Equal(t, "host1-a:80", h.URL.Host)

		h, _ = sel.Get(xl)
		assert.Equal(t, "http://host1-b:80", h.raw)
		assert.Equal(t, "host1-b:80", h.URL.Host)
		if i == 2 {
			h.SetFail(xl)
		}

		h, _ = sel.Get(xl)
		assert.Equal(t, "https://host2", h.raw)
		assert.Equal(t, "host2", h.URL.Host)

		h, _ = sel.Get(xl)
		assert.Equal(t, "http://host0-a", h.raw)
		assert.Equal(t, "host0-a", h.URL.Host)
	}
	assert.Equal(t, int32(2), lookupCount)

	lookupHost = func(host string) ([]string, error) {
		return []string{host + "-b", host + "-c"}, nil
	}
	err := sel.resolveDns()
	assert.NoError(t, err)
	sel.hostsLastUpdateTime = time.Now().UnixNano()

	for i := 0; i < 3; i++ {
		h, _ = sel.Get(xl)
		assert.Equal(t, "http://host0-c", h.raw)
		assert.Equal(t, "host0-c", h.URL.Host)

		h, _ = sel.Get(xl)
		assert.Equal(t, "http://host1-c:80", h.raw)
		assert.Equal(t, "host1-c:80", h.URL.Host)

		h, _ = sel.Get(xl)
		assert.Equal(t, "https://host2", h.raw)
		assert.Equal(t, "host2", h.URL.Host)
	}
	time.Sleep(100 * time.Millisecond)
	assert.Equal(t, int32(4), lookupCount)
}

func TestSelectorDnsHost(t *testing.T) {
	xl := xlog.NewDummy()
	lookupHost := func(host string) ([]string, error) {
		return []string{"***********", "***********"}, nil
	}
	sel := newSelector([]string{"http://host1:80"}, 0, 10, true, 1, lookupHost, 0, 0)
	h, _ := sel.Get(xl)
	assert.Equal(t, "host1:80", h.host)
}

func TestLookupHost(t *testing.T) {
	xl := xlog.NewDummy()

	hosts := []string{"***********", "***********"}
	lookupHost := func(host string) ([]string, error) {
		return hosts, nil
	}

	sel := newSelector([]string{"http://host1:321"}, 0, 10, true, 1, lookupHost, 0, 0)
	h, _ := sel.Get(xl)
	assert.True(t, "http://***********:321" == h.URL.String() || "http://***********:321" == h.URL.String())

	assert.NoError(t, sel.resolveDns())

	h, _ = sel.Get(xl)
	assert.True(t, "http://***********:321" == h.URL.String() || "http://***********:321" == h.URL.String())

}
