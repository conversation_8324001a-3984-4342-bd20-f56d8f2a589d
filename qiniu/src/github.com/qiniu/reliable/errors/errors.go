package errors

import (
	"errors"
	"syscall"
)

// --------------------------------------------------------------------

var <PERSON>rr<PERSON><PERSON><PERSON><PERSON> = errors.New("bad data")
var Err<PERSON><PERSON>TooLong = errors.New("line too long")
var ErrCrcChecksumError = errors.New("crc checksum error")
var ErrTooManyFails = errors.New("too many fails")
var ErrInvalidArgs = syscall.EINVAL

// --------------------------------------------------------------------
