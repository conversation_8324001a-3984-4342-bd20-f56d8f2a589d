package docutil

import (
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"regexp"
	"runtime"
	"strings"

	"github.com/gorilla/mux"
	"github.com/qiniu/http/webroute.v1"

	"qbox.us/servestk"
)

type IMux interface {
	Handle(pattern string, handler http.Handler)
	HandleFunc(pattern string, handler func(http.ResponseWriter, *http.Request))
	ServeHTTP(w http.ResponseWriter, r *http.Request)
}

var _ servestk.Mux = (IMux)(nil)
var _ webroute.Mux = (IMux)(nil)

type IAPIDumpingMux interface {
	IMux
	DumpRoutes()
}

type apiDumpingMux struct {
	inner mux.Router
}

func NewAPIDumpingMux() IAPIDumpingMux {
	return &apiDumpingMux{
		inner: *mux.NewRouter(),
	}
}

// 许氏框架 adapters

func (m *apiDumpingMux) Handle(pattern string, handler http.Handler) {
	m.inner.Handle(pattern, handler)
}

func (m *apiDumpingMux) HandleFunc(pattern string, handler func(http.ResponseWriter, *http.Request)) {
	m.inner.HandleFunc(pattern, handler)
}

func (m *apiDumpingMux) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	panic("don't need this for doc generation")
}

// API endpoint dumping helper

type goIdent struct {
	Pkg  string `json:"pkg"`
	Name string `json:"name"`
}

type goType struct {
	Ident   goIdent `json:"ident"`
	IsPtr   bool    `json:"is_ptr"`
	IsArray bool    `json:"is_array"`
}

type goMethod struct {
	Receiver *goType `json:"receiver"`
	Ident    goIdent `json:"ident"`
}

type routeDescOutput struct {
	Path       string   `json:"path"`
	Func       goMethod `json:"func"`
	HTTPMethod string   `json:"http_method"`
	ReqFormat  string   `json:"req_format"`
	ReqType    goType   `json:"req_type"`
	RespFormat string   `json:"resp_format"`
	RespType   goType   `json:"resp_type"`
}

func (m *apiDumpingMux) DumpRoutes() {
	var dumpedRouteDescs []routeDescOutput

	// 这边 walkerFn 不会报错，所以这里也不会报错，直接无视 err
	_ = m.inner.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		routeDesc, err := walkerFn(route, router, ancestors)
		if err != nil {
			return err
		}
		var emptyRouteDesc routeDescOutput
		if routeDesc == emptyRouteDesc {
			// 实质上这一条被无视了，直接继续
			return nil
		}
		dumpedRouteDescs = append(dumpedRouteDescs, routeDesc)
		return nil
	})

	// 输出 JSON 序列化的路由描述记录
	serialized, err := json.Marshal(dumpedRouteDescs)
	if err != nil {
		// should never happen on this type
		panic(err)
	}
	fmt.Println(string(serialized))
}

func walkerFn(route *mux.Route, router *mux.Router, ancestors []*mux.Route) (routeDescOutput, error) {
	pathTmpl, err := route.GetPathTemplate()
	if err != nil {
		// 这个 route 不表示路径，对于我们要实现的效果（打印所有注册的路由）而言，可以无视
		return routeDescOutput{}, nil
	}
	handler := route.GetHandler()
	rvHandler := reflect.ValueOf(handler)
	switch rvHandler.Kind() {
	case reflect.Ptr:
		ty := rvHandler.Elem().Type()
		switch {
		case ty.PkgPath() == "github.com/qiniu/http/rpcutil.v1" && ty.Name() == "handler":
			// 没有办法用 reflect 读取私有字段，因此需要和我们特意加入的方法配合，才能拿到最终的 handler 方法
			methFunc := rvHandler.MethodByName("GetMethod").Call(nil)[0].Interface().(reflect.Value)

			return dumpRouteDescForFn(pathTmpl, methFunc), nil

		default:
			// 不认识的许氏框架 handler 类型
			return routeDescOutput{}, nil
		}

	case reflect.Func:
		return dumpRouteDescForFn(pathTmpl, rvHandler), nil
	}

	return routeDescOutput{}, nil
}

func dumpRouteDescForFn(pathTmpl string, handlerFn reflect.Value) routeDescOutput {
	fnType := handlerFn.Type()
	fnFullName := getFuncFullName(handlerFn)
	switch {
	case fnType.PkgPath() == "net/http" && fnType.Name() == "HandlerFunc":
		// generic wrapper not from 许氏框架
		meth, ok := parseMethodNameFromFuncFullName(fnFullName)
		if !ok {
			return routeDescOutput{}
		}

		return routeDescOutput{
			Path:       pathTmpl,
			Func:       meth,
			HTTPMethod: "",
			ReqFormat:  "",
			ReqType:    goType{},
			RespFormat: "",
			RespType:   goType{},
		}
	}

	// 如果函数是来自许氏框架，那么现在 parse 函数签名，拿出请求格式（方法名前缀）、请求类型和响应类型
	fnName := getFuncNameFromFullName(fnFullName)
	if isServestkHandlerFn(fnName) {
		return dumpRouteDescForServestkFn(pathTmpl, fnFullName, fnType)
	}

	// 不认识的其他框架的函数？先不做任何事情
	return routeDescOutput{}
}

var httpMethodsForServestkPrefixes = map[string]string{
	"Ws":  "GET", // XXX 其实也可以是 POST 但一般都不会这么用
	"Wsp": "POST",
	"Rpc": "POST",
}

var reqFormatsForServestkPrefixes = map[string]string{
	"Ws":  "application/x-www-form-urlencoded",
	"Wsp": "application/x-www-form-urlencoded",
	"Rpc": "application/json",
}

func dumpRouteDescForServestkFn(pathTmpl string, fnFullName string, fnType reflect.Type) routeDescOutput {
	meth, ok := parseMethodNameFromFuncFullName(fnFullName)
	if !ok {
		panic("should not happen for servestk handlers")
	}
	prefix := getServestkPrefix(meth.Ident.Name)

	// 许氏框架的函数签名格式：func(<receiver>, 请求类型, <env>) (可选响应类型, error)
	reqType := fnType.In(1)
	var respType reflect.Type
	if fnType.NumOut() == 2 {
		respType = fnType.Out(0)
	}

	reqTypeDesc := goTypeFromReflectType(reqType)

	var respTypeDesc goType
	if respType != nil {
		respTypeDesc = goTypeFromReflectType(respType)
	}

	return routeDescOutput{
		Path:       pathTmpl,
		Func:       meth,
		HTTPMethod: httpMethodsForServestkPrefixes[prefix],
		ReqFormat:  reqFormatsForServestkPrefixes[prefix],
		ReqType:    reqTypeDesc,
		RespFormat: "application/json",
		RespType:   respTypeDesc,
	}
}

// https://stackoverflow.com/questions/7052693/how-to-get-the-name-of-a-function-in-go
//
// output is like "github.com/qbox/pay/walletEx/handle.(*HandleTransactionV4).WsGet"
func getFuncFullName(fn reflect.Value) string {
	return runtime.FuncForPC(fn.Pointer()).Name()
}

func parseTypeName(name string, pkg string) goType {
	if strings.HasPrefix(name, "[]") {
		return goType{
			Ident: goIdent{
				Pkg:  pkg,
				Name: name[2:],
			},
			IsPtr:   false,
			IsArray: true,
		}
	}

	if strings.HasPrefix(name, "*") {
		return goType{
			Ident: goIdent{
				Pkg:  pkg,
				Name: name[1:],
			},
			IsPtr:   true,
			IsArray: false,
		}
	}

	return goType{
		Ident: goIdent{
			Pkg:  pkg,
			Name: name,
		},
		IsPtr:   false,
		IsArray: false,
	}
}

var funcNameRegex = regexp.MustCompile(`^(.*)\.([^.]*)$`)
var methodNameRegex = regexp.MustCompile(`^(.*)\.\(?([^.)]*)\)?\.([^.]*)$`)

func parseBareFuncNameFromFuncFullName(fullName string) (meth goMethod, ok bool) {
	matches := funcNameRegex.FindStringSubmatch(fullName)
	if matches == nil {
		return goMethod{}, false
	}
	return goMethod{
		Receiver: nil,
		Ident: goIdent{
			Pkg:  matches[1],
			Name: matches[2],
		},
	}, true
}

func parseMethodNameFromFuncFullName(fullName string) (meth goMethod, ok bool) {
	matches := methodNameRegex.FindStringSubmatch(fullName)
	if matches == nil {
		// maybe just a bare func
		return parseBareFuncNameFromFuncFullName(fullName)
	}

	receiver := parseTypeName(matches[2], matches[1])
	return goMethod{
		Receiver: &receiver,
		Ident: goIdent{
			Pkg:  receiver.Ident.Pkg,
			Name: matches[3],
		},
	}, true
}

func getFuncNameFromFullName(fullName string) string {
	dotIdx := strings.LastIndex(fullName, ".")
	if dotIdx == -1 {
		return fullName
	}
	return fullName[dotIdx+1:]
}

func isServestkHandlerFn(fnName string) bool {
	switch getServestkPrefix(fnName) {
	case "Ws", "Wsp", "Rpc":
		return true
	default:
		return false
	}
}

func isUpper(x rune) bool {
	return 'A' <= x && x <= 'Z'
}

func getServestkPrefix(fnName string) string {
	// 至少也要符合 WsX 这种格式，因此长度至少为 3
	if len(fnName) < 3 {
		return ""
	}

	for i, ch := range fnName {
		// 首字母必须是大写
		if i == 0 {
			if !isUpper(ch) {
				return ""
			}
			continue
		}

		// i > 0
		// 拉出直到第二个大写字母（不含）为止的前缀
		if isUpper(ch) {
			return fnName[:i]
		}
	}

	return ""
}

func goTypeFromReflectType(ty reflect.Type) goType {
	switch ty.Kind() {
	case reflect.Ptr:
		return goType{
			Ident:   goIdentFromReflectType(ty.Elem()),
			IsPtr:   true,
			IsArray: false,
		}
	case reflect.Slice:
		return goType{
			Ident:   goIdentFromReflectType(ty.Elem()),
			IsPtr:   false,
			IsArray: true,
		}
	default:
		return goType{
			Ident:   goIdentFromReflectType(ty),
			IsPtr:   false,
			IsArray: false,
		}
	}
}

func goIdentFromReflectType(ty reflect.Type) goIdent {
	return goIdent{
		Pkg:  ty.PkgPath(),
		Name: ty.Name(),
	}
}
