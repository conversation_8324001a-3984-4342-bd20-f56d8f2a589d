package seshcookie

import (
	"crypto/sha1"
	"testing"
	"time"
)

func createKey() (key, iv []byte) {
	keySha1 := sha1.New()
	keySha1.Write([]byte(time.Now().UTC().String()))
	keyBytes := keySha1.Sum(nil)
	return keyBytes[:16], keyBytes[4:]
}

func TestCookieEncode(t *testing.T) {
	key, iv := createKey()

	orig := map[string]interface{}{"a": 1, "b": "c", "d": 1.2}

	encoded, err := encodeCookie(orig, key, iv)
	if err != nil {
		t.<PERSON><PERSON>("encodeCookie: %s", err)
		return
	}
	decoded, err := decodeCookie(encoded, key, iv)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("decodeCookie: %s", err)
		return
	}

	if decoded == nil {
		t.<PERSON><PERSON><PERSON>("decoded map is null")
		return
	}

	if len(decoded) != 3 {
		t.<PERSON><PERSON><PERSON>("len was %d, expected 3", len(decoded))
		return
	}

	for k, v := range orig {
		if decoded[k] != v {
			t.<PERSON>("expected decoded[%s] (%#v) == %#v", k,
				decoded[k], v)
		}
	}
}
