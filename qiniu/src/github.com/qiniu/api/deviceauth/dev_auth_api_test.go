package devauthapi

//todo

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/qiniu/xlog.v1"
	"github.com/stretchr/testify/assert"
)

func TestGet(t *testing.T) {
	VAL, _ := json.Marshal(map[string]string{"a": "haha"})
	masterSvr := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		w.Write(VAL)
	}))

	log := xlog.NewWith("TestGet")
	cfg := &Config{
		MasterHosts: []string{masterSvr.URL},
	}
	cli := New(cfg)
	id := "devauthapi.v2.TestGet"

	var ret map[string]string
	err := cli.Get(log, &ret, id)
	assert.NoError(t, err)
	assert.Equal(t, ret, map[string]string{"a": "haha"})
}
