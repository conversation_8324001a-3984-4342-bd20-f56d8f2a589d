GOVERSIONGT15 := $(shell go version | grep version >/dev/null && echo 1 || expr `go version | cut -f3 -d ' '` \>= "go1.5")
ifeq "$(GOVERSIONGT15)" "1"
	LDFLAGS := "-X 'github.com/qiniu/version.version=${BUILD_NUMBER} ${BUILD_ID} ${BUILD_URL}' -X 'github.com/qiniu/version.pkgName=${PACKAGE_NAME}'"
else
	LDFLAGS := "-X github.com/qiniu/version.version '${BUILD_NUMBER} ${BUILD_ID} ${BUILD_URL}' -X github.com/qiniu/version.pkgName '${PACKAGE_NAME}'"
endif

QCHECKSTYLE=go run $(QBOXROOT)/base/qiniu/src/github.com/qiniu/checkstyle/gocheckstyle/gocheckstyle.go -config=.qcodestyle
ifeq ("x${TAGS}","x")
	TAGS='ec28p4'
endif

all:
	cd qiniu; go install -ldflags ${LDFLAGS} -p 4 -v ./...
	cd docs; go install -v ./...
	cd com; go install -ldflags ${LDFLAGS} -p 4 -v ./...
	cd biz; go install -tags ${TAGS} -ldflags ${LDFLAGS} -p 4 -v ./...
	cd cgo; go install -ldflags ${LDFLAGS} -p 4 -v ./...
	cd portal; go install -ldflags ${LDFLAGS} -p 4 -v ./...
	if [ -n "$(MOCKACC)" ]; then cd mockacc; make; fi

ec28p4: TAGS='ec28p4'
ec28p4: all

ec16p4: TAGS='ec16p4'
ec16p4: all

race:
	cd qiniu; go install -ldflags ${LDFLAGS} -p 4 -v -race ./...
	cd com; go install -ldflags ${LDFLAGS} -p 4 -v -race ./...
	cd biz; go install -tags ${TAGS} -ldflags ${LDFLAGS} -p 4 -v -race ./...
	cd cgo; go install -ldflags ${LDFLAGS} -p 4 -v -race ./...
	cd portal; go install -ldflags ${LDFLAGS} -p 4 -v -race ./...
	if [ -n "$(MOCKACC)" ]; then cd mockacc; make; fi

test:
	cd qiniu/src; go test ./...
	cd docs/src; go test ./...
	cd com/src/qbox.us; go test ./...
	cd biz/src/qbox.us; go test -tags ${TAGS} ./...
	cd cgo/src/qbox.us; go test ./...
	cd portal/src/github.com; go test ./...
	if [ -n "$(MOCKACC)" ]; then cd mockacc; make test; fi

install: all
	@echo

clean:
	cd qiniu; go clean -i ./...
	cd docs; go clean -i ./...
	cd com; go clean -i ./...
	cd biz; go clean -i ./...
	cd cgo; go clean -i ./...
	cd portal; go clean -i ./...
	if [ -n "$(MOCKACC)" ]; then cd mockacc; make clean; fi

style:
	@$(QCHECKSTYLE) qiniu/src
	@$(QCHECKSTYLE) com/src
	@$(QCHECKSTYLE) biz/src
	@$(QCHECKSTYLE) cgo/src
	@$(QCHECKSTYLE) mockacc/src
	@$(QCHECKSTYLE) portal/src

ci:
	cd biz; CGO_ENABLED=0 go install -ldflags ${LDFLAGS} -v qbox.us/qmq/app/qboxmmq
	cd biz; CGO_ENABLED=0 go install -ldflags ${LDFLAGS} -v qbox.us/qmq/app/qboxmq
	cd biz; CGO_ENABLED=0 go install -ldflags ${LDFLAGS} -v qbox.us/qconf/app/qboxconfm
	cd biz; CGO_ENABLED=0 go install -ldflags ${LDFLAGS} -v qbox.us/qconf/app/qboxconfs
	cd biz; CGO_ENABLED=0 go install -ldflags ${LDFLAGS} -v qbox.us/qconf/app/qboxconfone
